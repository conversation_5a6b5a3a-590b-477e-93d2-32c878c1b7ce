"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[38],{637:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(5155);r(2115);let l=e=>{let{className:t=""}=e;return(0,a.jsx)("div",{className:"inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ".concat(t),children:(0,a.jsx)("span",{className:"text-white font-bold text-xl",children:"M"})})}},740:(e,t,r)=>{r.d(t,{default:()=>h});var a=r(5155),l=r(2115),s=r(4416),i=r(4783),n=r(637),o=r(4843),d=r(1366);let c=()=>(0,a.jsxs)("button",{onClick:()=>{window.$crisp?window.$crisp.push(["do","chat:open"]):window.location.href="mailto:<EMAIL>?subject=Chat%20Request",window.gtag&&window.gtag("event","chat_opened",{event_category:"engagement",event_label:"header_chat"})},className:"inline-flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200","aria-label":"Open chat",children:[(0,a.jsx)(d.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Chat"})]});var m=r(7847);let h=()=>{let[e,t]=(0,l.useState)(!1),r=e=>{let r=document.getElementById(e);r&&r.scrollIntoView({behavior:"smooth"}),t(!1)},d=[{label:"Services",href:"#services-overview"},{label:"How It Works",href:"#process"},{label:"About Us",href:"#about"}];return(0,a.jsxs)("header",{className:"fixed top-0 left-0 right-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 z-50 transition-colors duration-300",children:[(0,a.jsx)("div",{className:"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 w-full",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.A,{}),(0,a.jsx)("span",{className:"ml-2 text-xl font-bold text-dark-charcoal dark:text-white",children:"Mobilify"})]}),(0,a.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[d.map(e=>(0,a.jsx)("button",{onClick:()=>r(e.href.substring(1)),className:"text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200",children:e.label},e.label)),(0,a.jsx)(o.default,{children:(0,a.jsx)(c,{})}),(0,a.jsx)(o.default,{children:(0,a.jsx)(m.default,{size:"sm",className:"mr-4"})}),(0,a.jsx)("button",{onClick:()=>r("contact"),className:"bg-electric-blue text-white px-6 py-2 rounded-lg hover:opacity-90 transition-all duration-200",children:"Get a Quote"})]}),(0,a.jsx)("button",{onClick:()=>t(!e),className:"md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100",children:e?(0,a.jsx)(s.A,{size:24}):(0,a.jsx)(i.A,{size:24})})]})}),(0,a.jsx)(o.default,{children:e&&(0,a.jsx)("div",{className:"md:hidden fixed inset-0 top-16 bg-white z-40",children:(0,a.jsxs)("div",{className:"px-4 py-6 space-y-4",children:[d.map(e=>(0,a.jsx)("button",{onClick:()=>r(e.href.substring(1)),className:"block w-full text-left text-lg text-gray-600 hover:text-gray-900 py-2",children:e.label},e.label)),(0,a.jsx)("button",{onClick:()=>r("contact"),className:"block w-full bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors duration-200 mt-6",children:"Get a Quote"})]})})})]})}},2323:(e,t,r)=>{r.d(t,{$n:()=>d,Zp:()=>x,Wu:()=>u,pd:()=>m});var a=r(5155),l=r(2115),s=r(2596),i=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,s.$)(t))}let o=l.forwardRef((e,t)=>{let{className:r,variant:l="primary",size:s="md",isLoading:i=!1,disabled:o,children:d,...c}=e,m=n("inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-electric-blue text-white hover:opacity-90 focus:ring-electric-blue shadow-lg hover:shadow-xl",secondary:"border border-electric-blue text-electric-blue hover:bg-electric-blue hover:text-white focus:ring-electric-blue",ghost:"text-electric-blue hover:bg-electric-blue hover:bg-opacity-10 focus:ring-electric-blue"}[l],{sm:"px-4 py-2 text-sm",md:"px-6 py-3 text-base",lg:"px-8 py-4 text-lg"}[s],r);return(0,a.jsx)("button",{ref:t,className:m,disabled:o||i,...c,children:i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading..."]}):d})});o.displayName="Button";let d=o,c=l.forwardRef((e,t)=>{let{className:r,variant:l="base",label:s,helperText:i,errorMessage:o,...d}=e,c=n("w-full px-4 py-3 rounded-lg text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{base:"border border-gray-300 dark:border-gray-600 focus:ring-electric-blue focus:border-electric-blue bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400",error:"border border-red-500 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400",success:"border border-green-500 focus:ring-green-500 focus:border-green-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"}[l],r),m="error"===l&&o,h="error"!==l&&i;return(0,a.jsxs)("div",{className:"w-full",children:[s&&(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:s}),(0,a.jsx)("input",{ref:t,className:c,...d}),m&&(0,a.jsx)("p",{className:"mt-2 text-sm text-red-600 dark:text-red-400",children:o}),h&&(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:i})]})});c.displayName="Input";let m=c,h=l.forwardRef((e,t)=>{let{className:r,variant:l="base",children:s,...i}=e,o=n("bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",{base:"",hover:"hover:shadow-md transition-shadow duration-200",interactive:"hover:shadow-lg cursor-pointer transition-shadow duration-200"}[l],r);return(0,a.jsx)("div",{ref:t,className:o,...i,children:s})});h.displayName="Card",l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)("div",{ref:t,className:n("p-6 pb-0",r),...l})}).displayName="CardHeader";let u=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)("div",{ref:t,className:n("p-6",r),...l})});u.displayName="CardContent",l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,a.jsx)("div",{ref:t,className:n("p-6 pt-0",r),...l})}).displayName="CardFooter";let x=h},4843:(e,t,r)=>{r.r(t),r.d(t,{default:()=>s});var a=r(5155),l=r(2115);let s=e=>{let{children:t,fallback:r=null}=e,[s,i]=(0,l.useState)(!1);return((0,l.useEffect)(()=>{i(!0)},[]),s)?(0,a.jsx)(a.Fragment,{children:t}):(0,a.jsx)(a.Fragment,{children:r})}},6129:(e,t,r)=>{r.r(t),r.d(t,{default:()=>o});var a=r(5155),l=r(2115),s=r(6408),i=r(2323),n=r(9509);let o=e=>{let{variant:t="inline",className:r=""}=e,[o,d]=(0,l.useState)(""),[c,m]=(0,l.useState)(!1),[h,u]=(0,l.useState)("idle"),x=async e=>{if(e.preventDefault(),o.trim()){m(!0),u("idle");try{let e=n.env.NEXT_PUBLIC_MAILCHIMP_API_KEY,r=n.env.MAILCHIMP_LIST_ID;if(!e||!r){await new Promise(e=>setTimeout(e,1e3)),u("success"),d(""),window.gtag&&window.gtag("event","newsletter_signup",{event_category:"engagement",event_label:t});return}(await fetch("/api/newsletter",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:o,source:t})})).ok?(u("success"),d(""),window.gtag&&window.gtag("event","newsletter_signup",{event_category:"engagement",event_label:t})):u("error")}catch(e){u("error")}finally{m(!1)}}};return(0,a.jsxs)("div",{className:"".concat(r),children:[("inline"===t||"section"===t)&&(0,a.jsx)(s.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"py-16 md:py-20 bg-electric-blue",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"Stay Updated on Mobile Innovation"}),(0,a.jsx)("p",{className:"text-lg md:text-xl leading-relaxed text-blue-100 max-w-3xl mx-auto mb-8",children:"Get insights on mobile app development, industry trends, and exclusive tips delivered to your inbox."}),(0,a.jsxs)("form",{onSubmit:x,className:"max-w-md mx-auto",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)(i.pd,{type:"email",value:o,onChange:e=>d(e.target.value),placeholder:"Enter your email address",required:!0,className:"flex-1 bg-white border-white focus:ring-white focus:border-white",disabled:c}),(0,a.jsx)(i.$n,{type:"submit",variant:"secondary",isLoading:c,disabled:!o.trim()||c,className:"bg-white text-electric-blue hover:bg-gray-50 border-white",children:"Subscribe"})]}),"success"===h&&(0,a.jsx)(s.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4 text-green-100 text-sm",children:"✓ Successfully subscribed! Check your email for confirmation."}),"error"===h&&(0,a.jsx)(s.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4 text-red-100 text-sm",children:"✗ Something went wrong. Please try again."})]})]})}),"footer"===t&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4 text-white",children:"Stay Connected"}),(0,a.jsx)("p",{className:"text-sm text-gray-400 dark:text-gray-300 mb-4 leading-relaxed",children:"Get the latest updates on mobile app development and industry insights."}),(0,a.jsxs)("form",{onSubmit:x,className:"space-y-4",children:[(0,a.jsx)(i.pd,{type:"email",value:o,onChange:e=>d(e.target.value),placeholder:"Enter your email",required:!0,className:"bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:ring-electric-blue focus:border-electric-blue",disabled:c}),(0,a.jsx)(i.$n,{type:"submit",variant:"primary",size:"sm",isLoading:c,disabled:!o.trim()||c,className:"w-full",children:"Subscribe"}),"success"===h&&(0,a.jsx)(s.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-green-400 text-sm",children:"✓ Successfully subscribed!"}),"error"===h&&(0,a.jsx)(s.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-red-400 text-sm",children:"✗ Please try again."})]})]})]})}},7740:(e,t,r)=>{r.d(t,{DP:()=>i,ThemeProvider:()=>n});var a=r(5155),l=r(2115);let s=(0,l.createContext)(void 0),i=()=>{let e=(0,l.useContext)(s);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e},n=e=>{let{children:t}=e,[r,i]=(0,l.useState)("light"),[n,o]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{let e=localStorage.getItem("mobilify-theme"),t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";i(e||t),o(!0)},[]),(0,l.useEffect)(()=>{if(!n)return;let e=document.documentElement;"dark"===r?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("mobilify-theme",r),window.gtag&&window.gtag("event","theme_changed",{event_category:"user_preference",event_label:r})},[r,n]),(0,l.useEffect)(()=>{if(!n)return;let e=window.matchMedia("(prefers-color-scheme: dark)"),t=e=>{localStorage.getItem("mobilify-theme")||i(e.matches?"dark":"light")};return e.addEventListener("change",t),()=>e.removeEventListener("change",t)},[n]),(0,a.jsx)(s.Provider,{value:{theme:r,toggleTheme:()=>{i(e=>"light"===e?"dark":"light")},setTheme:e=>{i(e)}},children:(0,a.jsx)("div",{suppressHydrationWarning:!0,children:t})})}},7847:(e,t,r)=>{r.d(t,{default:()=>n});var a=r(5155);r(2115);var l=r(2098),s=r(3509),i=r(7740);let n=e=>{let{className:t="",size:r="md"}=e,{theme:n,toggleTheme:o}=(0,i.DP)();return(0,a.jsx)("button",{onClick:o,className:"".concat((()=>{switch(r){case"sm":return"w-8 h-8 text-sm";case"lg":return"w-12 h-12 text-lg";default:return"w-10 h-10 text-base"}})()," flex items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 ").concat(t),"aria-label":"Switch to ".concat("dark"===n?"light":"dark"," mode"),children:"dark"===n?(0,a.jsx)(l.A,{className:"w-5 h-5"}):(0,a.jsx)(s.A,{className:"w-5 h-5"})})}}}]);