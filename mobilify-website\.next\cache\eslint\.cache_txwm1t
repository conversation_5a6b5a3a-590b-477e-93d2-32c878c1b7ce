[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\api\\newsletter\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\page.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\FAQClient.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\page.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\robots.ts": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\services\\page.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\sitemap.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\AboutSnippet.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\AnimationWrapper.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ChatTrigger.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ClientOnly.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CompanyValues.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Contact.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ContactForm.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CrispChat.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\DarkModeToggle.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Footer.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\GoogleAnalytics.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Header.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Hero.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\InteractiveDemo.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Logo.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Mission.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NewsletterSignup.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NoSSR.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\OrganizationSchema.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PortableText.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PricingTable.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Process.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ServicesFAQ.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ServicesOverview.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleDarkModeToggle.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleFloatingChat.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleHeaderChat.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\StructuredData.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\TeamProfiles.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Button.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Card.tsx": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\index.ts": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Input.tsx": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Button.test.tsx": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Input.test.tsx": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\WebVitals.tsx": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Contact.test.tsx": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Header.test.tsx": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\InteractiveDemo.test.tsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\NewsletterSignup.test.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\config\\site.ts": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\index.ts": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useAnalytics.ts": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useContactForm.ts": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useSiteSettings.ts": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\metadata.ts": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\sanity.ts": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\utils.ts": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\simple.test.js": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\test-utils.tsx": "62"}, {"size": 2049, "mtime": 1751664360882, "results": "63", "hashOfConfig": "64"}, {"size": 3626, "mtime": 1751505504335, "results": "65", "hashOfConfig": "64"}, {"size": 10192, "mtime": 1751514851839, "results": "66", "hashOfConfig": "64"}, {"size": 9641, "mtime": 1751578712907, "results": "67", "hashOfConfig": "64"}, {"size": 12209, "mtime": 1751512639671, "results": "68", "hashOfConfig": "64"}, {"size": 5797, "mtime": 1751577101014, "results": "69", "hashOfConfig": "64"}, {"size": 3590, "mtime": 1751664413787, "results": "70", "hashOfConfig": "64"}, {"size": 1854, "mtime": 1751664625181, "results": "71", "hashOfConfig": "64"}, {"size": 415, "mtime": 1751664165349, "results": "72", "hashOfConfig": "64"}, {"size": 2076, "mtime": 1751664335916, "results": "73", "hashOfConfig": "64"}, {"size": 1476, "mtime": 1751664119979, "results": "74", "hashOfConfig": "64"}, {"size": 2442, "mtime": 1751578213824, "results": "75", "hashOfConfig": "64"}, {"size": 641, "mtime": 1750993207566, "results": "76", "hashOfConfig": "64"}, {"size": 4645, "mtime": 1751511180410, "results": "77", "hashOfConfig": "64"}, {"size": 447, "mtime": 1750993163336, "results": "78", "hashOfConfig": "64"}, {"size": 3968, "mtime": 1750990727135, "results": "79", "hashOfConfig": "64"}, {"size": 1851, "mtime": 1751670717377, "results": "80", "hashOfConfig": "64"}, {"size": 8619, "mtime": 1751670631570, "results": "81", "hashOfConfig": "64"}, {"size": 5197, "mtime": 1751574977693, "results": "82", "hashOfConfig": "64"}, {"size": 4806, "mtime": 1751511499987, "results": "83", "hashOfConfig": "64"}, {"size": 3355, "mtime": 1751578373600, "results": "84", "hashOfConfig": "64"}, {"size": 859, "mtime": 1751574932472, "results": "85", "hashOfConfig": "64"}, {"size": 3586, "mtime": 1751670566032, "results": "86", "hashOfConfig": "64"}, {"size": 4130, "mtime": 1751670521401, "results": "87", "hashOfConfig": "64"}, {"size": 10533, "mtime": 1751670843324, "results": "88", "hashOfConfig": "64"}, {"size": 315, "mtime": 1750990215277, "results": "89", "hashOfConfig": "64"}, {"size": 1703, "mtime": 1750990673093, "results": "90", "hashOfConfig": "64"}, {"size": 6904, "mtime": 1751661914782, "results": "91", "hashOfConfig": "64"}, {"size": 415, "mtime": 1750998195180, "results": "92", "hashOfConfig": "64"}, {"size": 2390, "mtime": 1751578724741, "results": "93", "hashOfConfig": "64"}, {"size": 6772, "mtime": 1751514986018, "results": "94", "hashOfConfig": "64"}, {"size": 7652, "mtime": 1750990602546, "results": "95", "hashOfConfig": "64"}, {"size": 4836, "mtime": 1751661352236, "results": "96", "hashOfConfig": "64"}, {"size": 6361, "mtime": 1750990637857, "results": "97", "hashOfConfig": "64"}, {"size": 4513, "mtime": 1751671722640, "results": "98", "hashOfConfig": "64"}, {"size": 1149, "mtime": 1751518090332, "results": "99", "hashOfConfig": "64"}, {"size": 1498, "mtime": 1751518229057, "results": "100", "hashOfConfig": "64"}, {"size": 1145, "mtime": 1751518286486, "results": "101", "hashOfConfig": "64"}, {"size": 4809, "mtime": 1751664389950, "results": "102", "hashOfConfig": "64"}, {"size": 4353, "mtime": 1751578594187, "results": "103", "hashOfConfig": "64"}, {"size": 2356, "mtime": 1751664777139, "results": "104", "hashOfConfig": "64"}, {"size": 1834, "mtime": 1751575325593, "results": "105", "hashOfConfig": "64"}, {"size": 300, "mtime": 1751575364754, "results": "106", "hashOfConfig": "64"}, {"size": 2176, "mtime": 1751575251624, "results": "107", "hashOfConfig": "64"}, {"size": 8324, "mtime": 1751662637625, "results": "108", "hashOfConfig": "64"}, {"size": 9276, "mtime": 1751662678560, "results": "109", "hashOfConfig": "64"}, {"size": 2650, "mtime": 1751664824565, "results": "110", "hashOfConfig": "64"}, {"size": 8698, "mtime": 1751662431191, "results": "111", "hashOfConfig": "64"}, {"size": 9021, "mtime": 1751662773289, "results": "112", "hashOfConfig": "64"}, {"size": 10379, "mtime": 1751662507680, "results": "113", "hashOfConfig": "64"}, {"size": 9645, "mtime": 1751662467457, "results": "114", "hashOfConfig": "64"}, {"size": 6036, "mtime": 1751670335025, "results": "115", "hashOfConfig": "64"}, {"size": 3477, "mtime": 1751515726939, "results": "116", "hashOfConfig": "64"}, {"size": 551, "mtime": 1751670473668, "results": "117", "hashOfConfig": "64"}, {"size": 5333, "mtime": 1751671766740, "results": "118", "hashOfConfig": "64"}, {"size": 6690, "mtime": 1751671807702, "results": "119", "hashOfConfig": "64"}, {"size": 5933, "mtime": 1751671826897, "results": "120", "hashOfConfig": "64"}, {"size": 5442, "mtime": 1751578759717, "results": "121", "hashOfConfig": "64"}, {"size": 6546, "mtime": 1751670381294, "results": "122", "hashOfConfig": "64"}, {"size": 169, "mtime": 1751574258441, "results": "123", "hashOfConfig": "64"}, {"size": 232, "mtime": 1751663733726, "results": "124", "hashOfConfig": "64"}, {"size": 5645, "mtime": 1751662735678, "results": "125", "hashOfConfig": "64"}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "ipl2r0", {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\about\\page.tsx", ["312"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\api\\newsletter\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\page.tsx", ["313", "314", "315"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx", ["316"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\FAQClient.tsx", ["317"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\services\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\sitemap.ts", ["318"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\AboutSnippet.tsx", ["319", "320", "321"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\AnimationWrapper.tsx", ["322"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ChatTrigger.tsx", ["323", "324"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ClientOnly.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CompanyValues.tsx", ["325", "326", "327"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Contact.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ContactForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CrispChat.tsx", ["328", "329", "330", "331", "332", "333", "334", "335"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\DarkModeToggle.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Footer.tsx", ["336", "337", "338", "339"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\GoogleAnalytics.tsx", ["340", "341"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Hero.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\InteractiveDemo.tsx", ["342"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Logo.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Mission.tsx", ["343", "344", "345"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NewsletterSignup.tsx", ["346", "347", "348", "349", "350", "351"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NoSSR.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\OrganizationSchema.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PortableText.tsx", ["352", "353", "354"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PricingTable.tsx", ["355"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Process.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ServicesFAQ.tsx", ["356", "357"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ServicesOverview.tsx", ["358", "359"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleDarkModeToggle.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleFloatingChat.tsx", ["360", "361", "362", "363"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleHeaderChat.tsx", ["364", "365", "366", "367"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\StructuredData.tsx", ["368", "369", "370"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\TeamProfiles.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Button.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Input.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\WebVitals.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Contact.test.tsx", ["371"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Header.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\InteractiveDemo.test.tsx", ["372"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\NewsletterSignup.test.tsx", ["373"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\config\\site.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx", ["374", "375"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useAnalytics.ts", ["376", "377", "378"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useContactForm.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useSiteSettings.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\metadata.ts", ["379"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\sanity.ts", ["380", "381", "382"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\simple.test.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\test-utils.tsx", ["383", "384", "385"], [], {"ruleId": "386", "severity": 2, "message": "387", "line": 46, "column": 17, "nodeType": "388", "messageId": "389", "suggestions": "390"}, {"ruleId": "391", "severity": 2, "message": "392", "line": 3, "column": 8, "nodeType": null, "messageId": "393", "endLine": 3, "endColumn": 13}, {"ruleId": "391", "severity": 2, "message": "394", "line": 5, "column": 10, "nodeType": null, "messageId": "393", "endLine": 5, "endColumn": 16}, {"ruleId": "391", "severity": 2, "message": "395", "line": 187, "column": 43, "nodeType": null, "messageId": "393", "endLine": 187, "endColumn": 48}, {"ruleId": "396", "severity": 2, "message": "397", "line": 78, "column": 23, "nodeType": "398", "messageId": "399", "endLine": 78, "endColumn": 26, "suggestions": "400"}, {"ruleId": "386", "severity": 2, "message": "387", "line": 259, "column": 19, "nodeType": "388", "messageId": "389", "suggestions": "401"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 10, "column": 16, "nodeType": "398", "messageId": "399", "endLine": 10, "endColumn": 19, "suggestions": "402"}, {"ruleId": "386", "severity": 2, "message": "387", "line": 23, "column": 19, "nodeType": "388", "messageId": "389", "suggestions": "403"}, {"ruleId": "386", "severity": 2, "message": "387", "line": 29, "column": 21, "nodeType": "388", "messageId": "389", "suggestions": "404"}, {"ruleId": "386", "severity": 2, "message": "387", "line": 34, "column": 68, "nodeType": "388", "messageId": "389", "suggestions": "405"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 19, "column": 37, "nodeType": "398", "messageId": "399", "endLine": 19, "endColumn": 40, "suggestions": "406"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 37, "column": 53, "nodeType": "398", "messageId": "399", "endLine": 37, "endColumn": 56, "suggestions": "407"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 38, "column": 18, "nodeType": "398", "messageId": "399", "endLine": 38, "endColumn": 21, "suggestions": "408"}, {"ruleId": "386", "severity": 2, "message": "387", "line": 86, "column": 17, "nodeType": "388", "messageId": "389", "suggestions": "409"}, {"ruleId": "386", "severity": 2, "message": "387", "line": 87, "column": 18, "nodeType": "388", "messageId": "389", "suggestions": "410"}, {"ruleId": "411", "severity": 2, "message": "412", "line": 89, "column": 13, "nodeType": "413", "endLine": 92, "endColumn": 14}, {"ruleId": "396", "severity": 2, "message": "397", "line": 8, "column": 13, "nodeType": "398", "messageId": "399", "endLine": 8, "endColumn": 16, "suggestions": "414"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 43, "column": 59, "nodeType": "398", "messageId": "399", "endLine": 43, "endColumn": 62, "suggestions": "415"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 44, "column": 24, "nodeType": "398", "messageId": "399", "endLine": 44, "endColumn": 27, "suggestions": "416"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 52, "column": 59, "nodeType": "398", "messageId": "399", "endLine": 52, "endColumn": 62, "suggestions": "417"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 53, "column": 24, "nodeType": "398", "messageId": "399", "endLine": 53, "endColumn": 27, "suggestions": "418"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 61, "column": 59, "nodeType": "398", "messageId": "399", "endLine": 61, "endColumn": 62, "suggestions": "419"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 62, "column": 24, "nodeType": "398", "messageId": "399", "endLine": 62, "endColumn": 27, "suggestions": "420"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 162, "column": 41, "nodeType": "398", "messageId": "399", "endLine": 162, "endColumn": 44, "suggestions": "421"}, {"ruleId": "391", "severity": 2, "message": "422", "line": 6, "column": 10, "nodeType": null, "messageId": "393", "endLine": 6, "endColumn": 14}, {"ruleId": "391", "severity": 2, "message": "423", "line": 6, "column": 16, "nodeType": null, "messageId": "393", "endLine": 6, "endColumn": 27}, {"ruleId": "411", "severity": 2, "message": "412", "line": 30, "column": 17, "nodeType": "413", "endLine": 30, "endColumn": 135}, {"ruleId": "411", "severity": 2, "message": "412", "line": 45, "column": 17, "nodeType": "413", "endLine": 45, "endColumn": 138}, {"ruleId": "396", "severity": 2, "message": "397", "line": 7, "column": 21, "nodeType": "398", "messageId": "399", "endLine": 7, "endColumn": 24, "suggestions": "424"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 8, "column": 16, "nodeType": "398", "messageId": "399", "endLine": 8, "endColumn": 19, "suggestions": "425"}, {"ruleId": "391", "severity": 2, "message": "426", "line": 6, "column": 10, "nodeType": null, "messageId": "393", "endLine": 6, "endColumn": 26}, {"ruleId": "386", "severity": 2, "message": "387", "line": 29, "column": 49, "nodeType": "388", "messageId": "389", "suggestions": "427"}, {"ruleId": "386", "severity": 2, "message": "387", "line": 36, "column": 82, "nodeType": "388", "messageId": "389", "suggestions": "428"}, {"ruleId": "386", "severity": 2, "message": "387", "line": 37, "column": 37, "nodeType": "388", "messageId": "389", "suggestions": "429"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 40, "column": 57, "nodeType": "398", "messageId": "399", "endLine": 40, "endColumn": 60, "suggestions": "430"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 41, "column": 22, "nodeType": "398", "messageId": "399", "endLine": 41, "endColumn": 25, "suggestions": "431"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 67, "column": 57, "nodeType": "398", "messageId": "399", "endLine": 67, "endColumn": 60, "suggestions": "432"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 68, "column": 22, "nodeType": "398", "messageId": "399", "endLine": 68, "endColumn": 25, "suggestions": "433"}, {"ruleId": "391", "severity": 2, "message": "434", "line": 84, "column": 9, "nodeType": null, "messageId": "393", "endLine": 84, "endColumn": 24}, {"ruleId": "391", "severity": 2, "message": "435", "line": 85, "column": 9, "nodeType": null, "messageId": "393", "endLine": 85, "endColumn": 25}, {"ruleId": "391", "severity": 2, "message": "392", "line": 3, "column": 8, "nodeType": null, "messageId": "393", "endLine": 3, "endColumn": 13}, {"ruleId": "391", "severity": 2, "message": "436", "line": 4, "column": 10, "nodeType": null, "messageId": "393", "endLine": 4, "endColumn": 16}, {"ruleId": "437", "severity": 2, "message": "438", "line": 72, "column": 11, "nodeType": "439", "messageId": "440", "endLine": 72, "endColumn": 15, "fix": "441"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 92, "column": 38, "nodeType": "398", "messageId": "399", "endLine": 92, "endColumn": 41, "suggestions": "442"}, {"ruleId": "386", "severity": 2, "message": "387", "line": 116, "column": 37, "nodeType": "388", "messageId": "389", "suggestions": "443"}, {"ruleId": "411", "severity": 2, "message": "412", "line": 118, "column": 11, "nodeType": "413", "endLine": 121, "endColumn": 12}, {"ruleId": "396", "severity": 2, "message": "397", "line": 30, "column": 53, "nodeType": "398", "messageId": "399", "endLine": 30, "endColumn": 56, "suggestions": "444"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 31, "column": 18, "nodeType": "398", "messageId": "399", "endLine": 31, "endColumn": 21, "suggestions": "445"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 9, "column": 53, "nodeType": "398", "messageId": "399", "endLine": 9, "endColumn": 56, "suggestions": "446"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 10, "column": 18, "nodeType": "398", "messageId": "399", "endLine": 10, "endColumn": 21, "suggestions": "447"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 17, "column": 53, "nodeType": "398", "messageId": "399", "endLine": 17, "endColumn": 56, "suggestions": "448"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 18, "column": 18, "nodeType": "398", "messageId": "399", "endLine": 18, "endColumn": 21, "suggestions": "449"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 9, "column": 53, "nodeType": "398", "messageId": "399", "endLine": 9, "endColumn": 56, "suggestions": "450"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 10, "column": 18, "nodeType": "398", "messageId": "399", "endLine": 10, "endColumn": 21, "suggestions": "451"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 17, "column": 53, "nodeType": "398", "messageId": "399", "endLine": 17, "endColumn": 56, "suggestions": "452"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 18, "column": 18, "nodeType": "398", "messageId": "399", "endLine": 18, "endColumn": 21, "suggestions": "453"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 5, "column": 10, "nodeType": "398", "messageId": "399", "endLine": 5, "endColumn": 13, "suggestions": "454"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 89, "column": 47, "nodeType": "398", "messageId": "399", "endLine": 89, "endColumn": 50, "suggestions": "455"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 103, "column": 60, "nodeType": "398", "messageId": "399", "endLine": 103, "endColumn": 63, "suggestions": "456"}, {"ruleId": "391", "severity": 2, "message": "457", "line": 2, "column": 26, "nodeType": null, "messageId": "393", "endLine": 2, "endColumn": 35}, {"ruleId": "391", "severity": 2, "message": "457", "line": 2, "column": 26, "nodeType": null, "messageId": "393", "endLine": 2, "endColumn": 35}, {"ruleId": "391", "severity": 2, "message": "457", "line": 2, "column": 26, "nodeType": null, "messageId": "393", "endLine": 2, "endColumn": 35}, {"ruleId": "396", "severity": 2, "message": "397", "line": 57, "column": 53, "nodeType": "398", "messageId": "399", "endLine": 57, "endColumn": 56, "suggestions": "458"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 58, "column": 18, "nodeType": "398", "messageId": "399", "endLine": 58, "endColumn": 21, "suggestions": "459"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 17, "column": 38, "nodeType": "398", "messageId": "399", "endLine": 17, "endColumn": 41, "suggestions": "460"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 75, "column": 53, "nodeType": "398", "messageId": "399", "endLine": 75, "endColumn": 56, "suggestions": "461"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 77, "column": 20, "nodeType": "398", "messageId": "399", "endLine": 77, "endColumn": 23, "suggestions": "462"}, {"ruleId": "391", "severity": 2, "message": "463", "line": 43, "column": 9, "nodeType": null, "messageId": "393", "endLine": 43, "endColumn": 26}, {"ruleId": "396", "severity": 2, "message": "397", "line": 22, "column": 32, "nodeType": "398", "messageId": "399", "endLine": 22, "endColumn": 35, "suggestions": "464"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 62, "column": 9, "nodeType": "398", "messageId": "399", "endLine": 62, "endColumn": 12, "suggestions": "465"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 109, "column": 11, "nodeType": "398", "messageId": "399", "endLine": 109, "endColumn": 14, "suggestions": "466"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 59, "column": 37, "nodeType": "398", "messageId": "399", "endLine": 59, "endColumn": 40, "suggestions": "467"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 160, "column": 66, "nodeType": "398", "messageId": "399", "endLine": 160, "endColumn": 69, "suggestions": "468"}, {"ruleId": "396", "severity": 2, "message": "397", "line": 177, "column": 75, "nodeType": "398", "messageId": "399", "endLine": 177, "endColumn": 78, "suggestions": "469"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["470", "471", "472", "473"], "@typescript-eslint/no-unused-vars", "'Image' is defined but never used.", "unusedVar", "'motion' is defined but never used.", "'index' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["474", "475"], ["476", "477", "478", "479"], ["480", "481"], ["482", "483", "484", "485"], ["486", "487", "488", "489"], ["490", "491", "492", "493"], ["494", "495"], ["496", "497"], ["498", "499"], ["500", "501", "502", "503"], ["504", "505", "506", "507"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", ["508", "509"], ["510", "511"], ["512", "513"], ["514", "515"], ["516", "517"], ["518", "519"], ["520", "521"], ["522", "523"], "'Card' is defined but never used.", "'CardContent' is defined but never used.", ["524", "525"], ["526", "527"], "'ANIMATION_CONFIG' is defined but never used.", ["528", "529", "530", "531"], ["532", "533", "534", "535"], ["536", "537", "538", "539"], ["540", "541"], ["542", "543"], ["544", "545"], ["546", "547"], "'isFooterVariant' is assigned a value but never used.", "'isSectionVariant' is assigned a value but never used.", "'urlFor' is defined but never used.", "prefer-const", "'text' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "548", "text": "549"}, ["550", "551"], ["552", "553", "554", "555"], ["556", "557"], ["558", "559"], ["560", "561"], ["562", "563"], ["564", "565"], ["566", "567"], ["568", "569"], ["570", "571"], ["572", "573"], ["574", "575"], ["576", "577"], ["578", "579"], ["580", "581"], "'fireEvent' is defined but never used.", ["582", "583"], ["584", "585"], ["586", "587"], ["588", "589"], ["590", "591"], "'keywordsToInclude' is assigned a value but never used.", ["592", "593"], ["594", "595"], ["596", "597"], ["598", "599"], ["600", "601"], ["602", "603"], {"messageId": "604", "data": "605", "fix": "606", "desc": "607"}, {"messageId": "604", "data": "608", "fix": "609", "desc": "610"}, {"messageId": "604", "data": "611", "fix": "612", "desc": "613"}, {"messageId": "604", "data": "614", "fix": "615", "desc": "616"}, {"messageId": "617", "fix": "618", "desc": "619"}, {"messageId": "620", "fix": "621", "desc": "622"}, {"messageId": "604", "data": "623", "fix": "624", "desc": "607"}, {"messageId": "604", "data": "625", "fix": "626", "desc": "610"}, {"messageId": "604", "data": "627", "fix": "628", "desc": "613"}, {"messageId": "604", "data": "629", "fix": "630", "desc": "616"}, {"messageId": "617", "fix": "631", "desc": "619"}, {"messageId": "620", "fix": "632", "desc": "622"}, {"messageId": "604", "data": "633", "fix": "634", "desc": "607"}, {"messageId": "604", "data": "635", "fix": "636", "desc": "610"}, {"messageId": "604", "data": "637", "fix": "638", "desc": "613"}, {"messageId": "604", "data": "639", "fix": "640", "desc": "616"}, {"messageId": "604", "data": "641", "fix": "642", "desc": "607"}, {"messageId": "604", "data": "643", "fix": "644", "desc": "610"}, {"messageId": "604", "data": "645", "fix": "646", "desc": "613"}, {"messageId": "604", "data": "647", "fix": "648", "desc": "616"}, {"messageId": "604", "data": "649", "fix": "650", "desc": "607"}, {"messageId": "604", "data": "651", "fix": "652", "desc": "610"}, {"messageId": "604", "data": "653", "fix": "654", "desc": "613"}, {"messageId": "604", "data": "655", "fix": "656", "desc": "616"}, {"messageId": "617", "fix": "657", "desc": "619"}, {"messageId": "620", "fix": "658", "desc": "622"}, {"messageId": "617", "fix": "659", "desc": "619"}, {"messageId": "620", "fix": "660", "desc": "622"}, {"messageId": "617", "fix": "661", "desc": "619"}, {"messageId": "620", "fix": "662", "desc": "622"}, {"messageId": "604", "data": "663", "fix": "664", "desc": "607"}, {"messageId": "604", "data": "665", "fix": "666", "desc": "610"}, {"messageId": "604", "data": "667", "fix": "668", "desc": "613"}, {"messageId": "604", "data": "669", "fix": "670", "desc": "616"}, {"messageId": "604", "data": "671", "fix": "672", "desc": "607"}, {"messageId": "604", "data": "673", "fix": "674", "desc": "610"}, {"messageId": "604", "data": "675", "fix": "676", "desc": "613"}, {"messageId": "604", "data": "677", "fix": "678", "desc": "616"}, {"messageId": "617", "fix": "679", "desc": "619"}, {"messageId": "620", "fix": "680", "desc": "622"}, {"messageId": "617", "fix": "681", "desc": "619"}, {"messageId": "620", "fix": "682", "desc": "622"}, {"messageId": "617", "fix": "683", "desc": "619"}, {"messageId": "620", "fix": "684", "desc": "622"}, {"messageId": "617", "fix": "685", "desc": "619"}, {"messageId": "620", "fix": "686", "desc": "622"}, {"messageId": "617", "fix": "687", "desc": "619"}, {"messageId": "620", "fix": "688", "desc": "622"}, {"messageId": "617", "fix": "689", "desc": "619"}, {"messageId": "620", "fix": "690", "desc": "622"}, {"messageId": "617", "fix": "691", "desc": "619"}, {"messageId": "620", "fix": "692", "desc": "622"}, {"messageId": "617", "fix": "693", "desc": "619"}, {"messageId": "620", "fix": "694", "desc": "622"}, {"messageId": "617", "fix": "695", "desc": "619"}, {"messageId": "620", "fix": "696", "desc": "622"}, {"messageId": "617", "fix": "697", "desc": "619"}, {"messageId": "620", "fix": "698", "desc": "622"}, {"messageId": "604", "data": "699", "fix": "700", "desc": "607"}, {"messageId": "604", "data": "701", "fix": "702", "desc": "610"}, {"messageId": "604", "data": "703", "fix": "704", "desc": "613"}, {"messageId": "604", "data": "705", "fix": "706", "desc": "616"}, {"messageId": "604", "data": "707", "fix": "708", "desc": "607"}, {"messageId": "604", "data": "709", "fix": "710", "desc": "610"}, {"messageId": "604", "data": "711", "fix": "712", "desc": "613"}, {"messageId": "604", "data": "713", "fix": "714", "desc": "616"}, {"messageId": "604", "data": "715", "fix": "716", "desc": "607"}, {"messageId": "604", "data": "717", "fix": "718", "desc": "610"}, {"messageId": "604", "data": "719", "fix": "720", "desc": "613"}, {"messageId": "604", "data": "721", "fix": "722", "desc": "616"}, {"messageId": "617", "fix": "723", "desc": "619"}, {"messageId": "620", "fix": "724", "desc": "622"}, {"messageId": "617", "fix": "725", "desc": "619"}, {"messageId": "620", "fix": "726", "desc": "622"}, {"messageId": "617", "fix": "727", "desc": "619"}, {"messageId": "620", "fix": "728", "desc": "622"}, {"messageId": "617", "fix": "729", "desc": "619"}, {"messageId": "620", "fix": "730", "desc": "622"}, [1800, 1821], "const text = span.text;", {"messageId": "617", "fix": "731", "desc": "619"}, {"messageId": "620", "fix": "732", "desc": "622"}, {"messageId": "604", "data": "733", "fix": "734", "desc": "607"}, {"messageId": "604", "data": "735", "fix": "736", "desc": "610"}, {"messageId": "604", "data": "737", "fix": "738", "desc": "613"}, {"messageId": "604", "data": "739", "fix": "740", "desc": "616"}, {"messageId": "617", "fix": "741", "desc": "619"}, {"messageId": "620", "fix": "742", "desc": "622"}, {"messageId": "617", "fix": "743", "desc": "619"}, {"messageId": "620", "fix": "744", "desc": "622"}, {"messageId": "617", "fix": "745", "desc": "619"}, {"messageId": "620", "fix": "746", "desc": "622"}, {"messageId": "617", "fix": "747", "desc": "619"}, {"messageId": "620", "fix": "748", "desc": "622"}, {"messageId": "617", "fix": "749", "desc": "619"}, {"messageId": "620", "fix": "750", "desc": "622"}, {"messageId": "617", "fix": "751", "desc": "619"}, {"messageId": "620", "fix": "752", "desc": "622"}, {"messageId": "617", "fix": "753", "desc": "619"}, {"messageId": "620", "fix": "754", "desc": "622"}, {"messageId": "617", "fix": "755", "desc": "619"}, {"messageId": "620", "fix": "756", "desc": "622"}, {"messageId": "617", "fix": "757", "desc": "619"}, {"messageId": "620", "fix": "758", "desc": "622"}, {"messageId": "617", "fix": "759", "desc": "619"}, {"messageId": "620", "fix": "760", "desc": "622"}, {"messageId": "617", "fix": "761", "desc": "619"}, {"messageId": "620", "fix": "762", "desc": "622"}, {"messageId": "617", "fix": "763", "desc": "619"}, {"messageId": "620", "fix": "764", "desc": "622"}, {"messageId": "617", "fix": "765", "desc": "619"}, {"messageId": "620", "fix": "766", "desc": "622"}, {"messageId": "617", "fix": "767", "desc": "619"}, {"messageId": "620", "fix": "768", "desc": "622"}, {"messageId": "617", "fix": "769", "desc": "619"}, {"messageId": "620", "fix": "770", "desc": "622"}, {"messageId": "617", "fix": "771", "desc": "619"}, {"messageId": "620", "fix": "772", "desc": "622"}, {"messageId": "617", "fix": "773", "desc": "619"}, {"messageId": "620", "fix": "774", "desc": "622"}, {"messageId": "617", "fix": "775", "desc": "619"}, {"messageId": "620", "fix": "776", "desc": "622"}, {"messageId": "617", "fix": "777", "desc": "619"}, {"messageId": "620", "fix": "778", "desc": "622"}, {"messageId": "617", "fix": "779", "desc": "619"}, {"messageId": "620", "fix": "780", "desc": "622"}, {"messageId": "617", "fix": "781", "desc": "619"}, {"messageId": "620", "fix": "782", "desc": "622"}, {"messageId": "617", "fix": "783", "desc": "619"}, {"messageId": "620", "fix": "784", "desc": "622"}, {"messageId": "617", "fix": "785", "desc": "619"}, {"messageId": "620", "fix": "786", "desc": "622"}, {"messageId": "617", "fix": "787", "desc": "619"}, {"messageId": "620", "fix": "788", "desc": "622"}, "replaceWithAlt", {"alt": "789"}, {"range": "790", "text": "791"}, "Replace with `&apos;`.", {"alt": "792"}, {"range": "793", "text": "794"}, "Replace with `&lsquo;`.", {"alt": "795"}, {"range": "796", "text": "797"}, "Replace with `&#39;`.", {"alt": "798"}, {"range": "799", "text": "800"}, "Replace with `&rsquo;`.", "suggestUnknown", {"range": "801", "text": "802"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "803", "text": "804"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"alt": "789"}, {"range": "805", "text": "806"}, {"alt": "792"}, {"range": "807", "text": "808"}, {"alt": "795"}, {"range": "809", "text": "810"}, {"alt": "798"}, {"range": "811", "text": "812"}, {"range": "813", "text": "802"}, {"range": "814", "text": "804"}, {"alt": "789"}, {"range": "815", "text": "816"}, {"alt": "792"}, {"range": "817", "text": "818"}, {"alt": "795"}, {"range": "819", "text": "820"}, {"alt": "798"}, {"range": "821", "text": "822"}, {"alt": "789"}, {"range": "823", "text": "824"}, {"alt": "792"}, {"range": "825", "text": "826"}, {"alt": "795"}, {"range": "827", "text": "828"}, {"alt": "798"}, {"range": "829", "text": "830"}, {"alt": "789"}, {"range": "831", "text": "832"}, {"alt": "792"}, {"range": "833", "text": "834"}, {"alt": "795"}, {"range": "835", "text": "836"}, {"alt": "798"}, {"range": "837", "text": "838"}, {"range": "839", "text": "802"}, {"range": "840", "text": "804"}, {"range": "841", "text": "802"}, {"range": "842", "text": "804"}, {"range": "843", "text": "802"}, {"range": "844", "text": "804"}, {"alt": "789"}, {"range": "845", "text": "846"}, {"alt": "792"}, {"range": "847", "text": "848"}, {"alt": "795"}, {"range": "849", "text": "850"}, {"alt": "798"}, {"range": "851", "text": "852"}, {"alt": "789"}, {"range": "853", "text": "854"}, {"alt": "792"}, {"range": "855", "text": "856"}, {"alt": "795"}, {"range": "857", "text": "858"}, {"alt": "798"}, {"range": "859", "text": "860"}, {"range": "861", "text": "802"}, {"range": "862", "text": "804"}, {"range": "863", "text": "802"}, {"range": "864", "text": "804"}, {"range": "865", "text": "802"}, {"range": "866", "text": "804"}, {"range": "867", "text": "802"}, {"range": "868", "text": "804"}, {"range": "869", "text": "802"}, {"range": "870", "text": "804"}, {"range": "871", "text": "802"}, {"range": "872", "text": "804"}, {"range": "873", "text": "802"}, {"range": "874", "text": "804"}, {"range": "875", "text": "802"}, {"range": "876", "text": "804"}, {"range": "877", "text": "802"}, {"range": "878", "text": "804"}, {"range": "879", "text": "802"}, {"range": "880", "text": "804"}, {"alt": "789"}, {"range": "881", "text": "882"}, {"alt": "792"}, {"range": "883", "text": "884"}, {"alt": "795"}, {"range": "885", "text": "886"}, {"alt": "798"}, {"range": "887", "text": "888"}, {"alt": "789"}, {"range": "889", "text": "890"}, {"alt": "792"}, {"range": "891", "text": "892"}, {"alt": "795"}, {"range": "893", "text": "894"}, {"alt": "798"}, {"range": "895", "text": "896"}, {"alt": "789"}, {"range": "897", "text": "898"}, {"alt": "792"}, {"range": "899", "text": "900"}, {"alt": "795"}, {"range": "901", "text": "902"}, {"alt": "798"}, {"range": "903", "text": "904"}, {"range": "905", "text": "802"}, {"range": "906", "text": "804"}, {"range": "907", "text": "802"}, {"range": "908", "text": "804"}, {"range": "909", "text": "802"}, {"range": "910", "text": "804"}, {"range": "911", "text": "802"}, {"range": "912", "text": "804"}, {"range": "913", "text": "802"}, {"range": "914", "text": "804"}, {"alt": "789"}, {"range": "915", "text": "916"}, {"alt": "792"}, {"range": "917", "text": "918"}, {"alt": "795"}, {"range": "919", "text": "920"}, {"alt": "798"}, {"range": "921", "text": "922"}, {"range": "923", "text": "802"}, {"range": "924", "text": "804"}, {"range": "925", "text": "802"}, {"range": "926", "text": "804"}, {"range": "927", "text": "802"}, {"range": "928", "text": "804"}, {"range": "929", "text": "802"}, {"range": "930", "text": "804"}, {"range": "931", "text": "802"}, {"range": "932", "text": "804"}, {"range": "933", "text": "802"}, {"range": "934", "text": "804"}, {"range": "935", "text": "802"}, {"range": "936", "text": "804"}, {"range": "937", "text": "802"}, {"range": "938", "text": "804"}, {"range": "939", "text": "802"}, {"range": "940", "text": "804"}, {"range": "941", "text": "802"}, {"range": "942", "text": "804"}, {"range": "943", "text": "802"}, {"range": "944", "text": "804"}, {"range": "945", "text": "802"}, {"range": "946", "text": "804"}, {"range": "947", "text": "802"}, {"range": "948", "text": "804"}, {"range": "949", "text": "802"}, {"range": "950", "text": "804"}, {"range": "951", "text": "802"}, {"range": "952", "text": "804"}, {"range": "953", "text": "802"}, {"range": "954", "text": "804"}, {"range": "955", "text": "802"}, {"range": "956", "text": "804"}, {"range": "957", "text": "802"}, {"range": "958", "text": "804"}, {"range": "959", "text": "802"}, {"range": "960", "text": "804"}, {"range": "961", "text": "802"}, {"range": "962", "text": "804"}, {"range": "963", "text": "802"}, {"range": "964", "text": "804"}, {"range": "965", "text": "802"}, {"range": "966", "text": "804"}, {"range": "967", "text": "802"}, {"range": "968", "text": "804"}, {"range": "969", "text": "802"}, {"range": "970", "text": "804"}, "&apos;", [1713, 1883], "\n              We&apos;re passionate about transforming great ideas into exceptional mobile experiences. \n              Meet the team and learn about our mission.\n            ", "&lsquo;", [1713, 1883], "\n              We&lsquo;re passionate about transforming great ideas into exceptional mobile experiences. \n              Meet the team and learn about our mission.\n            ", "&#39;", [1713, 1883], "\n              We&#39;re passionate about transforming great ideas into exceptional mobile experiences. \n              Meet the team and learn about our mission.\n            ", "&rsquo;", [1713, 1883], "\n              We&rsquo;re passionate about transforming great ideas into exceptional mobile experiences. \n              Meet the team and learn about our mission.\n            ", [2302, 2305], "unknown", [2302, 2305], "never", [11687, 11791], "\n                We&apos;re here to help! Get in touch with our team for personalized answers.\n              ", [11687, 11791], "\n                We&lsquo;re here to help! Get in touch with our team for personalized answers.\n              ", [11687, 11791], "\n                We&#39;re here to help! Get in touch with our team for personalized answers.\n              ", [11687, 11791], "\n                We&rsquo;re here to help! Get in touch with our team for personalized answers.\n              ", [431, 434], [431, 434], [845, 908], "\n                We&apos;re More Than Just Developers\n              ", [845, 908], "\n                We&lsquo;re More Than Just Developers\n              ", [845, 908], "\n                We&#39;re More Than Just Develo<PERSON>\n              ", [845, 908], "\n                We&rsquo;re More Than Just Developers\n              ", [1038, 1310], "\n                  At Mobilify, we believe that every great idea deserves to become reality.\n                  We&apos;re passionate about helping founders and businesses succeed by removing\n                  the traditional barriers to mobile app development.\n                ", [1038, 1310], "\n                  At Mobilify, we believe that every great idea deserves to become reality.\n                  We&lsquo;re passionate about helping founders and businesses succeed by removing\n                  the traditional barriers to mobile app development.\n                ", [1038, 1310], "\n                  At Mobilify, we believe that every great idea deserves to become reality.\n                  We&#39;re passionate about helping founders and businesses succeed by removing\n                  the traditional barriers to mobile app development.\n                ", [1038, 1310], "\n                  At Mobilify, we believe that every great idea deserves to become reality.\n                  We&rsquo;re passionate about helping founders and businesses succeed by removing\n                  the traditional barriers to mobile app development.\n                ", [1335, 1677], "\n                  Our commitment goes beyond just writing code – we&apos;re your partners in\n                  bringing your vision to life. We focus on quality over quantity, ensuring\n                  each app we create is crafted with care, attention to detail, and a deep\n                  understanding of your unique needs.\n                ", [1335, 1677], "\n                  Our commitment goes beyond just writing code – we&lsquo;re your partners in\n                  bringing your vision to life. We focus on quality over quantity, ensuring\n                  each app we create is crafted with care, attention to detail, and a deep\n                  understanding of your unique needs.\n                ", [1335, 1677], "\n                  Our commitment goes beyond just writing code – we&#39;re your partners in\n                  bringing your vision to life. We focus on quality over quantity, ensuring\n                  each app we create is crafted with care, attention to detail, and a deep\n                  understanding of your unique needs.\n                ", [1335, 1677], "\n                  Our commitment goes beyond just writing code – we&rsquo;re your partners in\n                  bringing your vision to life. We focus on quality over quantity, ensuring\n                  each app we create is crafted with care, attention to detail, and a deep\n                  understanding of your unique needs.\n                ", [500, 503], [500, 503], [980, 983], [980, 983], [1010, 1013], [1010, 1013], [3324, 3557], "\n              We&apos;d love to learn about your project and explore how we can help bring your mobile app vision to life. \n              Let's start a conversation about your goals and how Mobilify can support your success.\n            ", [3324, 3557], "\n              We&lsquo;d love to learn about your project and explore how we can help bring your mobile app vision to life. \n              Let's start a conversation about your goals and how Mobilify can support your success.\n            ", [3324, 3557], "\n              We&#39;d love to learn about your project and explore how we can help bring your mobile app vision to life. \n              Let's start a conversation about your goals and how Mobilify can support your success.\n            ", [3324, 3557], "\n              We&rsquo;d love to learn about your project and explore how we can help bring your mobile app vision to life. \n              Let's start a conversation about your goals and how Mobilify can support your success.\n            ", [3324, 3557], "\n              We'd love to learn about your project and explore how we can help bring your mobile app vision to life. \n              Let&apos;s start a conversation about your goals and how Mobilify can support your success.\n            ", [3324, 3557], "\n              We'd love to learn about your project and explore how we can help bring your mobile app vision to life. \n              Let&lsquo;s start a conversation about your goals and how Mobilify can support your success.\n            ", [3324, 3557], "\n              We'd love to learn about your project and explore how we can help bring your mobile app vision to life. \n              Let&#39;s start a conversation about your goals and how Mobilify can support your success.\n            ", [3324, 3557], "\n              We'd love to learn about your project and explore how we can help bring your mobile app vision to life. \n              Let&rsquo;s start a conversation about your goals and how Mobilify can support your success.\n            ", [135, 138], [135, 138], [1322, 1325], [1322, 1325], [1358, 1361], [1358, 1361], [1636, 1639], [1636, 1639], [1672, 1675], [1672, 1675], [1960, 1963], [1960, 1963], [1996, 1999], [1996, 1999], [4648, 4651], [4648, 4651], [108, 111], [108, 111], [139, 142], [139, 142], [970, 1247], "\n              We believe that innovation shouldn&apos;t be limited by technical barriers or \n              prohibitive costs. Every great idea deserves the chance to reach users \n              through the most personal and powerful platform available – mobile devices.\n            ", [970, 1247], "\n              We believe that innovation shouldn&lsquo;t be limited by technical barriers or \n              prohibitive costs. Every great idea deserves the chance to reach users \n              through the most personal and powerful platform available – mobile devices.\n            ", [970, 1247], "\n              We believe that innovation shouldn&#39;t be limited by technical barriers or \n              prohibitive costs. Every great idea deserves the chance to reach users \n              through the most personal and powerful platform available – mobile devices.\n            ", [970, 1247], "\n              We believe that innovation shouldn&rsquo;t be limited by technical barriers or \n              prohibitive costs. Every great idea deserves the chance to reach users \n              through the most personal and powerful platform available – mobile devices.\n            ", [1280, 1596], "\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We&apos;re not \n              just building apps; we're building the future of mobile experiences, one \n              project at a time.\n            ", [1280, 1596], "\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We&lsquo;re not \n              just building apps; we're building the future of mobile experiences, one \n              project at a time.\n            ", [1280, 1596], "\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We&#39;re not \n              just building apps; we're building the future of mobile experiences, one \n              project at a time.\n            ", [1280, 1596], "\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We&rsquo;re not \n              just building apps; we're building the future of mobile experiences, one \n              project at a time.\n            ", [1280, 1596], "\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We're not \n              just building apps; we&apos;re building the future of mobile experiences, one \n              project at a time.\n            ", [1280, 1596], "\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We're not \n              just building apps; we&lsquo;re building the future of mobile experiences, one \n              project at a time.\n            ", [1280, 1596], "\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We're not \n              just building apps; we&#39;re building the future of mobile experiences, one \n              project at a time.\n            ", [1280, 1596], "\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We're not \n              just building apps; we&rsquo;re building the future of mobile experiences, one \n              project at a time.\n            ", [1270, 1273], [1270, 1273], [1304, 1307], [1304, 1307], [2041, 2044], [2041, 2044], [2075, 2078], [2075, 2078], [2046, 2049], [2046, 2049], [5965, 6029], "\n            Still have questions? We&apos;d love to help!\n          ", [5965, 6029], "\n            Still have questions? We&lsquo;d love to help!\n          ", [5965, 6029], "\n            Still have questions? We&#39;d love to help!\n          ", [5965, 6029], "\n            Still have questions? We&rsquo;d love to help!\n          ", [830, 833], [830, 833], [860, 863], [860, 863], [262, 265], [262, 265], [294, 297], [294, 297], [584, 587], [584, 587], [614, 617], [614, 617], [260, 263], [260, 263], [292, 295], [292, 295], [582, 585], [582, 585], [612, 615], [612, 615], [147, 150], [147, 150], [3058, 3061], [3058, 3061], [3462, 3465], [3462, 3465], [1562, 1565], [1562, 1565], [1592, 1595], [1592, 1595], [413, 416], [413, 416], [2042, 2045], [2042, 2045], [2086, 2089], [2086, 2089], [750, 753], [750, 753], [1488, 1491], [1488, 1491], [2440, 2443], [2440, 2443], [1495, 1498], [1495, 1498], [4096, 4099], [4096, 4099], [4651, 4654], [4651, 4654]]