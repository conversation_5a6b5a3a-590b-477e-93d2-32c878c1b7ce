[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\api\\newsletter\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\page.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\FAQClient.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\page.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\robots.ts": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\services\\page.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\sitemap.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\AnimationWrapper.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ChatTrigger.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ClientOnly.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CompanyValues.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\DarkModeToggle.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Logo.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Mission.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NewsletterSignup.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NoSSR.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\OrganizationSchema.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PortableText.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PricingTable.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ServicesFAQ.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleDarkModeToggle.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleFloatingChat.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleHeaderChat.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\StructuredData.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\TeamProfiles.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Button.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Card.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\index.ts": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Input.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Button.test.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Input.test.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Contact.test.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Header.test.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\InteractiveDemo.test.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\NewsletterSignup.test.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\config\\site.ts": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\index.ts": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useAnalytics.ts": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useContactForm.ts": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useSiteSettings.ts": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\metadata.ts": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\sanity.ts": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\utils.ts": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\simple.test.js": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\test-utils.tsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\CrispChat.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\GoogleAnalytics.tsx": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\index.ts": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\WebVitals.tsx": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Footer.tsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Header.tsx": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\index.ts": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\MobileMenu.tsx": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Navigation.tsx": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\AboutSnippet.tsx": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Contact.tsx": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ContactForm.tsx": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoInput.tsx": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoPreview.tsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoTabs.tsx": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Hero.tsx": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\index.ts": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\InteractiveDemo.tsx": "68", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Process.tsx": "69", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ServicesOverview.tsx": "70", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\analytics.ts": "71", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\forms.ts": "72", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\index.ts": "73", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\sanity.ts": "74", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNav.tsx": "75", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNewsletter.tsx": "76"}, {"size": 2063, "mtime": 1751676776314, "results": "77", "hashOfConfig": "78"}, {"size": 3626, "mtime": 1751505504335, "results": "79", "hashOfConfig": "78"}, {"size": 10206, "mtime": 1751677568881, "results": "80", "hashOfConfig": "78"}, {"size": 9655, "mtime": 1751677588874, "results": "81", "hashOfConfig": "78"}, {"size": 12223, "mtime": 1751677364145, "results": "82", "hashOfConfig": "78"}, {"size": 5797, "mtime": 1751577101014, "results": "83", "hashOfConfig": "78"}, {"size": 3587, "mtime": 1751676697710, "results": "84", "hashOfConfig": "78"}, {"size": 1922, "mtime": 1751676758569, "results": "85", "hashOfConfig": "78"}, {"size": 415, "mtime": 1751664165349, "results": "86", "hashOfConfig": "78"}, {"size": 2090, "mtime": 1751676794792, "results": "87", "hashOfConfig": "78"}, {"size": 1476, "mtime": 1751664119979, "results": "88", "hashOfConfig": "78"}, {"size": 641, "mtime": 1750993207566, "results": "89", "hashOfConfig": "78"}, {"size": 4656, "mtime": 1751677383028, "results": "90", "hashOfConfig": "78"}, {"size": 447, "mtime": 1750993163336, "results": "91", "hashOfConfig": "78"}, {"size": 3968, "mtime": 1750990727135, "results": "92", "hashOfConfig": "78"}, {"size": 4806, "mtime": 1751511499987, "results": "93", "hashOfConfig": "78"}, {"size": 315, "mtime": 1750990215277, "results": "94", "hashOfConfig": "78"}, {"size": 1703, "mtime": 1750990673093, "results": "95", "hashOfConfig": "78"}, {"size": 6904, "mtime": 1751661914782, "results": "96", "hashOfConfig": "78"}, {"size": 415, "mtime": 1750998195180, "results": "97", "hashOfConfig": "78"}, {"size": 2390, "mtime": 1751578724741, "results": "98", "hashOfConfig": "78"}, {"size": 6772, "mtime": 1751514986018, "results": "99", "hashOfConfig": "78"}, {"size": 7652, "mtime": 1750990602546, "results": "100", "hashOfConfig": "78"}, {"size": 6361, "mtime": 1750990637857, "results": "101", "hashOfConfig": "78"}, {"size": 1149, "mtime": 1751518090332, "results": "102", "hashOfConfig": "78"}, {"size": 1498, "mtime": 1751518229057, "results": "103", "hashOfConfig": "78"}, {"size": 1145, "mtime": 1751518286486, "results": "104", "hashOfConfig": "78"}, {"size": 4809, "mtime": 1751664389950, "results": "105", "hashOfConfig": "78"}, {"size": 4353, "mtime": 1751578594187, "results": "106", "hashOfConfig": "78"}, {"size": 2356, "mtime": 1751664777139, "results": "107", "hashOfConfig": "78"}, {"size": 1834, "mtime": 1751575325593, "results": "108", "hashOfConfig": "78"}, {"size": 300, "mtime": 1751575364754, "results": "109", "hashOfConfig": "78"}, {"size": 2176, "mtime": 1751575251624, "results": "110", "hashOfConfig": "78"}, {"size": 8324, "mtime": 1751662637625, "results": "111", "hashOfConfig": "78"}, {"size": 9276, "mtime": 1751662678560, "results": "112", "hashOfConfig": "78"}, {"size": 8698, "mtime": 1751662431191, "results": "113", "hashOfConfig": "78"}, {"size": 9021, "mtime": 1751662773289, "results": "114", "hashOfConfig": "78"}, {"size": 10379, "mtime": 1751662507680, "results": "115", "hashOfConfig": "78"}, {"size": 9645, "mtime": 1751662467457, "results": "116", "hashOfConfig": "78"}, {"size": 6036, "mtime": 1751670335025, "results": "117", "hashOfConfig": "78"}, {"size": 3477, "mtime": 1751515726939, "results": "118", "hashOfConfig": "78"}, {"size": 551, "mtime": 1751670473668, "results": "119", "hashOfConfig": "78"}, {"size": 5333, "mtime": 1751671766740, "results": "120", "hashOfConfig": "78"}, {"size": 6690, "mtime": 1751671807702, "results": "121", "hashOfConfig": "78"}, {"size": 5933, "mtime": 1751671826897, "results": "122", "hashOfConfig": "78"}, {"size": 5442, "mtime": 1751578759717, "results": "123", "hashOfConfig": "78"}, {"size": 6546, "mtime": 1751670381294, "results": "124", "hashOfConfig": "78"}, {"size": 169, "mtime": 1751574258441, "results": "125", "hashOfConfig": "78"}, {"size": 232, "mtime": 1751663733726, "results": "126", "hashOfConfig": "78"}, {"size": 5645, "mtime": 1751662735678, "results": "127", "hashOfConfig": "78"}, {"size": 5197, "mtime": 1751574977693, "results": "128", "hashOfConfig": "78"}, {"size": 859, "mtime": 1751574932472, "results": "129", "hashOfConfig": "78"}, {"size": 461, "mtime": 1751676842124, "results": "130", "hashOfConfig": "78"}, {"size": 2650, "mtime": 1751664824565, "results": "131", "hashOfConfig": "78"}, {"size": 1876, "mtime": 1751678801354, "results": "132", "hashOfConfig": "78"}, {"size": 2095, "mtime": 1751677507868, "results": "133", "hashOfConfig": "78"}, {"size": 626, "mtime": 1751678612776, "results": "134", "hashOfConfig": "78"}, {"size": 3132, "mtime": 1751676893042, "results": "135", "hashOfConfig": "78"}, {"size": 1762, "mtime": 1751676876473, "results": "136", "hashOfConfig": "78"}, {"size": 2443, "mtime": 1751677402067, "results": "137", "hashOfConfig": "78"}, {"size": 1852, "mtime": 1751676812928, "results": "138", "hashOfConfig": "78"}, {"size": 8619, "mtime": 1751670631570, "results": "139", "hashOfConfig": "78"}, {"size": 3659, "mtime": 1751677104382, "results": "140", "hashOfConfig": "78"}, {"size": 6695, "mtime": 1751677133910, "results": "141", "hashOfConfig": "78"}, {"size": 2584, "mtime": 1751677085119, "results": "142", "hashOfConfig": "78"}, {"size": 4130, "mtime": 1751670521401, "results": "143", "hashOfConfig": "78"}, {"size": 685, "mtime": 1751676834675, "results": "144", "hashOfConfig": "78"}, {"size": 3257, "mtime": 1751677312540, "results": "145", "hashOfConfig": "78"}, {"size": 4837, "mtime": 1751677422096, "results": "146", "hashOfConfig": "78"}, {"size": 4513, "mtime": 1751671722640, "results": "147", "hashOfConfig": "78"}, {"size": 8012, "mtime": 1751676639727, "results": "148", "hashOfConfig": "78"}, {"size": 5959, "mtime": 1751676601255, "results": "149", "hashOfConfig": "78"}, {"size": 6626, "mtime": 1751676680436, "results": "150", "hashOfConfig": "78"}, {"size": 4695, "mtime": 1751676570640, "results": "151", "hashOfConfig": "78"}, {"size": 3140, "mtime": 1751678483417, "results": "152", "hashOfConfig": "78"}, {"size": 5518, "mtime": 1751678510568, "results": "153", "hashOfConfig": "78"}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "ipl2r0", {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 18, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\about\\page.tsx", ["382"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\api\\newsletter\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\page.tsx", ["383", "384", "385"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx", ["386"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\FAQClient.tsx", ["387"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\services\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\sitemap.ts", ["388"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\AnimationWrapper.tsx", ["389"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ChatTrigger.tsx", ["390", "391"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ClientOnly.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CompanyValues.tsx", ["392", "393", "394"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\DarkModeToggle.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Logo.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Mission.tsx", ["395", "396", "397"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NewsletterSignup.tsx", ["398", "399", "400", "401", "402", "403"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NoSSR.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\OrganizationSchema.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PortableText.tsx", ["404", "405", "406"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PricingTable.tsx", ["407"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ServicesFAQ.tsx", ["408", "409"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleDarkModeToggle.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleFloatingChat.tsx", ["410", "411", "412", "413"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleHeaderChat.tsx", ["414", "415", "416", "417"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\StructuredData.tsx", ["418", "419", "420"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\TeamProfiles.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Button.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Input.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Contact.test.tsx", ["421"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Header.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\InteractiveDemo.test.tsx", ["422"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\NewsletterSignup.test.tsx", ["423"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\config\\site.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx", ["424", "425"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useAnalytics.ts", ["426", "427", "428"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useContactForm.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useSiteSettings.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\metadata.ts", ["429"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\sanity.ts", ["430", "431", "432"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\simple.test.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\test-utils.tsx", ["433", "434", "435"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\CrispChat.tsx", ["436", "437", "438", "439", "440", "441", "442", "443"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\GoogleAnalytics.tsx", ["444", "445"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\WebVitals.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\MobileMenu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\AboutSnippet.tsx", ["446", "447", "448"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Contact.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ContactForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoInput.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoPreview.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoTabs.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Hero.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\InteractiveDemo.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Process.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ServicesOverview.tsx", ["449", "450"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\analytics.ts", ["451", "452", "453", "454", "455", "456", "457", "458", "459"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\forms.ts", ["460"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\index.ts", ["461", "462", "463", "464", "465", "466", "467", "468", "469", "470", "471", "472", "473", "474", "475", "476", "477", "478"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\sanity.ts", ["479", "480", "481", "482", "483", "484"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNav.tsx", ["485"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNewsletter.tsx", ["486"], [], {"ruleId": "487", "severity": 2, "message": "488", "line": 46, "column": 17, "nodeType": "489", "messageId": "490", "suggestions": "491"}, {"ruleId": "492", "severity": 2, "message": "493", "line": 3, "column": 8, "nodeType": null, "messageId": "494", "endLine": 3, "endColumn": 13}, {"ruleId": "492", "severity": 2, "message": "495", "line": 5, "column": 10, "nodeType": null, "messageId": "494", "endLine": 5, "endColumn": 16}, {"ruleId": "492", "severity": 2, "message": "496", "line": 187, "column": 43, "nodeType": null, "messageId": "494", "endLine": 187, "endColumn": 48}, {"ruleId": "497", "severity": 2, "message": "498", "line": 78, "column": 23, "nodeType": "499", "messageId": "500", "endLine": 78, "endColumn": 26, "suggestions": "501"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 259, "column": 19, "nodeType": "489", "messageId": "490", "suggestions": "502"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 10, "column": 16, "nodeType": "499", "messageId": "500", "endLine": 10, "endColumn": 19, "suggestions": "503"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 19, "column": 37, "nodeType": "499", "messageId": "500", "endLine": 19, "endColumn": 40, "suggestions": "504"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 37, "column": 53, "nodeType": "499", "messageId": "500", "endLine": 37, "endColumn": 56, "suggestions": "505"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 38, "column": 18, "nodeType": "499", "messageId": "500", "endLine": 38, "endColumn": 21, "suggestions": "506"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 86, "column": 17, "nodeType": "489", "messageId": "490", "suggestions": "507"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 87, "column": 18, "nodeType": "489", "messageId": "490", "suggestions": "508"}, {"ruleId": "509", "severity": 2, "message": "510", "line": 89, "column": 13, "nodeType": "511", "endLine": 92, "endColumn": 14}, {"ruleId": "487", "severity": 2, "message": "488", "line": 29, "column": 49, "nodeType": "489", "messageId": "490", "suggestions": "512"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 36, "column": 82, "nodeType": "489", "messageId": "490", "suggestions": "513"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 37, "column": 37, "nodeType": "489", "messageId": "490", "suggestions": "514"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 40, "column": 57, "nodeType": "499", "messageId": "500", "endLine": 40, "endColumn": 60, "suggestions": "515"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 41, "column": 22, "nodeType": "499", "messageId": "500", "endLine": 41, "endColumn": 25, "suggestions": "516"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 67, "column": 57, "nodeType": "499", "messageId": "500", "endLine": 67, "endColumn": 60, "suggestions": "517"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 68, "column": 22, "nodeType": "499", "messageId": "500", "endLine": 68, "endColumn": 25, "suggestions": "518"}, {"ruleId": "492", "severity": 2, "message": "519", "line": 84, "column": 9, "nodeType": null, "messageId": "494", "endLine": 84, "endColumn": 24}, {"ruleId": "492", "severity": 2, "message": "520", "line": 85, "column": 9, "nodeType": null, "messageId": "494", "endLine": 85, "endColumn": 25}, {"ruleId": "492", "severity": 2, "message": "493", "line": 3, "column": 8, "nodeType": null, "messageId": "494", "endLine": 3, "endColumn": 13}, {"ruleId": "492", "severity": 2, "message": "521", "line": 4, "column": 10, "nodeType": null, "messageId": "494", "endLine": 4, "endColumn": 16}, {"ruleId": "522", "severity": 2, "message": "523", "line": 72, "column": 11, "nodeType": "524", "messageId": "525", "endLine": 72, "endColumn": 15, "fix": "526"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 92, "column": 38, "nodeType": "499", "messageId": "500", "endLine": 92, "endColumn": 41, "suggestions": "527"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 116, "column": 37, "nodeType": "489", "messageId": "490", "suggestions": "528"}, {"ruleId": "509", "severity": 2, "message": "510", "line": 118, "column": 11, "nodeType": "511", "endLine": 121, "endColumn": 12}, {"ruleId": "497", "severity": 2, "message": "498", "line": 9, "column": 53, "nodeType": "499", "messageId": "500", "endLine": 9, "endColumn": 56, "suggestions": "529"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 10, "column": 18, "nodeType": "499", "messageId": "500", "endLine": 10, "endColumn": 21, "suggestions": "530"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 17, "column": 53, "nodeType": "499", "messageId": "500", "endLine": 17, "endColumn": 56, "suggestions": "531"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 18, "column": 18, "nodeType": "499", "messageId": "500", "endLine": 18, "endColumn": 21, "suggestions": "532"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 9, "column": 53, "nodeType": "499", "messageId": "500", "endLine": 9, "endColumn": 56, "suggestions": "533"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 10, "column": 18, "nodeType": "499", "messageId": "500", "endLine": 10, "endColumn": 21, "suggestions": "534"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 17, "column": 53, "nodeType": "499", "messageId": "500", "endLine": 17, "endColumn": 56, "suggestions": "535"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 18, "column": 18, "nodeType": "499", "messageId": "500", "endLine": 18, "endColumn": 21, "suggestions": "536"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 5, "column": 10, "nodeType": "499", "messageId": "500", "endLine": 5, "endColumn": 13, "suggestions": "537"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 89, "column": 47, "nodeType": "499", "messageId": "500", "endLine": 89, "endColumn": 50, "suggestions": "538"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 103, "column": 60, "nodeType": "499", "messageId": "500", "endLine": 103, "endColumn": 63, "suggestions": "539"}, {"ruleId": "492", "severity": 2, "message": "540", "line": 2, "column": 26, "nodeType": null, "messageId": "494", "endLine": 2, "endColumn": 35}, {"ruleId": "492", "severity": 2, "message": "540", "line": 2, "column": 26, "nodeType": null, "messageId": "494", "endLine": 2, "endColumn": 35}, {"ruleId": "492", "severity": 2, "message": "540", "line": 2, "column": 26, "nodeType": null, "messageId": "494", "endLine": 2, "endColumn": 35}, {"ruleId": "497", "severity": 2, "message": "498", "line": 57, "column": 53, "nodeType": "499", "messageId": "500", "endLine": 57, "endColumn": 56, "suggestions": "541"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 58, "column": 18, "nodeType": "499", "messageId": "500", "endLine": 58, "endColumn": 21, "suggestions": "542"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 17, "column": 38, "nodeType": "499", "messageId": "500", "endLine": 17, "endColumn": 41, "suggestions": "543"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 75, "column": 53, "nodeType": "499", "messageId": "500", "endLine": 75, "endColumn": 56, "suggestions": "544"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 77, "column": 20, "nodeType": "499", "messageId": "500", "endLine": 77, "endColumn": 23, "suggestions": "545"}, {"ruleId": "492", "severity": 2, "message": "546", "line": 43, "column": 9, "nodeType": null, "messageId": "494", "endLine": 43, "endColumn": 26}, {"ruleId": "497", "severity": 2, "message": "498", "line": 22, "column": 32, "nodeType": "499", "messageId": "500", "endLine": 22, "endColumn": 35, "suggestions": "547"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 62, "column": 9, "nodeType": "499", "messageId": "500", "endLine": 62, "endColumn": 12, "suggestions": "548"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 109, "column": 11, "nodeType": "499", "messageId": "500", "endLine": 109, "endColumn": 14, "suggestions": "549"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 59, "column": 37, "nodeType": "499", "messageId": "500", "endLine": 59, "endColumn": 40, "suggestions": "550"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 160, "column": 66, "nodeType": "499", "messageId": "500", "endLine": 160, "endColumn": 69, "suggestions": "551"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 177, "column": 75, "nodeType": "499", "messageId": "500", "endLine": 177, "endColumn": 78, "suggestions": "552"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 8, "column": 13, "nodeType": "499", "messageId": "500", "endLine": 8, "endColumn": 16, "suggestions": "553"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 43, "column": 59, "nodeType": "499", "messageId": "500", "endLine": 43, "endColumn": 62, "suggestions": "554"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 44, "column": 24, "nodeType": "499", "messageId": "500", "endLine": 44, "endColumn": 27, "suggestions": "555"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 52, "column": 59, "nodeType": "499", "messageId": "500", "endLine": 52, "endColumn": 62, "suggestions": "556"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 53, "column": 24, "nodeType": "499", "messageId": "500", "endLine": 53, "endColumn": 27, "suggestions": "557"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 61, "column": 59, "nodeType": "499", "messageId": "500", "endLine": 61, "endColumn": 62, "suggestions": "558"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 62, "column": 24, "nodeType": "499", "messageId": "500", "endLine": 62, "endColumn": 27, "suggestions": "559"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 162, "column": 41, "nodeType": "499", "messageId": "500", "endLine": 162, "endColumn": 44, "suggestions": "560"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 7, "column": 21, "nodeType": "499", "messageId": "500", "endLine": 7, "endColumn": 24, "suggestions": "561"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 8, "column": 16, "nodeType": "499", "messageId": "500", "endLine": 8, "endColumn": 19, "suggestions": "562"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 23, "column": 19, "nodeType": "489", "messageId": "490", "suggestions": "563"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 29, "column": 21, "nodeType": "489", "messageId": "490", "suggestions": "564"}, {"ruleId": "487", "severity": 2, "message": "488", "line": 34, "column": 68, "nodeType": "489", "messageId": "490", "suggestions": "565"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 30, "column": 53, "nodeType": "499", "messageId": "500", "endLine": 30, "endColumn": 56, "suggestions": "566"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 31, "column": 18, "nodeType": "499", "messageId": "500", "endLine": 31, "endColumn": 21, "suggestions": "567"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 11, "column": 31, "nodeType": "499", "messageId": "500", "endLine": 11, "endColumn": 34, "suggestions": "568"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 23, "column": 38, "nodeType": "499", "messageId": "500", "endLine": 23, "endColumn": 41, "suggestions": "569"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 64, "column": 38, "nodeType": "499", "messageId": "500", "endLine": 64, "endColumn": 41, "suggestions": "570"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 228, "column": 63, "nodeType": "499", "messageId": "500", "endLine": 228, "endColumn": 66, "suggestions": "571"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 229, "column": 65, "nodeType": "499", "messageId": "500", "endLine": 229, "endColumn": 68, "suggestions": "572"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 230, "column": 89, "nodeType": "499", "messageId": "500", "endLine": 230, "endColumn": 92, "suggestions": "573"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 231, "column": 88, "nodeType": "499", "messageId": "500", "endLine": 231, "endColumn": 91, "suggestions": "574"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 232, "column": 83, "nodeType": "499", "messageId": "500", "endLine": 232, "endColumn": 86, "suggestions": "575"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 233, "column": 76, "nodeType": "499", "messageId": "500", "endLine": 233, "endColumn": 79, "suggestions": "576"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "577", "line": 184, "column": 14}, {"ruleId": "497", "severity": 2, "message": "498", "line": 98, "column": 34, "nodeType": "499", "messageId": "500", "endLine": 98, "endColumn": 37, "suggestions": "578"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 106, "column": 40, "nodeType": "499", "messageId": "500", "endLine": 106, "endColumn": 43, "suggestions": "579"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 121, "column": 13, "nodeType": "499", "messageId": "500", "endLine": 121, "endColumn": 16, "suggestions": "580"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 135, "column": 33, "nodeType": "499", "messageId": "500", "endLine": 135, "endColumn": 36, "suggestions": "581"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 160, "column": 35, "nodeType": "499", "messageId": "500", "endLine": 160, "endColumn": 38, "suggestions": "582"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 164, "column": 27, "nodeType": "499", "messageId": "500", "endLine": 164, "endColumn": 30, "suggestions": "583"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 208, "column": 13, "nodeType": "499", "messageId": "500", "endLine": 208, "endColumn": 16, "suggestions": "584"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 209, "column": 13, "nodeType": "499", "messageId": "500", "endLine": 209, "endColumn": 16, "suggestions": "585"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 210, "column": 10, "nodeType": "499", "messageId": "500", "endLine": 210, "endColumn": 13, "suggestions": "586"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 212, "column": 29, "nodeType": "499", "messageId": "500", "endLine": 212, "endColumn": 32, "suggestions": "587"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 213, "column": 16, "nodeType": "499", "messageId": "500", "endLine": 213, "endColumn": 19, "suggestions": "588"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 214, "column": 14, "nodeType": "499", "messageId": "500", "endLine": 214, "endColumn": 17, "suggestions": "589"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 215, "column": 17, "nodeType": "499", "messageId": "500", "endLine": 215, "endColumn": 20, "suggestions": "590"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 238, "column": 31, "nodeType": "499", "messageId": "500", "endLine": 238, "endColumn": 34, "suggestions": "591"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 239, "column": 31, "nodeType": "499", "messageId": "500", "endLine": 239, "endColumn": 34, "suggestions": "592"}, {"ruleId": "593", "severity": 2, "message": "594", "line": 242, "column": 35, "nodeType": "595", "messageId": "596", "endLine": 242, "endColumn": 37, "suggestions": "597"}, {"ruleId": "593", "severity": 2, "message": "594", "line": 243, "column": 39, "nodeType": "595", "messageId": "596", "endLine": 243, "endColumn": 41, "suggestions": "598"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 284, "column": 31, "nodeType": "499", "messageId": "500", "endLine": 284, "endColumn": 34, "suggestions": "599"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 69, "column": 13, "nodeType": "499", "messageId": "500", "endLine": 69, "endColumn": 16, "suggestions": "600"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 95, "column": 11, "nodeType": "499", "messageId": "500", "endLine": 95, "endColumn": 14, "suggestions": "601"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 116, "column": 9, "nodeType": "499", "messageId": "500", "endLine": 116, "endColumn": 12, "suggestions": "602"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 134, "column": 21, "nodeType": "499", "messageId": "500", "endLine": 134, "endColumn": 24, "suggestions": "603"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 173, "column": 13, "nodeType": "499", "messageId": "500", "endLine": 173, "endColumn": 16, "suggestions": "604"}, {"ruleId": "497", "severity": 2, "message": "498", "line": 223, "column": 13, "nodeType": "499", "messageId": "500", "endLine": 223, "endColumn": 16, "suggestions": "605"}, {"ruleId": "492", "severity": 2, "message": "606", "line": 69, "column": 43, "nodeType": null, "messageId": "494", "endLine": 69, "endColumn": 47}, {"ruleId": "492", "severity": 2, "message": "607", "line": 74, "column": 14, "nodeType": null, "messageId": "494", "endLine": 74, "endColumn": 19}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["608", "609", "610", "611"], "@typescript-eslint/no-unused-vars", "'Image' is defined but never used.", "unusedVar", "'motion' is defined but never used.", "'index' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["612", "613"], ["614", "615", "616", "617"], ["618", "619"], ["620", "621"], ["622", "623"], ["624", "625"], ["626", "627", "628", "629"], ["630", "631", "632", "633"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", ["634", "635", "636", "637"], ["638", "639", "640", "641"], ["642", "643", "644", "645"], ["646", "647"], ["648", "649"], ["650", "651"], ["652", "653"], "'isFooterVariant' is assigned a value but never used.", "'isSectionVariant' is assigned a value but never used.", "'urlFor' is defined but never used.", "prefer-const", "'text' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "654", "text": "655"}, ["656", "657"], ["658", "659", "660", "661"], ["662", "663"], ["664", "665"], ["666", "667"], ["668", "669"], ["670", "671"], ["672", "673"], ["674", "675"], ["676", "677"], ["678", "679"], ["680", "681"], ["682", "683"], "'fireEvent' is defined but never used.", ["684", "685"], ["686", "687"], ["688", "689"], ["690", "691"], ["692", "693"], "'keywordsToInclude' is assigned a value but never used.", ["694", "695"], ["696", "697"], ["698", "699"], ["700", "701"], ["702", "703"], ["704", "705"], ["706", "707"], ["708", "709"], ["710", "711"], ["712", "713"], ["714", "715"], ["716", "717"], ["718", "719"], ["720", "721"], ["722", "723"], ["724", "725"], ["726", "727", "728", "729"], ["730", "731", "732", "733"], ["734", "735", "736", "737"], ["738", "739"], ["740", "741"], ["742", "743"], ["744", "745"], ["746", "747"], ["748", "749"], ["750", "751"], ["752", "753"], ["754", "755"], ["756", "757"], ["758", "759"], "Parsing error: ']' expected.", ["760", "761"], ["762", "763"], ["764", "765"], ["766", "767"], ["768", "769"], ["770", "771"], ["772", "773"], ["774", "775"], ["776", "777"], ["778", "779"], ["780", "781"], ["782", "783"], ["784", "785"], ["786", "787"], ["788", "789"], "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["790", "791"], ["792", "793"], ["794", "795"], ["796", "797"], ["798", "799"], ["800", "801"], ["802", "803"], ["804", "805"], ["806", "807"], "'href' is defined but never used.", "'error' is defined but never used.", {"messageId": "808", "data": "809", "fix": "810", "desc": "811"}, {"messageId": "808", "data": "812", "fix": "813", "desc": "814"}, {"messageId": "808", "data": "815", "fix": "816", "desc": "817"}, {"messageId": "808", "data": "818", "fix": "819", "desc": "820"}, {"messageId": "821", "fix": "822", "desc": "823"}, {"messageId": "824", "fix": "825", "desc": "826"}, {"messageId": "808", "data": "827", "fix": "828", "desc": "811"}, {"messageId": "808", "data": "829", "fix": "830", "desc": "814"}, {"messageId": "808", "data": "831", "fix": "832", "desc": "817"}, {"messageId": "808", "data": "833", "fix": "834", "desc": "820"}, {"messageId": "821", "fix": "835", "desc": "823"}, {"messageId": "824", "fix": "836", "desc": "826"}, {"messageId": "821", "fix": "837", "desc": "823"}, {"messageId": "824", "fix": "838", "desc": "826"}, {"messageId": "821", "fix": "839", "desc": "823"}, {"messageId": "824", "fix": "840", "desc": "826"}, {"messageId": "821", "fix": "841", "desc": "823"}, {"messageId": "824", "fix": "842", "desc": "826"}, {"messageId": "808", "data": "843", "fix": "844", "desc": "811"}, {"messageId": "808", "data": "845", "fix": "846", "desc": "814"}, {"messageId": "808", "data": "847", "fix": "848", "desc": "817"}, {"messageId": "808", "data": "849", "fix": "850", "desc": "820"}, {"messageId": "808", "data": "851", "fix": "852", "desc": "811"}, {"messageId": "808", "data": "853", "fix": "854", "desc": "814"}, {"messageId": "808", "data": "855", "fix": "856", "desc": "817"}, {"messageId": "808", "data": "857", "fix": "858", "desc": "820"}, {"messageId": "808", "data": "859", "fix": "860", "desc": "811"}, {"messageId": "808", "data": "861", "fix": "862", "desc": "814"}, {"messageId": "808", "data": "863", "fix": "864", "desc": "817"}, {"messageId": "808", "data": "865", "fix": "866", "desc": "820"}, {"messageId": "808", "data": "867", "fix": "868", "desc": "811"}, {"messageId": "808", "data": "869", "fix": "870", "desc": "814"}, {"messageId": "808", "data": "871", "fix": "872", "desc": "817"}, {"messageId": "808", "data": "873", "fix": "874", "desc": "820"}, {"messageId": "808", "data": "875", "fix": "876", "desc": "811"}, {"messageId": "808", "data": "877", "fix": "878", "desc": "814"}, {"messageId": "808", "data": "879", "fix": "880", "desc": "817"}, {"messageId": "808", "data": "881", "fix": "882", "desc": "820"}, {"messageId": "821", "fix": "883", "desc": "823"}, {"messageId": "824", "fix": "884", "desc": "826"}, {"messageId": "821", "fix": "885", "desc": "823"}, {"messageId": "824", "fix": "886", "desc": "826"}, {"messageId": "821", "fix": "887", "desc": "823"}, {"messageId": "824", "fix": "888", "desc": "826"}, {"messageId": "821", "fix": "889", "desc": "823"}, {"messageId": "824", "fix": "890", "desc": "826"}, [1800, 1821], "const text = span.text;", {"messageId": "821", "fix": "891", "desc": "823"}, {"messageId": "824", "fix": "892", "desc": "826"}, {"messageId": "808", "data": "893", "fix": "894", "desc": "811"}, {"messageId": "808", "data": "895", "fix": "896", "desc": "814"}, {"messageId": "808", "data": "897", "fix": "898", "desc": "817"}, {"messageId": "808", "data": "899", "fix": "900", "desc": "820"}, {"messageId": "821", "fix": "901", "desc": "823"}, {"messageId": "824", "fix": "902", "desc": "826"}, {"messageId": "821", "fix": "903", "desc": "823"}, {"messageId": "824", "fix": "904", "desc": "826"}, {"messageId": "821", "fix": "905", "desc": "823"}, {"messageId": "824", "fix": "906", "desc": "826"}, {"messageId": "821", "fix": "907", "desc": "823"}, {"messageId": "824", "fix": "908", "desc": "826"}, {"messageId": "821", "fix": "909", "desc": "823"}, {"messageId": "824", "fix": "910", "desc": "826"}, {"messageId": "821", "fix": "911", "desc": "823"}, {"messageId": "824", "fix": "912", "desc": "826"}, {"messageId": "821", "fix": "913", "desc": "823"}, {"messageId": "824", "fix": "914", "desc": "826"}, {"messageId": "821", "fix": "915", "desc": "823"}, {"messageId": "824", "fix": "916", "desc": "826"}, {"messageId": "821", "fix": "917", "desc": "823"}, {"messageId": "824", "fix": "918", "desc": "826"}, {"messageId": "821", "fix": "919", "desc": "823"}, {"messageId": "824", "fix": "920", "desc": "826"}, {"messageId": "821", "fix": "921", "desc": "823"}, {"messageId": "824", "fix": "922", "desc": "826"}, {"messageId": "821", "fix": "923", "desc": "823"}, {"messageId": "824", "fix": "924", "desc": "826"}, {"messageId": "821", "fix": "925", "desc": "823"}, {"messageId": "824", "fix": "926", "desc": "826"}, {"messageId": "821", "fix": "927", "desc": "823"}, {"messageId": "824", "fix": "928", "desc": "826"}, {"messageId": "821", "fix": "929", "desc": "823"}, {"messageId": "824", "fix": "930", "desc": "826"}, {"messageId": "821", "fix": "931", "desc": "823"}, {"messageId": "824", "fix": "932", "desc": "826"}, {"messageId": "821", "fix": "933", "desc": "823"}, {"messageId": "824", "fix": "934", "desc": "826"}, {"messageId": "821", "fix": "935", "desc": "823"}, {"messageId": "824", "fix": "936", "desc": "826"}, {"messageId": "821", "fix": "937", "desc": "823"}, {"messageId": "824", "fix": "938", "desc": "826"}, {"messageId": "821", "fix": "939", "desc": "823"}, {"messageId": "824", "fix": "940", "desc": "826"}, {"messageId": "821", "fix": "941", "desc": "823"}, {"messageId": "824", "fix": "942", "desc": "826"}, {"messageId": "821", "fix": "943", "desc": "823"}, {"messageId": "824", "fix": "944", "desc": "826"}, {"messageId": "821", "fix": "945", "desc": "823"}, {"messageId": "824", "fix": "946", "desc": "826"}, {"messageId": "821", "fix": "947", "desc": "823"}, {"messageId": "824", "fix": "948", "desc": "826"}, {"messageId": "821", "fix": "949", "desc": "823"}, {"messageId": "824", "fix": "950", "desc": "826"}, {"messageId": "821", "fix": "951", "desc": "823"}, {"messageId": "824", "fix": "952", "desc": "826"}, {"messageId": "821", "fix": "953", "desc": "823"}, {"messageId": "824", "fix": "954", "desc": "826"}, {"messageId": "821", "fix": "955", "desc": "823"}, {"messageId": "824", "fix": "956", "desc": "826"}, {"messageId": "821", "fix": "957", "desc": "823"}, {"messageId": "824", "fix": "958", "desc": "826"}, {"messageId": "821", "fix": "959", "desc": "823"}, {"messageId": "824", "fix": "960", "desc": "826"}, {"messageId": "821", "fix": "961", "desc": "823"}, {"messageId": "824", "fix": "962", "desc": "826"}, {"messageId": "821", "fix": "963", "desc": "823"}, {"messageId": "824", "fix": "964", "desc": "826"}, {"messageId": "808", "data": "965", "fix": "966", "desc": "811"}, {"messageId": "808", "data": "967", "fix": "968", "desc": "814"}, {"messageId": "808", "data": "969", "fix": "970", "desc": "817"}, {"messageId": "808", "data": "971", "fix": "972", "desc": "820"}, {"messageId": "808", "data": "973", "fix": "974", "desc": "811"}, {"messageId": "808", "data": "975", "fix": "976", "desc": "814"}, {"messageId": "808", "data": "977", "fix": "978", "desc": "817"}, {"messageId": "808", "data": "979", "fix": "980", "desc": "820"}, {"messageId": "808", "data": "981", "fix": "982", "desc": "811"}, {"messageId": "808", "data": "983", "fix": "984", "desc": "814"}, {"messageId": "808", "data": "985", "fix": "986", "desc": "817"}, {"messageId": "808", "data": "987", "fix": "988", "desc": "820"}, {"messageId": "821", "fix": "989", "desc": "823"}, {"messageId": "824", "fix": "990", "desc": "826"}, {"messageId": "821", "fix": "991", "desc": "823"}, {"messageId": "824", "fix": "992", "desc": "826"}, {"messageId": "821", "fix": "993", "desc": "823"}, {"messageId": "824", "fix": "994", "desc": "826"}, {"messageId": "821", "fix": "995", "desc": "823"}, {"messageId": "824", "fix": "996", "desc": "826"}, {"messageId": "821", "fix": "997", "desc": "823"}, {"messageId": "824", "fix": "998", "desc": "826"}, {"messageId": "821", "fix": "999", "desc": "823"}, {"messageId": "824", "fix": "1000", "desc": "826"}, {"messageId": "821", "fix": "1001", "desc": "823"}, {"messageId": "824", "fix": "1002", "desc": "826"}, {"messageId": "821", "fix": "1003", "desc": "823"}, {"messageId": "824", "fix": "1004", "desc": "826"}, {"messageId": "821", "fix": "1005", "desc": "823"}, {"messageId": "824", "fix": "1006", "desc": "826"}, {"messageId": "821", "fix": "1007", "desc": "823"}, {"messageId": "824", "fix": "1008", "desc": "826"}, {"messageId": "821", "fix": "1009", "desc": "823"}, {"messageId": "824", "fix": "1010", "desc": "826"}, {"messageId": "821", "fix": "1011", "desc": "823"}, {"messageId": "824", "fix": "1012", "desc": "826"}, {"messageId": "821", "fix": "1013", "desc": "823"}, {"messageId": "824", "fix": "1014", "desc": "826"}, {"messageId": "821", "fix": "1015", "desc": "823"}, {"messageId": "824", "fix": "1016", "desc": "826"}, {"messageId": "821", "fix": "1017", "desc": "823"}, {"messageId": "824", "fix": "1018", "desc": "826"}, {"messageId": "821", "fix": "1019", "desc": "823"}, {"messageId": "824", "fix": "1020", "desc": "826"}, {"messageId": "821", "fix": "1021", "desc": "823"}, {"messageId": "824", "fix": "1022", "desc": "826"}, {"messageId": "821", "fix": "1023", "desc": "823"}, {"messageId": "824", "fix": "1024", "desc": "826"}, {"messageId": "821", "fix": "1025", "desc": "823"}, {"messageId": "824", "fix": "1026", "desc": "826"}, {"messageId": "821", "fix": "1027", "desc": "823"}, {"messageId": "824", "fix": "1028", "desc": "826"}, {"messageId": "821", "fix": "1029", "desc": "823"}, {"messageId": "824", "fix": "1030", "desc": "826"}, {"messageId": "821", "fix": "1031", "desc": "823"}, {"messageId": "824", "fix": "1032", "desc": "826"}, {"messageId": "821", "fix": "1033", "desc": "823"}, {"messageId": "824", "fix": "1034", "desc": "826"}, {"messageId": "821", "fix": "1035", "desc": "823"}, {"messageId": "824", "fix": "1036", "desc": "826"}, {"messageId": "821", "fix": "1037", "desc": "823"}, {"messageId": "824", "fix": "1038", "desc": "826"}, {"messageId": "821", "fix": "1039", "desc": "823"}, {"messageId": "824", "fix": "1040", "desc": "826"}, {"messageId": "1041", "data": "1042", "fix": "1043", "desc": "1044"}, {"messageId": "1041", "data": "1045", "fix": "1046", "desc": "1047"}, {"messageId": "1041", "data": "1048", "fix": "1049", "desc": "1044"}, {"messageId": "1041", "data": "1050", "fix": "1051", "desc": "1047"}, {"messageId": "821", "fix": "1052", "desc": "823"}, {"messageId": "824", "fix": "1053", "desc": "826"}, {"messageId": "821", "fix": "1054", "desc": "823"}, {"messageId": "824", "fix": "1055", "desc": "826"}, {"messageId": "821", "fix": "1056", "desc": "823"}, {"messageId": "824", "fix": "1057", "desc": "826"}, {"messageId": "821", "fix": "1058", "desc": "823"}, {"messageId": "824", "fix": "1059", "desc": "826"}, {"messageId": "821", "fix": "1060", "desc": "823"}, {"messageId": "824", "fix": "1061", "desc": "826"}, {"messageId": "821", "fix": "1062", "desc": "823"}, {"messageId": "824", "fix": "1063", "desc": "826"}, {"messageId": "821", "fix": "1064", "desc": "823"}, {"messageId": "824", "fix": "1065", "desc": "826"}, "replaceWithAlt", {"alt": "1066"}, {"range": "1067", "text": "1068"}, "Replace with `&apos;`.", {"alt": "1069"}, {"range": "1070", "text": "1071"}, "Replace with `&lsquo;`.", {"alt": "1072"}, {"range": "1073", "text": "1074"}, "Replace with `&#39;`.", {"alt": "1075"}, {"range": "1076", "text": "1077"}, "Replace with `&rsquo;`.", "suggestUnknown", {"range": "1078", "text": "1079"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1080", "text": "1081"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"alt": "1066"}, {"range": "1082", "text": "1083"}, {"alt": "1069"}, {"range": "1084", "text": "1085"}, {"alt": "1072"}, {"range": "1086", "text": "1087"}, {"alt": "1075"}, {"range": "1088", "text": "1089"}, {"range": "1090", "text": "1079"}, {"range": "1091", "text": "1081"}, {"range": "1092", "text": "1079"}, {"range": "1093", "text": "1081"}, {"range": "1094", "text": "1079"}, {"range": "1095", "text": "1081"}, {"range": "1096", "text": "1079"}, {"range": "1097", "text": "1081"}, {"alt": "1066"}, {"range": "1098", "text": "1099"}, {"alt": "1069"}, {"range": "1100", "text": "1101"}, {"alt": "1072"}, {"range": "1102", "text": "1103"}, {"alt": "1075"}, {"range": "1104", "text": "1105"}, {"alt": "1066"}, {"range": "1106", "text": "1107"}, {"alt": "1069"}, {"range": "1108", "text": "1109"}, {"alt": "1072"}, {"range": "1110", "text": "1111"}, {"alt": "1075"}, {"range": "1112", "text": "1113"}, {"alt": "1066"}, {"range": "1114", "text": "1115"}, {"alt": "1069"}, {"range": "1116", "text": "1117"}, {"alt": "1072"}, {"range": "1118", "text": "1119"}, {"alt": "1075"}, {"range": "1120", "text": "1121"}, {"alt": "1066"}, {"range": "1122", "text": "1123"}, {"alt": "1069"}, {"range": "1124", "text": "1125"}, {"alt": "1072"}, {"range": "1126", "text": "1127"}, {"alt": "1075"}, {"range": "1128", "text": "1129"}, {"alt": "1066"}, {"range": "1130", "text": "1131"}, {"alt": "1069"}, {"range": "1132", "text": "1133"}, {"alt": "1072"}, {"range": "1134", "text": "1135"}, {"alt": "1075"}, {"range": "1136", "text": "1137"}, {"range": "1138", "text": "1079"}, {"range": "1139", "text": "1081"}, {"range": "1140", "text": "1079"}, {"range": "1141", "text": "1081"}, {"range": "1142", "text": "1079"}, {"range": "1143", "text": "1081"}, {"range": "1144", "text": "1079"}, {"range": "1145", "text": "1081"}, {"range": "1146", "text": "1079"}, {"range": "1147", "text": "1081"}, {"alt": "1066"}, {"range": "1148", "text": "1149"}, {"alt": "1069"}, {"range": "1150", "text": "1151"}, {"alt": "1072"}, {"range": "1152", "text": "1153"}, {"alt": "1075"}, {"range": "1154", "text": "1155"}, {"range": "1156", "text": "1079"}, {"range": "1157", "text": "1081"}, {"range": "1158", "text": "1079"}, {"range": "1159", "text": "1081"}, {"range": "1160", "text": "1079"}, {"range": "1161", "text": "1081"}, {"range": "1162", "text": "1079"}, {"range": "1163", "text": "1081"}, {"range": "1164", "text": "1079"}, {"range": "1165", "text": "1081"}, {"range": "1166", "text": "1079"}, {"range": "1167", "text": "1081"}, {"range": "1168", "text": "1079"}, {"range": "1169", "text": "1081"}, {"range": "1170", "text": "1079"}, {"range": "1171", "text": "1081"}, {"range": "1172", "text": "1079"}, {"range": "1173", "text": "1081"}, {"range": "1174", "text": "1079"}, {"range": "1175", "text": "1081"}, {"range": "1176", "text": "1079"}, {"range": "1177", "text": "1081"}, {"range": "1178", "text": "1079"}, {"range": "1179", "text": "1081"}, {"range": "1180", "text": "1079"}, {"range": "1181", "text": "1081"}, {"range": "1182", "text": "1079"}, {"range": "1183", "text": "1081"}, {"range": "1184", "text": "1079"}, {"range": "1185", "text": "1081"}, {"range": "1186", "text": "1079"}, {"range": "1187", "text": "1081"}, {"range": "1188", "text": "1079"}, {"range": "1189", "text": "1081"}, {"range": "1190", "text": "1079"}, {"range": "1191", "text": "1081"}, {"range": "1192", "text": "1079"}, {"range": "1193", "text": "1081"}, {"range": "1194", "text": "1079"}, {"range": "1195", "text": "1081"}, {"range": "1196", "text": "1079"}, {"range": "1197", "text": "1081"}, {"range": "1198", "text": "1079"}, {"range": "1199", "text": "1081"}, {"range": "1200", "text": "1079"}, {"range": "1201", "text": "1081"}, {"range": "1202", "text": "1079"}, {"range": "1203", "text": "1081"}, {"range": "1204", "text": "1079"}, {"range": "1205", "text": "1081"}, {"range": "1206", "text": "1079"}, {"range": "1207", "text": "1081"}, {"range": "1208", "text": "1079"}, {"range": "1209", "text": "1081"}, {"range": "1210", "text": "1079"}, {"range": "1211", "text": "1081"}, {"range": "1212", "text": "1079"}, {"range": "1213", "text": "1081"}, {"range": "1214", "text": "1079"}, {"range": "1215", "text": "1081"}, {"range": "1216", "text": "1079"}, {"range": "1217", "text": "1081"}, {"range": "1218", "text": "1079"}, {"range": "1219", "text": "1081"}, {"alt": "1066"}, {"range": "1220", "text": "1221"}, {"alt": "1069"}, {"range": "1222", "text": "1223"}, {"alt": "1072"}, {"range": "1224", "text": "1225"}, {"alt": "1075"}, {"range": "1226", "text": "1227"}, {"alt": "1066"}, {"range": "1228", "text": "1229"}, {"alt": "1069"}, {"range": "1230", "text": "1231"}, {"alt": "1072"}, {"range": "1232", "text": "1233"}, {"alt": "1075"}, {"range": "1234", "text": "1235"}, {"alt": "1066"}, {"range": "1236", "text": "1237"}, {"alt": "1069"}, {"range": "1238", "text": "1239"}, {"alt": "1072"}, {"range": "1240", "text": "1241"}, {"alt": "1075"}, {"range": "1242", "text": "1243"}, {"range": "1244", "text": "1079"}, {"range": "1245", "text": "1081"}, {"range": "1246", "text": "1079"}, {"range": "1247", "text": "1081"}, {"range": "1248", "text": "1079"}, {"range": "1249", "text": "1081"}, {"range": "1250", "text": "1079"}, {"range": "1251", "text": "1081"}, {"range": "1252", "text": "1079"}, {"range": "1253", "text": "1081"}, {"range": "1254", "text": "1079"}, {"range": "1255", "text": "1081"}, {"range": "1256", "text": "1079"}, {"range": "1257", "text": "1081"}, {"range": "1258", "text": "1079"}, {"range": "1259", "text": "1081"}, {"range": "1260", "text": "1079"}, {"range": "1261", "text": "1081"}, {"range": "1262", "text": "1079"}, {"range": "1263", "text": "1081"}, {"range": "1264", "text": "1079"}, {"range": "1265", "text": "1081"}, {"range": "1266", "text": "1079"}, {"range": "1267", "text": "1081"}, {"range": "1268", "text": "1079"}, {"range": "1269", "text": "1081"}, {"range": "1270", "text": "1079"}, {"range": "1271", "text": "1081"}, {"range": "1272", "text": "1079"}, {"range": "1273", "text": "1081"}, {"range": "1274", "text": "1079"}, {"range": "1275", "text": "1081"}, {"range": "1276", "text": "1079"}, {"range": "1277", "text": "1081"}, {"range": "1278", "text": "1079"}, {"range": "1279", "text": "1081"}, {"range": "1280", "text": "1079"}, {"range": "1281", "text": "1081"}, {"range": "1282", "text": "1079"}, {"range": "1283", "text": "1081"}, {"range": "1284", "text": "1079"}, {"range": "1285", "text": "1081"}, {"range": "1286", "text": "1079"}, {"range": "1287", "text": "1081"}, {"range": "1288", "text": "1079"}, {"range": "1289", "text": "1081"}, {"range": "1290", "text": "1079"}, {"range": "1291", "text": "1081"}, {"range": "1292", "text": "1079"}, {"range": "1293", "text": "1081"}, {"range": "1294", "text": "1079"}, {"range": "1295", "text": "1081"}, "replaceEmptyObjectType", {"replacement": "1296"}, {"range": "1297", "text": "1296"}, "Replace `{}` with `object`.", {"replacement": "1079"}, {"range": "1298", "text": "1079"}, "Replace `{}` with `unknown`.", {"replacement": "1296"}, {"range": "1299", "text": "1296"}, {"replacement": "1079"}, {"range": "1300", "text": "1079"}, {"range": "1301", "text": "1079"}, {"range": "1302", "text": "1081"}, {"range": "1303", "text": "1079"}, {"range": "1304", "text": "1081"}, {"range": "1305", "text": "1079"}, {"range": "1306", "text": "1081"}, {"range": "1307", "text": "1079"}, {"range": "1308", "text": "1081"}, {"range": "1309", "text": "1079"}, {"range": "1310", "text": "1081"}, {"range": "1311", "text": "1079"}, {"range": "1312", "text": "1081"}, {"range": "1313", "text": "1079"}, {"range": "1314", "text": "1081"}, "&apos;", [1727, 1897], "\n              We&apos;re passionate about transforming great ideas into exceptional mobile experiences. \n              Meet the team and learn about our mission.\n            ", "&lsquo;", [1727, 1897], "\n              We&lsquo;re passionate about transforming great ideas into exceptional mobile experiences. \n              Meet the team and learn about our mission.\n            ", "&#39;", [1727, 1897], "\n              We&#39;re passionate about transforming great ideas into exceptional mobile experiences. \n              Meet the team and learn about our mission.\n            ", "&rsquo;", [1727, 1897], "\n              We&rsquo;re passionate about transforming great ideas into exceptional mobile experiences. \n              Meet the team and learn about our mission.\n            ", [2316, 2319], "unknown", [2316, 2319], "never", [11701, 11805], "\n                We&apos;re here to help! Get in touch with our team for personalized answers.\n              ", [11701, 11805], "\n                We&lsquo;re here to help! Get in touch with our team for personalized answers.\n              ", [11701, 11805], "\n                We&#39;re here to help! Get in touch with our team for personalized answers.\n              ", [11701, 11805], "\n                We&rsquo;re here to help! Get in touch with our team for personalized answers.\n              ", [431, 434], [431, 434], [500, 503], [500, 503], [991, 994], [991, 994], [1021, 1024], [1021, 1024], [3324, 3557], "\n              We&apos;d love to learn about your project and explore how we can help bring your mobile app vision to life. \n              Let's start a conversation about your goals and how Mobilify can support your success.\n            ", [3324, 3557], "\n              We&lsquo;d love to learn about your project and explore how we can help bring your mobile app vision to life. \n              Let's start a conversation about your goals and how Mobilify can support your success.\n            ", [3324, 3557], "\n              We&#39;d love to learn about your project and explore how we can help bring your mobile app vision to life. \n              Let's start a conversation about your goals and how Mobilify can support your success.\n            ", [3324, 3557], "\n              We&rsquo;d love to learn about your project and explore how we can help bring your mobile app vision to life. \n              Let's start a conversation about your goals and how Mobilify can support your success.\n            ", [3324, 3557], "\n              We'd love to learn about your project and explore how we can help bring your mobile app vision to life. \n              Let&apos;s start a conversation about your goals and how Mobilify can support your success.\n            ", [3324, 3557], "\n              We'd love to learn about your project and explore how we can help bring your mobile app vision to life. \n              Let&lsquo;s start a conversation about your goals and how Mobilify can support your success.\n            ", [3324, 3557], "\n              We'd love to learn about your project and explore how we can help bring your mobile app vision to life. \n              Let&#39;s start a conversation about your goals and how Mobilify can support your success.\n            ", [3324, 3557], "\n              We'd love to learn about your project and explore how we can help bring your mobile app vision to life. \n              Let&rsquo;s start a conversation about your goals and how Mobilify can support your success.\n            ", [970, 1247], "\n              We believe that innovation shouldn&apos;t be limited by technical barriers or \n              prohibitive costs. Every great idea deserves the chance to reach users \n              through the most personal and powerful platform available – mobile devices.\n            ", [970, 1247], "\n              We believe that innovation shouldn&lsquo;t be limited by technical barriers or \n              prohibitive costs. Every great idea deserves the chance to reach users \n              through the most personal and powerful platform available – mobile devices.\n            ", [970, 1247], "\n              We believe that innovation shouldn&#39;t be limited by technical barriers or \n              prohibitive costs. Every great idea deserves the chance to reach users \n              through the most personal and powerful platform available – mobile devices.\n            ", [970, 1247], "\n              We believe that innovation shouldn&rsquo;t be limited by technical barriers or \n              prohibitive costs. Every great idea deserves the chance to reach users \n              through the most personal and powerful platform available – mobile devices.\n            ", [1280, 1596], "\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We&apos;re not \n              just building apps; we're building the future of mobile experiences, one \n              project at a time.\n            ", [1280, 1596], "\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We&lsquo;re not \n              just building apps; we're building the future of mobile experiences, one \n              project at a time.\n            ", [1280, 1596], "\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We&#39;re not \n              just building apps; we're building the future of mobile experiences, one \n              project at a time.\n            ", [1280, 1596], "\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We&rsquo;re not \n              just building apps; we're building the future of mobile experiences, one \n              project at a time.\n            ", [1280, 1596], "\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We're not \n              just building apps; we&apos;re building the future of mobile experiences, one \n              project at a time.\n            ", [1280, 1596], "\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We're not \n              just building apps; we&lsquo;re building the future of mobile experiences, one \n              project at a time.\n            ", [1280, 1596], "\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We're not \n              just building apps; we&#39;re building the future of mobile experiences, one \n              project at a time.\n            ", [1280, 1596], "\n              Our approach combines cutting-edge technology with human expertise, ensuring \n              that each app we create is not just functional, but exceptional. We're not \n              just building apps; we&rsquo;re building the future of mobile experiences, one \n              project at a time.\n            ", [1270, 1273], [1270, 1273], [1304, 1307], [1304, 1307], [2041, 2044], [2041, 2044], [2075, 2078], [2075, 2078], [2046, 2049], [2046, 2049], [5965, 6029], "\n            Still have questions? We&apos;d love to help!\n          ", [5965, 6029], "\n            Still have questions? We&lsquo;d love to help!\n          ", [5965, 6029], "\n            Still have questions? We&#39;d love to help!\n          ", [5965, 6029], "\n            Still have questions? We&rsquo;d love to help!\n          ", [262, 265], [262, 265], [294, 297], [294, 297], [584, 587], [584, 587], [614, 617], [614, 617], [260, 263], [260, 263], [292, 295], [292, 295], [582, 585], [582, 585], [612, 615], [612, 615], [147, 150], [147, 150], [3058, 3061], [3058, 3061], [3462, 3465], [3462, 3465], [1562, 1565], [1562, 1565], [1592, 1595], [1592, 1595], [413, 416], [413, 416], [2042, 2045], [2042, 2045], [2086, 2089], [2086, 2089], [750, 753], [750, 753], [1488, 1491], [1488, 1491], [2440, 2443], [2440, 2443], [1495, 1498], [1495, 1498], [4096, 4099], [4096, 4099], [4651, 4654], [4651, 4654], [135, 138], [135, 138], [1322, 1325], [1322, 1325], [1358, 1361], [1358, 1361], [1636, 1639], [1636, 1639], [1672, 1675], [1672, 1675], [1960, 1963], [1960, 1963], [1996, 1999], [1996, 1999], [4648, 4651], [4648, 4651], [108, 111], [108, 111], [139, 142], [139, 142], [846, 909], "\n                We&apos;re More Than Just Developers\n              ", [846, 909], "\n                We&lsquo;re More Than Just Developers\n              ", [846, 909], "\n                We&#39;re More Than Just Develo<PERSON>\n              ", [846, 909], "\n                We&rsquo;re More Than Just Developers\n              ", [1039, 1311], "\n                  At Mobilify, we believe that every great idea deserves to become reality.\n                  We&apos;re passionate about helping founders and businesses succeed by removing\n                  the traditional barriers to mobile app development.\n                ", [1039, 1311], "\n                  At Mobilify, we believe that every great idea deserves to become reality.\n                  We&lsquo;re passionate about helping founders and businesses succeed by removing\n                  the traditional barriers to mobile app development.\n                ", [1039, 1311], "\n                  At Mobilify, we believe that every great idea deserves to become reality.\n                  We&#39;re passionate about helping founders and businesses succeed by removing\n                  the traditional barriers to mobile app development.\n                ", [1039, 1311], "\n                  At Mobilify, we believe that every great idea deserves to become reality.\n                  We&rsquo;re passionate about helping founders and businesses succeed by removing\n                  the traditional barriers to mobile app development.\n                ", [1336, 1678], "\n                  Our commitment goes beyond just writing code – we&apos;re your partners in\n                  bringing your vision to life. We focus on quality over quantity, ensuring\n                  each app we create is crafted with care, attention to detail, and a deep\n                  understanding of your unique needs.\n                ", [1336, 1678], "\n                  Our commitment goes beyond just writing code – we&lsquo;re your partners in\n                  bringing your vision to life. We focus on quality over quantity, ensuring\n                  each app we create is crafted with care, attention to detail, and a deep\n                  understanding of your unique needs.\n                ", [1336, 1678], "\n                  Our commitment goes beyond just writing code – we&#39;re your partners in\n                  bringing your vision to life. We focus on quality over quantity, ensuring\n                  each app we create is crafted with care, attention to detail, and a deep\n                  understanding of your unique needs.\n                ", [1336, 1678], "\n                  Our commitment goes beyond just writing code – we&rsquo;re your partners in\n                  bringing your vision to life. We focus on quality over quantity, ensuring\n                  each app we create is crafted with care, attention to detail, and a deep\n                  understanding of your unique needs.\n                ", [830, 833], [830, 833], [860, 863], [860, 863], [316, 319], [316, 319], [550, 553], [550, 553], [1535, 1538], [1535, 1538], [6252, 6255], [6252, 6255], [6331, 6334], [6331, 6334], [6434, 6437], [6434, 6437], [6536, 6539], [6536, 6539], [6633, 6636], [6633, 6636], [6723, 6726], [6723, 6726], [2501, 2504], [2501, 2504], [2643, 2646], [2643, 2646], [2913, 2916], [2913, 2916], [3206, 3209], [3206, 3209], [3641, 3644], [3641, 3644], [3722, 3725], [3722, 3725], [4664, 4667], [4664, 4667], [4681, 4684], [4681, 4684], [4695, 4698], [4695, 4698], [4760, 4763], [4760, 4763], [4781, 4784], [4781, 4784], [4799, 4802], [4799, 4802], [4820, 4823], [4820, 4823], [5348, 5351], [5348, 5351], [5405, 5408], [5405, 5408], "object", [5520, 5522], [5520, 5522], [5600, 5602], [5600, 5602], [6406, 6409], [6406, 6409], [1494, 1497], [1494, 1497], [1981, 1984], [1981, 1984], [2376, 2379], [2376, 2379], [2716, 2719], [2716, 2719], [3452, 3455], [3452, 3455], [4330, 4333], [4330, 4333]]