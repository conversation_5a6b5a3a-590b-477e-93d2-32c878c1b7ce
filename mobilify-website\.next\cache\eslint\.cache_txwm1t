[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\api\\newsletter\\route.ts": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\page.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\FAQClient.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\page.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\robots.ts": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\services\\page.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\sitemap.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\AnimationWrapper.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ChatTrigger.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ClientOnly.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CompanyValues.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\DarkModeToggle.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Logo.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Mission.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NewsletterSignup.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NoSSR.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\OrganizationSchema.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PortableText.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PricingTable.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ServicesFAQ.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleDarkModeToggle.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleFloatingChat.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleHeaderChat.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\StructuredData.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\TeamProfiles.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Button.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Card.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\index.ts": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Input.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Button.test.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Input.test.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Contact.test.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Header.test.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\InteractiveDemo.test.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\NewsletterSignup.test.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\config\\site.ts": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\index.ts": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useAnalytics.ts": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useContactForm.ts": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useSiteSettings.ts": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\metadata.ts": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\sanity.ts": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\utils.ts": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\simple.test.js": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\test-utils.tsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\CrispChat.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\GoogleAnalytics.tsx": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\index.ts": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\WebVitals.tsx": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Footer.tsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Header.tsx": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\index.ts": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\MobileMenu.tsx": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Navigation.tsx": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\AboutSnippet.tsx": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Contact.tsx": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ContactForm.tsx": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoInput.tsx": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoPreview.tsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoTabs.tsx": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Hero.tsx": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\index.ts": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\InteractiveDemo.tsx": "68", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Process.tsx": "69", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ServicesOverview.tsx": "70", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\analytics.ts": "71", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\forms.ts": "72", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\index.ts": "73", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\sanity.ts": "74", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNav.tsx": "75", "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNewsletter.tsx": "76"}, {"size": 2067, "mtime": 1751679940296, "results": "77", "hashOfConfig": "78"}, {"size": 3626, "mtime": 1751505504335, "results": "79", "hashOfConfig": "78"}, {"size": 10127, "mtime": 1751680016774, "results": "80", "hashOfConfig": "78"}, {"size": 9659, "mtime": 1751680034032, "results": "81", "hashOfConfig": "78"}, {"size": 12228, "mtime": 1751680051540, "results": "82", "hashOfConfig": "78"}, {"size": 5797, "mtime": 1751577101014, "results": "83", "hashOfConfig": "78"}, {"size": 3587, "mtime": 1751676697710, "results": "84", "hashOfConfig": "78"}, {"size": 1922, "mtime": 1751676758569, "results": "85", "hashOfConfig": "78"}, {"size": 415, "mtime": 1751664165349, "results": "86", "hashOfConfig": "78"}, {"size": 2090, "mtime": 1751676794792, "results": "87", "hashOfConfig": "78"}, {"size": 1480, "mtime": 1751680113228, "results": "88", "hashOfConfig": "78"}, {"size": 615, "mtime": 1751680147823, "results": "89", "hashOfConfig": "78"}, {"size": 4638, "mtime": 1751680166678, "results": "90", "hashOfConfig": "78"}, {"size": 447, "mtime": 1750993163336, "results": "91", "hashOfConfig": "78"}, {"size": 4013, "mtime": 1751680290044, "results": "92", "hashOfConfig": "78"}, {"size": 4806, "mtime": 1751511499987, "results": "93", "hashOfConfig": "78"}, {"size": 315, "mtime": 1750990215277, "results": "94", "hashOfConfig": "78"}, {"size": 1715, "mtime": 1751680446533, "results": "95", "hashOfConfig": "78"}, {"size": 6904, "mtime": 1751661914782, "results": "96", "hashOfConfig": "78"}, {"size": 415, "mtime": 1750998195180, "results": "97", "hashOfConfig": "78"}, {"size": 2390, "mtime": 1751578724741, "results": "98", "hashOfConfig": "78"}, {"size": 6772, "mtime": 1751514986018, "results": "99", "hashOfConfig": "78"}, {"size": 7652, "mtime": 1750990602546, "results": "100", "hashOfConfig": "78"}, {"size": 6361, "mtime": 1750990637857, "results": "101", "hashOfConfig": "78"}, {"size": 1149, "mtime": 1751518090332, "results": "102", "hashOfConfig": "78"}, {"size": 1498, "mtime": 1751518229057, "results": "103", "hashOfConfig": "78"}, {"size": 1145, "mtime": 1751518286486, "results": "104", "hashOfConfig": "78"}, {"size": 4809, "mtime": 1751664389950, "results": "105", "hashOfConfig": "78"}, {"size": 4353, "mtime": 1751578594187, "results": "106", "hashOfConfig": "78"}, {"size": 2356, "mtime": 1751664777139, "results": "107", "hashOfConfig": "78"}, {"size": 1834, "mtime": 1751575325593, "results": "108", "hashOfConfig": "78"}, {"size": 300, "mtime": 1751575364754, "results": "109", "hashOfConfig": "78"}, {"size": 2288, "mtime": 1751679747546, "results": "110", "hashOfConfig": "78"}, {"size": 8324, "mtime": 1751662637625, "results": "111", "hashOfConfig": "78"}, {"size": 9276, "mtime": 1751662678560, "results": "112", "hashOfConfig": "78"}, {"size": 8698, "mtime": 1751662431191, "results": "113", "hashOfConfig": "78"}, {"size": 9021, "mtime": 1751662773289, "results": "114", "hashOfConfig": "78"}, {"size": 10379, "mtime": 1751662507680, "results": "115", "hashOfConfig": "78"}, {"size": 9645, "mtime": 1751662467457, "results": "116", "hashOfConfig": "78"}, {"size": 6036, "mtime": 1751670335025, "results": "117", "hashOfConfig": "78"}, {"size": 3477, "mtime": 1751515726939, "results": "118", "hashOfConfig": "78"}, {"size": 551, "mtime": 1751670473668, "results": "119", "hashOfConfig": "78"}, {"size": 5333, "mtime": 1751671766740, "results": "120", "hashOfConfig": "78"}, {"size": 6690, "mtime": 1751671807702, "results": "121", "hashOfConfig": "78"}, {"size": 5933, "mtime": 1751671826897, "results": "122", "hashOfConfig": "78"}, {"size": 5442, "mtime": 1751578759717, "results": "123", "hashOfConfig": "78"}, {"size": 6546, "mtime": 1751670381294, "results": "124", "hashOfConfig": "78"}, {"size": 169, "mtime": 1751574258441, "results": "125", "hashOfConfig": "78"}, {"size": 232, "mtime": 1751663733726, "results": "126", "hashOfConfig": "78"}, {"size": 5645, "mtime": 1751662735678, "results": "127", "hashOfConfig": "78"}, {"size": 5590, "mtime": 1751679901576, "results": "128", "hashOfConfig": "78"}, {"size": 970, "mtime": 1751679975840, "results": "129", "hashOfConfig": "78"}, {"size": 461, "mtime": 1751676842124, "results": "130", "hashOfConfig": "78"}, {"size": 2650, "mtime": 1751664824565, "results": "131", "hashOfConfig": "78"}, {"size": 1876, "mtime": 1751678801354, "results": "132", "hashOfConfig": "78"}, {"size": 2095, "mtime": 1751677507868, "results": "133", "hashOfConfig": "78"}, {"size": 626, "mtime": 1751678612776, "results": "134", "hashOfConfig": "78"}, {"size": 3132, "mtime": 1751676893042, "results": "135", "hashOfConfig": "78"}, {"size": 1762, "mtime": 1751676876473, "results": "136", "hashOfConfig": "78"}, {"size": 2443, "mtime": 1751677402067, "results": "137", "hashOfConfig": "78"}, {"size": 1852, "mtime": 1751676812928, "results": "138", "hashOfConfig": "78"}, {"size": 8619, "mtime": 1751670631570, "results": "139", "hashOfConfig": "78"}, {"size": 3659, "mtime": 1751677104382, "results": "140", "hashOfConfig": "78"}, {"size": 6781, "mtime": 1751679731048, "results": "141", "hashOfConfig": "78"}, {"size": 2584, "mtime": 1751677085119, "results": "142", "hashOfConfig": "78"}, {"size": 4130, "mtime": 1751670521401, "results": "143", "hashOfConfig": "78"}, {"size": 685, "mtime": 1751676834675, "results": "144", "hashOfConfig": "78"}, {"size": 3257, "mtime": 1751677312540, "results": "145", "hashOfConfig": "78"}, {"size": 4837, "mtime": 1751677422096, "results": "146", "hashOfConfig": "78"}, {"size": 4513, "mtime": 1751671722640, "results": "147", "hashOfConfig": "78"}, {"size": 8012, "mtime": 1751676639727, "results": "148", "hashOfConfig": "78"}, {"size": 5959, "mtime": 1751676601255, "results": "149", "hashOfConfig": "78"}, {"size": 6626, "mtime": 1751676680436, "results": "150", "hashOfConfig": "78"}, {"size": 4695, "mtime": 1751676570640, "results": "151", "hashOfConfig": "78"}, {"size": 3104, "mtime": 1751680327855, "results": "152", "hashOfConfig": "78"}, {"size": 5510, "mtime": 1751680346231, "results": "153", "hashOfConfig": "78"}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ipl2r0", {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 18, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\api\\newsletter\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\FAQClient.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\faq\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\services\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\AnimationWrapper.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ChatTrigger.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ClientOnly.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\CompanyValues.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\DarkModeToggle.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Logo.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Mission.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NewsletterSignup.tsx", ["382", "383", "384", "385", "386", "387"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NoSSR.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\OrganizationSchema.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PortableText.tsx", ["388", "389", "390"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\PricingTable.tsx", ["391"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ServicesFAQ.tsx", ["392", "393"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleDarkModeToggle.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleFloatingChat.tsx", ["394", "395", "396", "397"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleHeaderChat.tsx", ["398", "399", "400", "401"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\StructuredData.tsx", ["402", "403", "404"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\TeamProfiles.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Button.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\ui\\__tests__\\Input.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Contact.test.tsx", ["405"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\Header.test.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\InteractiveDemo.test.tsx", ["406"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\__tests__\\NewsletterSignup.test.tsx", ["407"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\config\\site.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\contexts\\ThemeContext.tsx", ["408", "409"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useAnalytics.ts", ["410", "411", "412"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useContactForm.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\hooks\\useSiteSettings.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\metadata.ts", ["413"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\sanity.ts", ["414", "415", "416"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\simple.test.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\__tests__\\test-utils.tsx", ["417", "418", "419"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\CrispChat.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\GoogleAnalytics.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\analytics\\WebVitals.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\MobileMenu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\AboutSnippet.tsx", ["420", "421", "422"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Contact.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ContactForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoInput.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoPreview.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\DemoTabs.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Hero.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\InteractiveDemo.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Process.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ServicesOverview.tsx", ["423", "424"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\analytics.ts", ["425", "426", "427", "428", "429", "430", "431", "432", "433"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\forms.ts", ["434"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\index.ts", ["435", "436", "437", "438", "439", "440", "441", "442", "443", "444", "445", "446", "447", "448", "449", "450", "451", "452"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\types\\sanity.ts", ["453", "454", "455", "456", "457", "458"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNav.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\FooterNewsletter.tsx", [], [], {"ruleId": "459", "severity": 2, "message": "460", "line": 40, "column": 57, "nodeType": "461", "messageId": "462", "endLine": 40, "endColumn": 60, "suggestions": "463"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 41, "column": 22, "nodeType": "461", "messageId": "462", "endLine": 41, "endColumn": 25, "suggestions": "464"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 67, "column": 57, "nodeType": "461", "messageId": "462", "endLine": 67, "endColumn": 60, "suggestions": "465"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 68, "column": 22, "nodeType": "461", "messageId": "462", "endLine": 68, "endColumn": 25, "suggestions": "466"}, {"ruleId": "467", "severity": 2, "message": "468", "line": 84, "column": 9, "nodeType": null, "messageId": "469", "endLine": 84, "endColumn": 24}, {"ruleId": "467", "severity": 2, "message": "470", "line": 85, "column": 9, "nodeType": null, "messageId": "469", "endLine": 85, "endColumn": 25}, {"ruleId": "467", "severity": 2, "message": "471", "line": 3, "column": 8, "nodeType": null, "messageId": "469", "endLine": 3, "endColumn": 13}, {"ruleId": "467", "severity": 2, "message": "472", "line": 4, "column": 10, "nodeType": null, "messageId": "469", "endLine": 4, "endColumn": 16}, {"ruleId": "473", "severity": 2, "message": "474", "line": 72, "column": 11, "nodeType": "475", "messageId": "476", "endLine": 72, "endColumn": 15, "fix": "477"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 92, "column": 38, "nodeType": "461", "messageId": "462", "endLine": 92, "endColumn": 41, "suggestions": "478"}, {"ruleId": "479", "severity": 2, "message": "480", "line": 116, "column": 37, "nodeType": "481", "messageId": "482", "suggestions": "483"}, {"ruleId": "484", "severity": 2, "message": "485", "line": 118, "column": 11, "nodeType": "486", "endLine": 121, "endColumn": 12}, {"ruleId": "459", "severity": 2, "message": "460", "line": 9, "column": 53, "nodeType": "461", "messageId": "462", "endLine": 9, "endColumn": 56, "suggestions": "487"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 10, "column": 18, "nodeType": "461", "messageId": "462", "endLine": 10, "endColumn": 21, "suggestions": "488"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 17, "column": 53, "nodeType": "461", "messageId": "462", "endLine": 17, "endColumn": 56, "suggestions": "489"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 18, "column": 18, "nodeType": "461", "messageId": "462", "endLine": 18, "endColumn": 21, "suggestions": "490"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 9, "column": 53, "nodeType": "461", "messageId": "462", "endLine": 9, "endColumn": 56, "suggestions": "491"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 10, "column": 18, "nodeType": "461", "messageId": "462", "endLine": 10, "endColumn": 21, "suggestions": "492"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 17, "column": 53, "nodeType": "461", "messageId": "462", "endLine": 17, "endColumn": 56, "suggestions": "493"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 18, "column": 18, "nodeType": "461", "messageId": "462", "endLine": 18, "endColumn": 21, "suggestions": "494"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 5, "column": 10, "nodeType": "461", "messageId": "462", "endLine": 5, "endColumn": 13, "suggestions": "495"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 89, "column": 47, "nodeType": "461", "messageId": "462", "endLine": 89, "endColumn": 50, "suggestions": "496"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 103, "column": 60, "nodeType": "461", "messageId": "462", "endLine": 103, "endColumn": 63, "suggestions": "497"}, {"ruleId": "467", "severity": 2, "message": "498", "line": 2, "column": 26, "nodeType": null, "messageId": "469", "endLine": 2, "endColumn": 35}, {"ruleId": "467", "severity": 2, "message": "498", "line": 2, "column": 26, "nodeType": null, "messageId": "469", "endLine": 2, "endColumn": 35}, {"ruleId": "467", "severity": 2, "message": "498", "line": 2, "column": 26, "nodeType": null, "messageId": "469", "endLine": 2, "endColumn": 35}, {"ruleId": "459", "severity": 2, "message": "460", "line": 57, "column": 53, "nodeType": "461", "messageId": "462", "endLine": 57, "endColumn": 56, "suggestions": "499"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 58, "column": 18, "nodeType": "461", "messageId": "462", "endLine": 58, "endColumn": 21, "suggestions": "500"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 17, "column": 38, "nodeType": "461", "messageId": "462", "endLine": 17, "endColumn": 41, "suggestions": "501"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 75, "column": 53, "nodeType": "461", "messageId": "462", "endLine": 75, "endColumn": 56, "suggestions": "502"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 77, "column": 20, "nodeType": "461", "messageId": "462", "endLine": 77, "endColumn": 23, "suggestions": "503"}, {"ruleId": "467", "severity": 2, "message": "504", "line": 43, "column": 9, "nodeType": null, "messageId": "469", "endLine": 43, "endColumn": 26}, {"ruleId": "459", "severity": 2, "message": "460", "line": 22, "column": 32, "nodeType": "461", "messageId": "462", "endLine": 22, "endColumn": 35, "suggestions": "505"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 62, "column": 9, "nodeType": "461", "messageId": "462", "endLine": 62, "endColumn": 12, "suggestions": "506"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 109, "column": 11, "nodeType": "461", "messageId": "462", "endLine": 109, "endColumn": 14, "suggestions": "507"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 59, "column": 37, "nodeType": "461", "messageId": "462", "endLine": 59, "endColumn": 40, "suggestions": "508"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 160, "column": 66, "nodeType": "461", "messageId": "462", "endLine": 160, "endColumn": 69, "suggestions": "509"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 177, "column": 75, "nodeType": "461", "messageId": "462", "endLine": 177, "endColumn": 78, "suggestions": "510"}, {"ruleId": "479", "severity": 2, "message": "480", "line": 23, "column": 19, "nodeType": "481", "messageId": "482", "suggestions": "511"}, {"ruleId": "479", "severity": 2, "message": "480", "line": 29, "column": 21, "nodeType": "481", "messageId": "482", "suggestions": "512"}, {"ruleId": "479", "severity": 2, "message": "480", "line": 34, "column": 68, "nodeType": "481", "messageId": "482", "suggestions": "513"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 30, "column": 53, "nodeType": "461", "messageId": "462", "endLine": 30, "endColumn": 56, "suggestions": "514"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 31, "column": 18, "nodeType": "461", "messageId": "462", "endLine": 31, "endColumn": 21, "suggestions": "515"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 11, "column": 31, "nodeType": "461", "messageId": "462", "endLine": 11, "endColumn": 34, "suggestions": "516"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 23, "column": 38, "nodeType": "461", "messageId": "462", "endLine": 23, "endColumn": 41, "suggestions": "517"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 64, "column": 38, "nodeType": "461", "messageId": "462", "endLine": 64, "endColumn": 41, "suggestions": "518"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 228, "column": 63, "nodeType": "461", "messageId": "462", "endLine": 228, "endColumn": 66, "suggestions": "519"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 229, "column": 65, "nodeType": "461", "messageId": "462", "endLine": 229, "endColumn": 68, "suggestions": "520"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 230, "column": 89, "nodeType": "461", "messageId": "462", "endLine": 230, "endColumn": 92, "suggestions": "521"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 231, "column": 88, "nodeType": "461", "messageId": "462", "endLine": 231, "endColumn": 91, "suggestions": "522"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 232, "column": 83, "nodeType": "461", "messageId": "462", "endLine": 232, "endColumn": 86, "suggestions": "523"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 233, "column": 76, "nodeType": "461", "messageId": "462", "endLine": 233, "endColumn": 79, "suggestions": "524"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "525", "line": 184, "column": 14}, {"ruleId": "459", "severity": 2, "message": "460", "line": 98, "column": 34, "nodeType": "461", "messageId": "462", "endLine": 98, "endColumn": 37, "suggestions": "526"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 106, "column": 40, "nodeType": "461", "messageId": "462", "endLine": 106, "endColumn": 43, "suggestions": "527"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 121, "column": 13, "nodeType": "461", "messageId": "462", "endLine": 121, "endColumn": 16, "suggestions": "528"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 135, "column": 33, "nodeType": "461", "messageId": "462", "endLine": 135, "endColumn": 36, "suggestions": "529"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 160, "column": 35, "nodeType": "461", "messageId": "462", "endLine": 160, "endColumn": 38, "suggestions": "530"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 164, "column": 27, "nodeType": "461", "messageId": "462", "endLine": 164, "endColumn": 30, "suggestions": "531"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 208, "column": 13, "nodeType": "461", "messageId": "462", "endLine": 208, "endColumn": 16, "suggestions": "532"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 209, "column": 13, "nodeType": "461", "messageId": "462", "endLine": 209, "endColumn": 16, "suggestions": "533"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 210, "column": 10, "nodeType": "461", "messageId": "462", "endLine": 210, "endColumn": 13, "suggestions": "534"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 212, "column": 29, "nodeType": "461", "messageId": "462", "endLine": 212, "endColumn": 32, "suggestions": "535"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 213, "column": 16, "nodeType": "461", "messageId": "462", "endLine": 213, "endColumn": 19, "suggestions": "536"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 214, "column": 14, "nodeType": "461", "messageId": "462", "endLine": 214, "endColumn": 17, "suggestions": "537"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 215, "column": 17, "nodeType": "461", "messageId": "462", "endLine": 215, "endColumn": 20, "suggestions": "538"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 238, "column": 31, "nodeType": "461", "messageId": "462", "endLine": 238, "endColumn": 34, "suggestions": "539"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 239, "column": 31, "nodeType": "461", "messageId": "462", "endLine": 239, "endColumn": 34, "suggestions": "540"}, {"ruleId": "541", "severity": 2, "message": "542", "line": 242, "column": 35, "nodeType": "543", "messageId": "544", "endLine": 242, "endColumn": 37, "suggestions": "545"}, {"ruleId": "541", "severity": 2, "message": "542", "line": 243, "column": 39, "nodeType": "543", "messageId": "544", "endLine": 243, "endColumn": 41, "suggestions": "546"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 284, "column": 31, "nodeType": "461", "messageId": "462", "endLine": 284, "endColumn": 34, "suggestions": "547"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 69, "column": 13, "nodeType": "461", "messageId": "462", "endLine": 69, "endColumn": 16, "suggestions": "548"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 95, "column": 11, "nodeType": "461", "messageId": "462", "endLine": 95, "endColumn": 14, "suggestions": "549"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 116, "column": 9, "nodeType": "461", "messageId": "462", "endLine": 116, "endColumn": 12, "suggestions": "550"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 134, "column": 21, "nodeType": "461", "messageId": "462", "endLine": 134, "endColumn": 24, "suggestions": "551"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 173, "column": 13, "nodeType": "461", "messageId": "462", "endLine": 173, "endColumn": 16, "suggestions": "552"}, {"ruleId": "459", "severity": 2, "message": "460", "line": 223, "column": 13, "nodeType": "461", "messageId": "462", "endLine": 223, "endColumn": 16, "suggestions": "553"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["554", "555"], ["556", "557"], ["558", "559"], ["560", "561"], "@typescript-eslint/no-unused-vars", "'isFooterVariant' is assigned a value but never used.", "unusedVar", "'isSectionVariant' is assigned a value but never used.", "'Image' is defined but never used.", "'urlFor' is defined but never used.", "prefer-const", "'text' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "562", "text": "563"}, ["564", "565"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["566", "567", "568", "569"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", ["570", "571"], ["572", "573"], ["574", "575"], ["576", "577"], ["578", "579"], ["580", "581"], ["582", "583"], ["584", "585"], ["586", "587"], ["588", "589"], ["590", "591"], "'fireEvent' is defined but never used.", ["592", "593"], ["594", "595"], ["596", "597"], ["598", "599"], ["600", "601"], "'keywordsToInclude' is assigned a value but never used.", ["602", "603"], ["604", "605"], ["606", "607"], ["608", "609"], ["610", "611"], ["612", "613"], ["614", "615", "616", "617"], ["618", "619", "620", "621"], ["622", "623", "624", "625"], ["626", "627"], ["628", "629"], ["630", "631"], ["632", "633"], ["634", "635"], ["636", "637"], ["638", "639"], ["640", "641"], ["642", "643"], ["644", "645"], ["646", "647"], "Parsing error: ']' expected.", ["648", "649"], ["650", "651"], ["652", "653"], ["654", "655"], ["656", "657"], ["658", "659"], ["660", "661"], ["662", "663"], ["664", "665"], ["666", "667"], ["668", "669"], ["670", "671"], ["672", "673"], ["674", "675"], ["676", "677"], "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["678", "679"], ["680", "681"], ["682", "683"], ["684", "685"], ["686", "687"], ["688", "689"], ["690", "691"], ["692", "693"], ["694", "695"], {"messageId": "696", "fix": "697", "desc": "698"}, {"messageId": "699", "fix": "700", "desc": "701"}, {"messageId": "696", "fix": "702", "desc": "698"}, {"messageId": "699", "fix": "703", "desc": "701"}, {"messageId": "696", "fix": "704", "desc": "698"}, {"messageId": "699", "fix": "705", "desc": "701"}, {"messageId": "696", "fix": "706", "desc": "698"}, {"messageId": "699", "fix": "707", "desc": "701"}, [1800, 1821], "const text = span.text;", {"messageId": "696", "fix": "708", "desc": "698"}, {"messageId": "699", "fix": "709", "desc": "701"}, {"messageId": "710", "data": "711", "fix": "712", "desc": "713"}, {"messageId": "710", "data": "714", "fix": "715", "desc": "716"}, {"messageId": "710", "data": "717", "fix": "718", "desc": "719"}, {"messageId": "710", "data": "720", "fix": "721", "desc": "722"}, {"messageId": "696", "fix": "723", "desc": "698"}, {"messageId": "699", "fix": "724", "desc": "701"}, {"messageId": "696", "fix": "725", "desc": "698"}, {"messageId": "699", "fix": "726", "desc": "701"}, {"messageId": "696", "fix": "727", "desc": "698"}, {"messageId": "699", "fix": "728", "desc": "701"}, {"messageId": "696", "fix": "729", "desc": "698"}, {"messageId": "699", "fix": "730", "desc": "701"}, {"messageId": "696", "fix": "731", "desc": "698"}, {"messageId": "699", "fix": "732", "desc": "701"}, {"messageId": "696", "fix": "733", "desc": "698"}, {"messageId": "699", "fix": "734", "desc": "701"}, {"messageId": "696", "fix": "735", "desc": "698"}, {"messageId": "699", "fix": "736", "desc": "701"}, {"messageId": "696", "fix": "737", "desc": "698"}, {"messageId": "699", "fix": "738", "desc": "701"}, {"messageId": "696", "fix": "739", "desc": "698"}, {"messageId": "699", "fix": "740", "desc": "701"}, {"messageId": "696", "fix": "741", "desc": "698"}, {"messageId": "699", "fix": "742", "desc": "701"}, {"messageId": "696", "fix": "743", "desc": "698"}, {"messageId": "699", "fix": "744", "desc": "701"}, {"messageId": "696", "fix": "745", "desc": "698"}, {"messageId": "699", "fix": "746", "desc": "701"}, {"messageId": "696", "fix": "747", "desc": "698"}, {"messageId": "699", "fix": "748", "desc": "701"}, {"messageId": "696", "fix": "749", "desc": "698"}, {"messageId": "699", "fix": "750", "desc": "701"}, {"messageId": "696", "fix": "751", "desc": "698"}, {"messageId": "699", "fix": "752", "desc": "701"}, {"messageId": "696", "fix": "753", "desc": "698"}, {"messageId": "699", "fix": "754", "desc": "701"}, {"messageId": "696", "fix": "755", "desc": "698"}, {"messageId": "699", "fix": "756", "desc": "701"}, {"messageId": "696", "fix": "757", "desc": "698"}, {"messageId": "699", "fix": "758", "desc": "701"}, {"messageId": "696", "fix": "759", "desc": "698"}, {"messageId": "699", "fix": "760", "desc": "701"}, {"messageId": "696", "fix": "761", "desc": "698"}, {"messageId": "699", "fix": "762", "desc": "701"}, {"messageId": "696", "fix": "763", "desc": "698"}, {"messageId": "699", "fix": "764", "desc": "701"}, {"messageId": "696", "fix": "765", "desc": "698"}, {"messageId": "699", "fix": "766", "desc": "701"}, {"messageId": "710", "data": "767", "fix": "768", "desc": "713"}, {"messageId": "710", "data": "769", "fix": "770", "desc": "716"}, {"messageId": "710", "data": "771", "fix": "772", "desc": "719"}, {"messageId": "710", "data": "773", "fix": "774", "desc": "722"}, {"messageId": "710", "data": "775", "fix": "776", "desc": "713"}, {"messageId": "710", "data": "777", "fix": "778", "desc": "716"}, {"messageId": "710", "data": "779", "fix": "780", "desc": "719"}, {"messageId": "710", "data": "781", "fix": "782", "desc": "722"}, {"messageId": "710", "data": "783", "fix": "784", "desc": "713"}, {"messageId": "710", "data": "785", "fix": "786", "desc": "716"}, {"messageId": "710", "data": "787", "fix": "788", "desc": "719"}, {"messageId": "710", "data": "789", "fix": "790", "desc": "722"}, {"messageId": "696", "fix": "791", "desc": "698"}, {"messageId": "699", "fix": "792", "desc": "701"}, {"messageId": "696", "fix": "793", "desc": "698"}, {"messageId": "699", "fix": "794", "desc": "701"}, {"messageId": "696", "fix": "795", "desc": "698"}, {"messageId": "699", "fix": "796", "desc": "701"}, {"messageId": "696", "fix": "797", "desc": "698"}, {"messageId": "699", "fix": "798", "desc": "701"}, {"messageId": "696", "fix": "799", "desc": "698"}, {"messageId": "699", "fix": "800", "desc": "701"}, {"messageId": "696", "fix": "801", "desc": "698"}, {"messageId": "699", "fix": "802", "desc": "701"}, {"messageId": "696", "fix": "803", "desc": "698"}, {"messageId": "699", "fix": "804", "desc": "701"}, {"messageId": "696", "fix": "805", "desc": "698"}, {"messageId": "699", "fix": "806", "desc": "701"}, {"messageId": "696", "fix": "807", "desc": "698"}, {"messageId": "699", "fix": "808", "desc": "701"}, {"messageId": "696", "fix": "809", "desc": "698"}, {"messageId": "699", "fix": "810", "desc": "701"}, {"messageId": "696", "fix": "811", "desc": "698"}, {"messageId": "699", "fix": "812", "desc": "701"}, {"messageId": "696", "fix": "813", "desc": "698"}, {"messageId": "699", "fix": "814", "desc": "701"}, {"messageId": "696", "fix": "815", "desc": "698"}, {"messageId": "699", "fix": "816", "desc": "701"}, {"messageId": "696", "fix": "817", "desc": "698"}, {"messageId": "699", "fix": "818", "desc": "701"}, {"messageId": "696", "fix": "819", "desc": "698"}, {"messageId": "699", "fix": "820", "desc": "701"}, {"messageId": "696", "fix": "821", "desc": "698"}, {"messageId": "699", "fix": "822", "desc": "701"}, {"messageId": "696", "fix": "823", "desc": "698"}, {"messageId": "699", "fix": "824", "desc": "701"}, {"messageId": "696", "fix": "825", "desc": "698"}, {"messageId": "699", "fix": "826", "desc": "701"}, {"messageId": "696", "fix": "827", "desc": "698"}, {"messageId": "699", "fix": "828", "desc": "701"}, {"messageId": "696", "fix": "829", "desc": "698"}, {"messageId": "699", "fix": "830", "desc": "701"}, {"messageId": "696", "fix": "831", "desc": "698"}, {"messageId": "699", "fix": "832", "desc": "701"}, {"messageId": "696", "fix": "833", "desc": "698"}, {"messageId": "699", "fix": "834", "desc": "701"}, {"messageId": "696", "fix": "835", "desc": "698"}, {"messageId": "699", "fix": "836", "desc": "701"}, {"messageId": "696", "fix": "837", "desc": "698"}, {"messageId": "699", "fix": "838", "desc": "701"}, {"messageId": "696", "fix": "839", "desc": "698"}, {"messageId": "699", "fix": "840", "desc": "701"}, {"messageId": "696", "fix": "841", "desc": "698"}, {"messageId": "699", "fix": "842", "desc": "701"}, {"messageId": "843", "data": "844", "fix": "845", "desc": "846"}, {"messageId": "843", "data": "847", "fix": "848", "desc": "849"}, {"messageId": "843", "data": "850", "fix": "851", "desc": "846"}, {"messageId": "843", "data": "852", "fix": "853", "desc": "849"}, {"messageId": "696", "fix": "854", "desc": "698"}, {"messageId": "699", "fix": "855", "desc": "701"}, {"messageId": "696", "fix": "856", "desc": "698"}, {"messageId": "699", "fix": "857", "desc": "701"}, {"messageId": "696", "fix": "858", "desc": "698"}, {"messageId": "699", "fix": "859", "desc": "701"}, {"messageId": "696", "fix": "860", "desc": "698"}, {"messageId": "699", "fix": "861", "desc": "701"}, {"messageId": "696", "fix": "862", "desc": "698"}, {"messageId": "699", "fix": "863", "desc": "701"}, {"messageId": "696", "fix": "864", "desc": "698"}, {"messageId": "699", "fix": "865", "desc": "701"}, {"messageId": "696", "fix": "866", "desc": "698"}, {"messageId": "699", "fix": "867", "desc": "701"}, "suggestUnknown", {"range": "868", "text": "869"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "870", "text": "871"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "872", "text": "869"}, {"range": "873", "text": "871"}, {"range": "874", "text": "869"}, {"range": "875", "text": "871"}, {"range": "876", "text": "869"}, {"range": "877", "text": "871"}, {"range": "878", "text": "869"}, {"range": "879", "text": "871"}, "replaceWithAlt", {"alt": "880"}, {"range": "881", "text": "882"}, "Replace with `&apos;`.", {"alt": "883"}, {"range": "884", "text": "885"}, "Replace with `&lsquo;`.", {"alt": "886"}, {"range": "887", "text": "888"}, "Replace with `&#39;`.", {"alt": "889"}, {"range": "890", "text": "891"}, "Replace with `&rsquo;`.", {"range": "892", "text": "869"}, {"range": "893", "text": "871"}, {"range": "894", "text": "869"}, {"range": "895", "text": "871"}, {"range": "896", "text": "869"}, {"range": "897", "text": "871"}, {"range": "898", "text": "869"}, {"range": "899", "text": "871"}, {"range": "900", "text": "869"}, {"range": "901", "text": "871"}, {"range": "902", "text": "869"}, {"range": "903", "text": "871"}, {"range": "904", "text": "869"}, {"range": "905", "text": "871"}, {"range": "906", "text": "869"}, {"range": "907", "text": "871"}, {"range": "908", "text": "869"}, {"range": "909", "text": "871"}, {"range": "910", "text": "869"}, {"range": "911", "text": "871"}, {"range": "912", "text": "869"}, {"range": "913", "text": "871"}, {"range": "914", "text": "869"}, {"range": "915", "text": "871"}, {"range": "916", "text": "869"}, {"range": "917", "text": "871"}, {"range": "918", "text": "869"}, {"range": "919", "text": "871"}, {"range": "920", "text": "869"}, {"range": "921", "text": "871"}, {"range": "922", "text": "869"}, {"range": "923", "text": "871"}, {"range": "924", "text": "869"}, {"range": "925", "text": "871"}, {"range": "926", "text": "869"}, {"range": "927", "text": "871"}, {"range": "928", "text": "869"}, {"range": "929", "text": "871"}, {"range": "930", "text": "869"}, {"range": "931", "text": "871"}, {"range": "932", "text": "869"}, {"range": "933", "text": "871"}, {"range": "934", "text": "869"}, {"range": "935", "text": "871"}, {"alt": "880"}, {"range": "936", "text": "937"}, {"alt": "883"}, {"range": "938", "text": "939"}, {"alt": "886"}, {"range": "940", "text": "941"}, {"alt": "889"}, {"range": "942", "text": "943"}, {"alt": "880"}, {"range": "944", "text": "945"}, {"alt": "883"}, {"range": "946", "text": "947"}, {"alt": "886"}, {"range": "948", "text": "949"}, {"alt": "889"}, {"range": "950", "text": "951"}, {"alt": "880"}, {"range": "952", "text": "953"}, {"alt": "883"}, {"range": "954", "text": "955"}, {"alt": "886"}, {"range": "956", "text": "957"}, {"alt": "889"}, {"range": "958", "text": "959"}, {"range": "960", "text": "869"}, {"range": "961", "text": "871"}, {"range": "962", "text": "869"}, {"range": "963", "text": "871"}, {"range": "964", "text": "869"}, {"range": "965", "text": "871"}, {"range": "966", "text": "869"}, {"range": "967", "text": "871"}, {"range": "968", "text": "869"}, {"range": "969", "text": "871"}, {"range": "970", "text": "869"}, {"range": "971", "text": "871"}, {"range": "972", "text": "869"}, {"range": "973", "text": "871"}, {"range": "974", "text": "869"}, {"range": "975", "text": "871"}, {"range": "976", "text": "869"}, {"range": "977", "text": "871"}, {"range": "978", "text": "869"}, {"range": "979", "text": "871"}, {"range": "980", "text": "869"}, {"range": "981", "text": "871"}, {"range": "982", "text": "869"}, {"range": "983", "text": "871"}, {"range": "984", "text": "869"}, {"range": "985", "text": "871"}, {"range": "986", "text": "869"}, {"range": "987", "text": "871"}, {"range": "988", "text": "869"}, {"range": "989", "text": "871"}, {"range": "990", "text": "869"}, {"range": "991", "text": "871"}, {"range": "992", "text": "869"}, {"range": "993", "text": "871"}, {"range": "994", "text": "869"}, {"range": "995", "text": "871"}, {"range": "996", "text": "869"}, {"range": "997", "text": "871"}, {"range": "998", "text": "869"}, {"range": "999", "text": "871"}, {"range": "1000", "text": "869"}, {"range": "1001", "text": "871"}, {"range": "1002", "text": "869"}, {"range": "1003", "text": "871"}, {"range": "1004", "text": "869"}, {"range": "1005", "text": "871"}, {"range": "1006", "text": "869"}, {"range": "1007", "text": "871"}, {"range": "1008", "text": "869"}, {"range": "1009", "text": "871"}, {"range": "1010", "text": "869"}, {"range": "1011", "text": "871"}, "replaceEmptyObjectType", {"replacement": "1012"}, {"range": "1013", "text": "1012"}, "Replace `{}` with `object`.", {"replacement": "869"}, {"range": "1014", "text": "869"}, "Replace `{}` with `unknown`.", {"replacement": "1012"}, {"range": "1015", "text": "1012"}, {"replacement": "869"}, {"range": "1016", "text": "869"}, {"range": "1017", "text": "869"}, {"range": "1018", "text": "871"}, {"range": "1019", "text": "869"}, {"range": "1020", "text": "871"}, {"range": "1021", "text": "869"}, {"range": "1022", "text": "871"}, {"range": "1023", "text": "869"}, {"range": "1024", "text": "871"}, {"range": "1025", "text": "869"}, {"range": "1026", "text": "871"}, {"range": "1027", "text": "869"}, {"range": "1028", "text": "871"}, {"range": "1029", "text": "869"}, {"range": "1030", "text": "871"}, [1270, 1273], "unknown", [1270, 1273], "never", [1304, 1307], [1304, 1307], [2041, 2044], [2041, 2044], [2075, 2078], [2075, 2078], [2046, 2049], [2046, 2049], "&apos;", [5965, 6029], "\n            Still have questions? We&apos;d love to help!\n          ", "&lsquo;", [5965, 6029], "\n            Still have questions? We&lsquo;d love to help!\n          ", "&#39;", [5965, 6029], "\n            Still have questions? We&#39;d love to help!\n          ", "&rsquo;", [5965, 6029], "\n            Still have questions? We&rsquo;d love to help!\n          ", [262, 265], [262, 265], [294, 297], [294, 297], [584, 587], [584, 587], [614, 617], [614, 617], [260, 263], [260, 263], [292, 295], [292, 295], [582, 585], [582, 585], [612, 615], [612, 615], [147, 150], [147, 150], [3058, 3061], [3058, 3061], [3462, 3465], [3462, 3465], [1562, 1565], [1562, 1565], [1592, 1595], [1592, 1595], [413, 416], [413, 416], [2042, 2045], [2042, 2045], [2086, 2089], [2086, 2089], [750, 753], [750, 753], [1488, 1491], [1488, 1491], [2440, 2443], [2440, 2443], [1495, 1498], [1495, 1498], [4096, 4099], [4096, 4099], [4651, 4654], [4651, 4654], [846, 909], "\n                We&apos;re More Than Just Developers\n              ", [846, 909], "\n                We&lsquo;re More Than Just Developers\n              ", [846, 909], "\n                We&#39;re More Than Just Develo<PERSON>\n              ", [846, 909], "\n                We&rsquo;re More Than Just Developers\n              ", [1039, 1311], "\n                  At Mobilify, we believe that every great idea deserves to become reality.\n                  We&apos;re passionate about helping founders and businesses succeed by removing\n                  the traditional barriers to mobile app development.\n                ", [1039, 1311], "\n                  At Mobilify, we believe that every great idea deserves to become reality.\n                  We&lsquo;re passionate about helping founders and businesses succeed by removing\n                  the traditional barriers to mobile app development.\n                ", [1039, 1311], "\n                  At Mobilify, we believe that every great idea deserves to become reality.\n                  We&#39;re passionate about helping founders and businesses succeed by removing\n                  the traditional barriers to mobile app development.\n                ", [1039, 1311], "\n                  At Mobilify, we believe that every great idea deserves to become reality.\n                  We&rsquo;re passionate about helping founders and businesses succeed by removing\n                  the traditional barriers to mobile app development.\n                ", [1336, 1678], "\n                  Our commitment goes beyond just writing code – we&apos;re your partners in\n                  bringing your vision to life. We focus on quality over quantity, ensuring\n                  each app we create is crafted with care, attention to detail, and a deep\n                  understanding of your unique needs.\n                ", [1336, 1678], "\n                  Our commitment goes beyond just writing code – we&lsquo;re your partners in\n                  bringing your vision to life. We focus on quality over quantity, ensuring\n                  each app we create is crafted with care, attention to detail, and a deep\n                  understanding of your unique needs.\n                ", [1336, 1678], "\n                  Our commitment goes beyond just writing code – we&#39;re your partners in\n                  bringing your vision to life. We focus on quality over quantity, ensuring\n                  each app we create is crafted with care, attention to detail, and a deep\n                  understanding of your unique needs.\n                ", [1336, 1678], "\n                  Our commitment goes beyond just writing code – we&rsquo;re your partners in\n                  bringing your vision to life. We focus on quality over quantity, ensuring\n                  each app we create is crafted with care, attention to detail, and a deep\n                  understanding of your unique needs.\n                ", [830, 833], [830, 833], [860, 863], [860, 863], [316, 319], [316, 319], [550, 553], [550, 553], [1535, 1538], [1535, 1538], [6252, 6255], [6252, 6255], [6331, 6334], [6331, 6334], [6434, 6437], [6434, 6437], [6536, 6539], [6536, 6539], [6633, 6636], [6633, 6636], [6723, 6726], [6723, 6726], [2501, 2504], [2501, 2504], [2643, 2646], [2643, 2646], [2913, 2916], [2913, 2916], [3206, 3209], [3206, 3209], [3641, 3644], [3641, 3644], [3722, 3725], [3722, 3725], [4664, 4667], [4664, 4667], [4681, 4684], [4681, 4684], [4695, 4698], [4695, 4698], [4760, 4763], [4760, 4763], [4781, 4784], [4781, 4784], [4799, 4802], [4799, 4802], [4820, 4823], [4820, 4823], [5348, 5351], [5348, 5351], [5405, 5408], [5405, 5408], "object", [5520, 5522], [5520, 5522], [5600, 5602], [5600, 5602], [6406, 6409], [6406, 6409], [1494, 1497], [1494, 1497], [1981, 1984], [1981, 1984], [2376, 2379], [2376, 2379], [2716, 2719], [2716, 2719], [3452, 3455], [3452, 3455], [4330, 4333], [4330, 4333]]