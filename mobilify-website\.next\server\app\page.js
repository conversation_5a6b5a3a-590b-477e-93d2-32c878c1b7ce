(()=>{var e={};e.id=974,e.ids=[974],e.modules={2022:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\sections\\\\InteractiveDemo.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\InteractiveDemo.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6708:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\SimpleFloatingChat.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleFloatingChat.tsx","default")},6809:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let s=r(37413),o=r(61120),a=r(38922),i=r(95047);function l(e){return{default:e&&"default"in e?e.default:e}}let n={loader:()=>Promise.resolve(l(()=>null)),loading:null,ssr:!0},d=function(e){let t={...n,...e},r=(0,o.lazy)(()=>t.loader().then(l)),d=t.loading;function c(e){let l=d?(0,s.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,n=!t.ssr||!!t.loading,c=n?o.Suspense:o.Fragment,m=t.ssr?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.PreloadChunks,{moduleIds:t.modules}),(0,s.jsx)(r,{...e})]}):(0,s.jsx)(a.BailoutToCSR,{reason:"next/dynamic",children:(0,s.jsx)(r,{...e})});return(0,s.jsx)(c,{...n?{fallback:l}:{},children:m})}return c.displayName="LoadableComponent",c}},7382:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,38922,23)),Promise.resolve().then(r.t.bind(r,95047,23)),Promise.resolve().then(r.bind(r,84712)),Promise.resolve().then(r.bind(r,68926)),Promise.resolve().then(r.bind(r,68367)),Promise.resolve().then(r.bind(r,37463)),Promise.resolve().then(r.bind(r,9769)),Promise.resolve().then(r.bind(r,94759)),Promise.resolve().then(r.bind(r,8595)),Promise.resolve().then(r.bind(r,2022)),Promise.resolve().then(r.bind(r,43712)),Promise.resolve().then(r.bind(r,73814)),Promise.resolve().then(r.bind(r,6708))},8595:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\sections\\\\Hero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Hero.tsx","default")},9769:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\sections\\\\AboutSnippet.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\AboutSnippet.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},17581:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},19006:(e,t,r)=>{Promise.resolve().then(r.bind(r,56780)),Promise.resolve().then(r.bind(r,64777)),Promise.resolve().then(r.bind(r,56857)),Promise.resolve().then(r.bind(r,99537)),Promise.resolve().then(r.bind(r,91477)),Promise.resolve().then(r.bind(r,47009)),Promise.resolve().then(r.bind(r,68175)),Promise.resolve().then(r.bind(r,69430)),Promise.resolve().then(r.bind(r,36481)),Promise.resolve().then(r.bind(r,82196)),Promise.resolve().then(r.bind(r,97275)),Promise.resolve().then(r.bind(r,34185)),Promise.resolve().then(r.bind(r,33430))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19587:(e,t)=>{"use strict";function r(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return r}})},21820:e=>{"use strict";e.exports=require("os")},21910:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(37413),o=r(67113),a=r.n(o),i=r(68926),l=r(8595),n=r(37463);let d=a()(()=>Promise.resolve().then(r.bind(r,2022)),{loadableGenerated:{modules:["app\\page.tsx -> ../components/sections/InteractiveDemo"]},loading:()=>(0,s.jsx)("div",{className:"h-96 bg-gray-50 animate-pulse"})}),c=a()(()=>Promise.resolve().then(r.bind(r,73814)),{loadableGenerated:{modules:["app\\page.tsx -> ../components/sections/ServicesOverview"]},loading:()=>(0,s.jsx)("div",{className:"h-96 bg-gray-50 animate-pulse"})}),m=a()(()=>Promise.resolve().then(r.bind(r,43712)),{loadableGenerated:{modules:["app\\page.tsx -> ../components/sections/Process"]},loading:()=>(0,s.jsx)("div",{className:"h-96 bg-gray-50 animate-pulse"})}),p=a()(()=>Promise.resolve().then(r.bind(r,9769)),{loadableGenerated:{modules:["app\\page.tsx -> ../components/sections/AboutSnippet"]},loading:()=>(0,s.jsx)("div",{className:"h-64 bg-gray-50 animate-pulse"})}),u=a()(()=>Promise.resolve().then(r.bind(r,68367)),{loadableGenerated:{modules:["app\\page.tsx -> ../components/NewsletterSignup"]},loading:()=>(0,s.jsx)("div",{className:"h-32 bg-electric-blue animate-pulse"})}),x=a()(()=>Promise.resolve().then(r.bind(r,94759)),{loadableGenerated:{modules:["app\\page.tsx -> ../components/sections/Contact"]},loading:()=>(0,s.jsx)("div",{className:"h-96 bg-white animate-pulse"})}),b=a()(()=>Promise.resolve().then(r.bind(r,84712)),{loadableGenerated:{modules:["app\\page.tsx -> ../components/layout/Footer"]},loading:()=>(0,s.jsx)("div",{className:"h-64 bg-gray-900 animate-pulse"})}),h=a()(()=>Promise.resolve().then(r.bind(r,6708)),{loadableGenerated:{modules:["app\\page.tsx -> ../components/SimpleFloatingChat"]},loading:()=>null});function g(){return(0,s.jsxs)("div",{className:"min-h-screen w-full overflow-x-hidden",children:[(0,s.jsx)(i.default,{}),(0,s.jsxs)("main",{className:"w-full",children:[(0,s.jsx)(l.default,{}),(0,s.jsx)(d,{}),(0,s.jsx)(c,{}),(0,s.jsx)(m,{}),(0,s.jsx)(p,{}),(0,s.jsx)(u,{variant:"section"}),(0,s.jsx)(x,{})]}),(0,s.jsx)(b,{}),(0,s.jsx)(n.default,{children:(0,s.jsx)(h,{})})]})}},25334:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31190:(e,t,r)=>{"use strict";r.d(t,{$n:()=>eb,Zp:()=>ev,Wu:()=>ey,pd:()=>eg});var s=r(60687),o=r(43210),a=r.n(o);let i=e=>{let t=c(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:s}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),l(r,t)||d(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&s[e]?[...o,...s[e]]:o}}},l=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],s=t.nextPart.get(r),o=s?l(e.slice(1),s):void 0;if(o)return o;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},n=/^\[(.+)\]$/,d=e=>{if(n.test(e)){let t=n.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},c=e=>{let{theme:t,classGroups:r}=e,s={nextPart:new Map,validators:[]};for(let e in r)m(r[e],s,e,t);return s},m=(e,t,r,s)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:p(t,e)).classGroupId=r;return}if("function"==typeof e)return u(e)?void m(e(s),t,r,s):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,o])=>{m(o,p(t,e),r,s)})})},p=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},u=e=>e.isThemeGetter,x=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,s=new Map,o=(o,a)=>{r.set(o,a),++t>e&&(t=0,s=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=s.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},b=e=>{let{prefix:t,experimentalParseClassName:r}=e,s=e=>{let t,r=[],s=0,o=0,a=0;for(let i=0;i<e.length;i++){let l=e[i];if(0===s&&0===o){if(":"===l){r.push(e.slice(a,i)),a=i+1;continue}if("/"===l){t=i;continue}}"["===l?s++:"]"===l?s--:"("===l?o++:")"===l&&o--}let i=0===r.length?e:e.substring(a),l=h(i);return{modifiers:r,hasImportantModifier:l!==i,baseClassName:l,maybePostfixModifierPosition:t&&t>a?t-a:void 0}};if(t){let e=t+":",r=s;s=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=s;s=t=>r({className:t,parseClassName:e})}return s},h=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,g=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],s=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...s.sort(),e),s=[]):s.push(e)}),r.push(...s.sort()),r}},f=e=>({cache:x(e.cacheSize),parseClassName:b(e),sortModifiers:g(e),...i(e)}),y=/\s+/,v=(e,t)=>{let{parseClassName:r,getClassGroupId:s,getConflictingClassGroupIds:o,sortModifiers:a}=t,i=[],l=e.trim().split(y),n="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{isExternal:d,modifiers:c,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:u}=r(t);if(d){n=t+(n.length>0?" "+n:n);continue}let x=!!u,b=s(x?p.substring(0,u):p);if(!b){if(!x||!(b=s(p))){n=t+(n.length>0?" "+n:n);continue}x=!1}let h=a(c).join(":"),g=m?h+"!":h,f=g+b;if(i.includes(f))continue;i.push(f);let y=o(b,x);for(let e=0;e<y.length;++e){let t=y[e];i.push(g+t)}n=t+(n.length>0?" "+n:n)}return n};function w(){let e,t,r=0,s="";for(;r<arguments.length;)(e=arguments[r++])&&(t=k(e))&&(s&&(s+=" "),s+=t);return s}let k=e=>{let t;if("string"==typeof e)return e;let r="";for(let s=0;s<e.length;s++)e[s]&&(t=k(e[s]))&&(r&&(r+=" "),r+=t);return r},j=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},N=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,C=/^\((?:(\w[\w-]*):)?(.+)\)$/i,P=/^\d+\/\d+$/,M=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,z=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,A=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,D=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,O=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,I=e=>P.test(e),S=e=>!!e&&!Number.isNaN(Number(e)),_=e=>!!e&&Number.isInteger(Number(e)),U=e=>e.endsWith("%")&&S(e.slice(0,-1)),q=e=>M.test(e),T=()=>!0,E=e=>z.test(e)&&!A.test(e),$=()=>!1,R=e=>D.test(e),G=e=>O.test(e),F=e=>!B(e)&&!X(e),V=e=>es(e,el,$),B=e=>N.test(e),H=e=>es(e,en,E),W=e=>es(e,ed,S),L=e=>es(e,ea,$),Z=e=>es(e,ei,G),Y=e=>es(e,em,R),X=e=>C.test(e),K=e=>eo(e,en),J=e=>eo(e,ec),Q=e=>eo(e,ea),ee=e=>eo(e,el),et=e=>eo(e,ei),er=e=>eo(e,em,!0),es=(e,t,r)=>{let s=N.exec(e);return!!s&&(s[1]?t(s[1]):r(s[2]))},eo=(e,t,r=!1)=>{let s=C.exec(e);return!!s&&(s[1]?t(s[1]):r)},ea=e=>"position"===e||"percentage"===e,ei=e=>"image"===e||"url"===e,el=e=>"length"===e||"size"===e||"bg-size"===e,en=e=>"length"===e,ed=e=>"number"===e,ec=e=>"family-name"===e,em=e=>"shadow"===e;Symbol.toStringTag;let ep=function(e,...t){let r,s,o,a=function(l){return s=(r=f(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,a=i,i(l)};function i(e){let t=s(e);if(t)return t;let a=v(e,r);return o(e,a),a}return function(){return a(w.apply(null,arguments))}}(()=>{let e=j("color"),t=j("font"),r=j("text"),s=j("font-weight"),o=j("tracking"),a=j("leading"),i=j("breakpoint"),l=j("container"),n=j("spacing"),d=j("radius"),c=j("shadow"),m=j("inset-shadow"),p=j("text-shadow"),u=j("drop-shadow"),x=j("blur"),b=j("perspective"),h=j("aspect"),g=j("ease"),f=j("animate"),y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],v=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...v(),X,B],k=()=>["auto","hidden","clip","visible","scroll"],N=()=>["auto","contain","none"],C=()=>[X,B,n],P=()=>[I,"full","auto",...C()],M=()=>[_,"none","subgrid",X,B],z=()=>["auto",{span:["full",_,X,B]},_,X,B],A=()=>[_,"auto",X,B],D=()=>["auto","min","max","fr",X,B],O=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],E=()=>["start","end","center","stretch","center-safe","end-safe"],$=()=>["auto",...C()],R=()=>[I,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...C()],G=()=>[e,X,B],es=()=>[...v(),Q,L,{position:[X,B]}],eo=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",ee,V,{size:[X,B]}],ei=()=>[U,K,H],el=()=>["","none","full",d,X,B],en=()=>["",S,K,H],ed=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],em=()=>[S,U,Q,L],ep=()=>["","none",x,X,B],eu=()=>["none",S,X,B],ex=()=>["none",S,X,B],eb=()=>[S,X,B],eh=()=>[I,"full",...C()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[q],breakpoint:[q],color:[T],container:[q],"drop-shadow":[q],ease:["in","out","in-out"],font:[F],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[q],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[q],shadow:[q],spacing:["px",S],text:[q],"text-shadow":[q],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",I,B,X,h]}],container:["container"],columns:[{columns:[S,B,X,l]}],"break-after":[{"break-after":y()}],"break-before":[{"break-before":y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:k()}],"overflow-x":[{"overflow-x":k()}],"overflow-y":[{"overflow-y":k()}],overscroll:[{overscroll:N()}],"overscroll-x":[{"overscroll-x":N()}],"overscroll-y":[{"overscroll-y":N()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:P()}],"inset-x":[{"inset-x":P()}],"inset-y":[{"inset-y":P()}],start:[{start:P()}],end:[{end:P()}],top:[{top:P()}],right:[{right:P()}],bottom:[{bottom:P()}],left:[{left:P()}],visibility:["visible","invisible","collapse"],z:[{z:[_,"auto",X,B]}],basis:[{basis:[I,"full","auto",l,...C()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[S,I,"auto","initial","none",B]}],grow:[{grow:["",S,X,B]}],shrink:[{shrink:["",S,X,B]}],order:[{order:[_,"first","last","none",X,B]}],"grid-cols":[{"grid-cols":M()}],"col-start-end":[{col:z()}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":M()}],"row-start-end":[{row:z()}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":D()}],"auto-rows":[{"auto-rows":D()}],gap:[{gap:C()}],"gap-x":[{"gap-x":C()}],"gap-y":[{"gap-y":C()}],"justify-content":[{justify:[...O(),"normal"]}],"justify-items":[{"justify-items":[...E(),"normal"]}],"justify-self":[{"justify-self":["auto",...E()]}],"align-content":[{content:["normal",...O()]}],"align-items":[{items:[...E(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...E(),{baseline:["","last"]}]}],"place-content":[{"place-content":O()}],"place-items":[{"place-items":[...E(),"baseline"]}],"place-self":[{"place-self":["auto",...E()]}],p:[{p:C()}],px:[{px:C()}],py:[{py:C()}],ps:[{ps:C()}],pe:[{pe:C()}],pt:[{pt:C()}],pr:[{pr:C()}],pb:[{pb:C()}],pl:[{pl:C()}],m:[{m:$()}],mx:[{mx:$()}],my:[{my:$()}],ms:[{ms:$()}],me:[{me:$()}],mt:[{mt:$()}],mr:[{mr:$()}],mb:[{mb:$()}],ml:[{ml:$()}],"space-x":[{"space-x":C()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":C()}],"space-y-reverse":["space-y-reverse"],size:[{size:R()}],w:[{w:[l,"screen",...R()]}],"min-w":[{"min-w":[l,"screen","none",...R()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[i]},...R()]}],h:[{h:["screen","lh",...R()]}],"min-h":[{"min-h":["screen","lh","none",...R()]}],"max-h":[{"max-h":["screen","lh",...R()]}],"font-size":[{text:["base",r,K,H]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[s,X,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",U,B]}],"font-family":[{font:[J,B,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,X,B]}],"line-clamp":[{"line-clamp":[S,"none",X,W]}],leading:[{leading:[a,...C()]}],"list-image":[{"list-image":["none",X,B]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",X,B]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:G()}],"text-color":[{text:G()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[S,"from-font","auto",X,H]}],"text-decoration-color":[{decoration:G()}],"underline-offset":[{"underline-offset":[S,"auto",X,B]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X,B]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X,B]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:es()}],"bg-repeat":[{bg:eo()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},_,X,B],radial:["",X,B],conic:[_,X,B]},et,Z]}],"bg-color":[{bg:G()}],"gradient-from-pos":[{from:ei()}],"gradient-via-pos":[{via:ei()}],"gradient-to-pos":[{to:ei()}],"gradient-from":[{from:G()}],"gradient-via":[{via:G()}],"gradient-to":[{to:G()}],rounded:[{rounded:el()}],"rounded-s":[{"rounded-s":el()}],"rounded-e":[{"rounded-e":el()}],"rounded-t":[{"rounded-t":el()}],"rounded-r":[{"rounded-r":el()}],"rounded-b":[{"rounded-b":el()}],"rounded-l":[{"rounded-l":el()}],"rounded-ss":[{"rounded-ss":el()}],"rounded-se":[{"rounded-se":el()}],"rounded-ee":[{"rounded-ee":el()}],"rounded-es":[{"rounded-es":el()}],"rounded-tl":[{"rounded-tl":el()}],"rounded-tr":[{"rounded-tr":el()}],"rounded-br":[{"rounded-br":el()}],"rounded-bl":[{"rounded-bl":el()}],"border-w":[{border:en()}],"border-w-x":[{"border-x":en()}],"border-w-y":[{"border-y":en()}],"border-w-s":[{"border-s":en()}],"border-w-e":[{"border-e":en()}],"border-w-t":[{"border-t":en()}],"border-w-r":[{"border-r":en()}],"border-w-b":[{"border-b":en()}],"border-w-l":[{"border-l":en()}],"divide-x":[{"divide-x":en()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":en()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:G()}],"border-color-x":[{"border-x":G()}],"border-color-y":[{"border-y":G()}],"border-color-s":[{"border-s":G()}],"border-color-e":[{"border-e":G()}],"border-color-t":[{"border-t":G()}],"border-color-r":[{"border-r":G()}],"border-color-b":[{"border-b":G()}],"border-color-l":[{"border-l":G()}],"divide-color":[{divide:G()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[S,X,B]}],"outline-w":[{outline:["",S,K,H]}],"outline-color":[{outline:G()}],shadow:[{shadow:["","none",c,er,Y]}],"shadow-color":[{shadow:G()}],"inset-shadow":[{"inset-shadow":["none",m,er,Y]}],"inset-shadow-color":[{"inset-shadow":G()}],"ring-w":[{ring:en()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:G()}],"ring-offset-w":[{"ring-offset":[S,H]}],"ring-offset-color":[{"ring-offset":G()}],"inset-ring-w":[{"inset-ring":en()}],"inset-ring-color":[{"inset-ring":G()}],"text-shadow":[{"text-shadow":["none",p,er,Y]}],"text-shadow-color":[{"text-shadow":G()}],opacity:[{opacity:[S,X,B]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[S]}],"mask-image-linear-from-pos":[{"mask-linear-from":em()}],"mask-image-linear-to-pos":[{"mask-linear-to":em()}],"mask-image-linear-from-color":[{"mask-linear-from":G()}],"mask-image-linear-to-color":[{"mask-linear-to":G()}],"mask-image-t-from-pos":[{"mask-t-from":em()}],"mask-image-t-to-pos":[{"mask-t-to":em()}],"mask-image-t-from-color":[{"mask-t-from":G()}],"mask-image-t-to-color":[{"mask-t-to":G()}],"mask-image-r-from-pos":[{"mask-r-from":em()}],"mask-image-r-to-pos":[{"mask-r-to":em()}],"mask-image-r-from-color":[{"mask-r-from":G()}],"mask-image-r-to-color":[{"mask-r-to":G()}],"mask-image-b-from-pos":[{"mask-b-from":em()}],"mask-image-b-to-pos":[{"mask-b-to":em()}],"mask-image-b-from-color":[{"mask-b-from":G()}],"mask-image-b-to-color":[{"mask-b-to":G()}],"mask-image-l-from-pos":[{"mask-l-from":em()}],"mask-image-l-to-pos":[{"mask-l-to":em()}],"mask-image-l-from-color":[{"mask-l-from":G()}],"mask-image-l-to-color":[{"mask-l-to":G()}],"mask-image-x-from-pos":[{"mask-x-from":em()}],"mask-image-x-to-pos":[{"mask-x-to":em()}],"mask-image-x-from-color":[{"mask-x-from":G()}],"mask-image-x-to-color":[{"mask-x-to":G()}],"mask-image-y-from-pos":[{"mask-y-from":em()}],"mask-image-y-to-pos":[{"mask-y-to":em()}],"mask-image-y-from-color":[{"mask-y-from":G()}],"mask-image-y-to-color":[{"mask-y-to":G()}],"mask-image-radial":[{"mask-radial":[X,B]}],"mask-image-radial-from-pos":[{"mask-radial-from":em()}],"mask-image-radial-to-pos":[{"mask-radial-to":em()}],"mask-image-radial-from-color":[{"mask-radial-from":G()}],"mask-image-radial-to-color":[{"mask-radial-to":G()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":v()}],"mask-image-conic-pos":[{"mask-conic":[S]}],"mask-image-conic-from-pos":[{"mask-conic-from":em()}],"mask-image-conic-to-pos":[{"mask-conic-to":em()}],"mask-image-conic-from-color":[{"mask-conic-from":G()}],"mask-image-conic-to-color":[{"mask-conic-to":G()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:es()}],"mask-repeat":[{mask:eo()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",X,B]}],filter:[{filter:["","none",X,B]}],blur:[{blur:ep()}],brightness:[{brightness:[S,X,B]}],contrast:[{contrast:[S,X,B]}],"drop-shadow":[{"drop-shadow":["","none",u,er,Y]}],"drop-shadow-color":[{"drop-shadow":G()}],grayscale:[{grayscale:["",S,X,B]}],"hue-rotate":[{"hue-rotate":[S,X,B]}],invert:[{invert:["",S,X,B]}],saturate:[{saturate:[S,X,B]}],sepia:[{sepia:["",S,X,B]}],"backdrop-filter":[{"backdrop-filter":["","none",X,B]}],"backdrop-blur":[{"backdrop-blur":ep()}],"backdrop-brightness":[{"backdrop-brightness":[S,X,B]}],"backdrop-contrast":[{"backdrop-contrast":[S,X,B]}],"backdrop-grayscale":[{"backdrop-grayscale":["",S,X,B]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[S,X,B]}],"backdrop-invert":[{"backdrop-invert":["",S,X,B]}],"backdrop-opacity":[{"backdrop-opacity":[S,X,B]}],"backdrop-saturate":[{"backdrop-saturate":[S,X,B]}],"backdrop-sepia":[{"backdrop-sepia":["",S,X,B]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":C()}],"border-spacing-x":[{"border-spacing-x":C()}],"border-spacing-y":[{"border-spacing-y":C()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",X,B]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[S,"initial",X,B]}],ease:[{ease:["linear","initial",g,X,B]}],delay:[{delay:[S,X,B]}],animate:[{animate:["none",f,X,B]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,X,B]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:eu()}],"rotate-x":[{"rotate-x":eu()}],"rotate-y":[{"rotate-y":eu()}],"rotate-z":[{"rotate-z":eu()}],scale:[{scale:ex()}],"scale-x":[{"scale-x":ex()}],"scale-y":[{"scale-y":ex()}],"scale-z":[{"scale-z":ex()}],"scale-3d":["scale-3d"],skew:[{skew:eb()}],"skew-x":[{"skew-x":eb()}],"skew-y":[{"skew-y":eb()}],transform:[{transform:[X,B,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eh()}],"translate-x":[{"translate-x":eh()}],"translate-y":[{"translate-y":eh()}],"translate-z":[{"translate-z":eh()}],"translate-none":["translate-none"],accent:[{accent:G()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:G()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X,B]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X,B]}],fill:[{fill:["none",...G()]}],"stroke-w":[{stroke:[S,K,H,W]}],stroke:[{stroke:["none",...G()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function eu(...e){return ep(function(){for(var e,t,r=0,s="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,s,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(s=e(t[r]))&&(o&&(o+=" "),o+=s)}else for(s in t)t[s]&&(o&&(o+=" "),o+=s);return o}(e))&&(s&&(s+=" "),s+=t);return s}(e))}let ex=a().forwardRef(({className:e,variant:t="primary",size:r="md",isLoading:o=!1,disabled:a,children:i,...l},n)=>{let d=eu("inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-electric-blue text-white hover:opacity-90 focus:ring-electric-blue shadow-lg hover:shadow-xl",secondary:"border border-electric-blue text-electric-blue hover:bg-electric-blue hover:text-white focus:ring-electric-blue",ghost:"text-electric-blue hover:bg-electric-blue hover:bg-opacity-10 focus:ring-electric-blue"}[t],{sm:"px-4 py-2 text-sm",md:"px-6 py-3 text-base",lg:"px-8 py-4 text-lg"}[r],e);return(0,s.jsx)("button",{ref:n,className:d,disabled:a||o,...l,children:o?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading..."]}):i})});ex.displayName="Button";let eb=ex,eh=a().forwardRef(({className:e,variant:t="base",label:r,helperText:o,errorMessage:a,...i},l)=>{let n=eu("w-full px-4 py-3 rounded-lg text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{base:"border border-border-light dark:border-border-dark focus:ring-electric-blue focus:border-electric-blue bg-surface-light dark:bg-surface-gray-dark text-text-primary dark:text-text-primary-dark placeholder-text-muted dark:placeholder-text-muted-dark",error:"border border-error focus:ring-error focus:border-error bg-surface-light dark:bg-surface-gray-dark text-text-primary dark:text-text-primary-dark placeholder-text-muted dark:placeholder-text-muted-dark",success:"border border-success focus:ring-success focus:border-success bg-surface-light dark:bg-surface-gray-dark text-text-primary dark:text-text-primary-dark placeholder-text-muted dark:placeholder-text-muted-dark"}[t],e),d="error"===t&&a,c="error"!==t&&o;return(0,s.jsxs)("div",{className:"w-full",children:[r&&(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r}),(0,s.jsx)("input",{ref:l,className:n,...i}),d&&(0,s.jsx)("p",{className:"mt-2 text-sm text-red-600 dark:text-red-400",children:a}),c&&(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:o})]})});eh.displayName="Input";let eg=eh,ef=a().forwardRef(({className:e,variant:t="base",children:r,...o},a)=>{let i=eu("bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",{base:"",hover:"hover:shadow-md transition-shadow duration-200",interactive:"hover:shadow-lg cursor-pointer transition-shadow duration-200"}[t],e);return(0,s.jsx)("div",{ref:a,className:i,...o,children:r})});ef.displayName="Card",a().forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:eu("p-6 pb-0",e),...t})).displayName="CardHeader";let ey=a().forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:eu("p-6",e),...t}));ey.displayName="CardContent",a().forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:eu("p-6 pt-0",e),...t})).displayName="CardFooter";let ev=ef},33430:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(60687);r(43210);var o=r(33872);let a=()=>(0,s.jsxs)("button",{onClick:()=>{window.location.href="mailto:<EMAIL>?subject=Chat%20Request"},className:"fixed bottom-6 right-6 z-50 bg-electric-blue text-white p-4 rounded-full shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-200 group","aria-label":"Open chat",children:[(0,s.jsx)(o.A,{className:"w-6 h-6"}),(0,s.jsxs)("div",{className:"absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap",children:["Chat with us",(0,s.jsx)("div",{className:"absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"})]})]})},33873:e=>{"use strict";e.exports=require("path")},34185:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(60687);r(43210);var o=r(26001),a=r(17581),i=r(62688);let l=(0,i.A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]),n=(0,i.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var d=r(70334),c=r(85814),m=r.n(c),p=r(7378),u=r(1745);let x=()=>{let e=(0,p.Mw)("services"),t=[{icon:(0,s.jsx)(a.A,{className:"w-8 h-8"}),...u.Bg.starter},{icon:(0,s.jsx)(l,{className:"w-8 h-8"}),...u.Bg.custom},{icon:(0,s.jsx)(n,{className:"w-8 h-8"}),...u.Bg.enterprise}];return(0,s.jsx)("section",{id:"services-overview",className:"py-16 md:py-20 bg-gray-50 dark:bg-gray-800 transition-colors duration-300",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:u.UZ.duration.normal},viewport:{once:!0},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-dark-charcoal dark:text-white mb-4",children:e.headline}),(0,s.jsx)("p",{className:"text-lg md:text-xl leading-relaxed text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:e.subtext})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-3 gap-8 md:gap-12 mb-12",children:t.map((e,t)=>(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:`relative bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 ${e.popular?"ring-2 ring-electric-blue":""}`,children:[e.popular&&(0,s.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,s.jsx)("span",{className:"bg-electric-blue text-white px-4 py-1 rounded-full text-sm font-medium",children:"Most Popular"})}),(0,s.jsx)("div",{className:"text-electric-blue mb-4",children:e.icon}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-dark-charcoal dark:text-white mb-2",children:e.name}),(0,s.jsx)("p",{className:"text-base leading-relaxed text-gray-600 dark:text-gray-300 mb-4",children:e.description}),(0,s.jsx)("div",{className:"text-2xl font-bold text-dark-charcoal dark:text-white mb-4",children:e.price}),(0,s.jsx)("ul",{className:"space-y-2 mb-6",children:e.features.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-center text-base text-gray-600 dark:text-gray-300",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-electric-blue rounded-full mr-3"}),e]},t))})]},t))}),(0,s.jsx)(o.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"text-center",children:(0,s.jsxs)(m(),{href:"/services",onClick:()=>{},className:"inline-flex items-center bg-electric-blue text-white px-8 py-4 rounded-lg font-semibold hover:opacity-90 transition-opacity duration-200 shadow-lg hover:shadow-xl",children:["Compare All Features & Pricing",(0,s.jsx)(d.A,{className:"ml-2 w-5 h-5"})]})})]})})}},34631:e=>{"use strict";e.exports=require("tls")},36481:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(60687);r(43210);var o=r(26001),a=r(31190),i=r(7378),l=r(1745);let n=()=>{let e=(0,i.Mw)("hero"),{trackNavigation:t}=(0,i.st)(),r=e=>{let r=document.getElementById(e);r&&(r.scrollIntoView({behavior:"smooth"}),t(e,"hero_cta"))};return(0,s.jsx)("section",{id:"hero",className:"pt-16 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 min-h-screen flex items-center w-full transition-colors duration-300",children:(0,s.jsx)("div",{className:"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center w-full",children:[(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:l.UZ.duration.slow},className:"text-center lg:text-left",children:[(0,s.jsx)("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-dark-charcoal dark:text-white leading-tight",children:e.headline}),(0,s.jsx)("p",{className:"mt-6 text-xl text-gray-600 dark:text-gray-300 leading-relaxed",children:e.subtext}),(0,s.jsx)("div",{className:"mt-8",children:(0,s.jsx)(a.$n,{onClick:()=>r("demo"),variant:"primary",size:"lg",disabled:e.isLoading,children:e.buttonText})})]}),(0,s.jsx)(o.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:l.UZ.duration.slow,delay:l.UZ.duration.fast},className:"relative",children:(0,s.jsx)("div",{className:"relative mx-auto w-64 h-96 bg-gray-900 rounded-3xl p-2 shadow-2xl",children:(0,s.jsxs)("div",{className:"w-full h-full bg-white rounded-2xl overflow-hidden relative",children:[(0,s.jsx)("div",{className:"h-6 bg-gray-900 flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-16 h-1 bg-white rounded-full"})}),(0,s.jsxs)("div",{className:"p-4 space-y-4",children:[(0,s.jsx)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1},className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,s.jsx)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1.2},className:"h-32 bg-gradient-to-r from-electric-blue/10 to-electric-blue/20 rounded-lg flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-electric-blue font-semibold",children:"Your App Here"})}),(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:1.4},className:"space-y-2",children:[(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4"})]})]})]})})})]})})})}},38922:(e,t,r)=>{let{createProxy:s}=r(39844);e.exports=s("C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\dynamic-bailout-to-csr.js")},43712:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\sections\\\\Process.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Process.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56780:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return o}});let s=r(81208);function o(e){let{reason:t,children:r}=e;throw Object.defineProperty(new s.BailoutToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64777:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return l}});let s=r(60687),o=r(51215),a=r(29294),i=r(19587);function l(e){let{moduleIds:t}=e,r=a.workAsyncStorage.getStore();if(void 0===r)return null;let l=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files;l.push(...t)}}return 0===l.length?null:(0,s.jsx)(s.Fragment,{children:l.map(e=>{let t=r.assetPrefix+"/_next/"+(0,i.encodeURIPath)(e);return e.endsWith(".css")?(0,s.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,o.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},67113:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let s=r(72639)._(r(6809));function o(e,t){var r;let o={};"function"==typeof e&&(o.loader=e);let a={...o,...t};return(0,s.default)({...a,modules:null==(r=a.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68175:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687);r(43210);var o=r(26001),a=r(85814),i=r.n(a),l=r(70334),n=r(31190);let d=()=>(0,s.jsx)("section",{id:"about",className:"py-20 bg-gray-50 dark:bg-gray-800 transition-colors duration-300",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsx)(o.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"max-w-4xl mx-auto",children:(0,s.jsx)(n.Zp,{variant:"hover",className:"text-center",children:(0,s.jsxs)(n.Wu,{className:"p-8 md:p-12",children:[(0,s.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-dark-charcoal dark:text-white mb-6",children:"We're More Than Just Developers"}),(0,s.jsxs)("div",{className:"text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8 space-y-4",children:[(0,s.jsx)("p",{children:"At Mobilify, we believe that every great idea deserves to become reality. We're passionate about helping founders and businesses succeed by removing the traditional barriers to mobile app development."}),(0,s.jsx)("p",{children:"Our commitment goes beyond just writing code – we're your partners in bringing your vision to life. We focus on quality over quantity, ensuring each app we create is crafted with care, attention to detail, and a deep understanding of your unique needs."})]}),(0,s.jsx)(o.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},children:(0,s.jsxs)(i(),{href:"/about",className:"inline-flex items-center text-electric-blue hover:text-electric-blue/80 font-semibold transition-colors duration-200",children:["Meet the Team",(0,s.jsx)(l.A,{className:"ml-2 w-4 h-4"})]})})]})})})})})},68367:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\NewsletterSignup.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NewsletterSignup.tsx","default")},68926:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Header.tsx","default")},69430:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(60687);r(43210);var o=r(26001),a=r(62688);let i=(0,a.A)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),l=(0,a.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var n=r(33872),d=r(91405);let c=({variant:e="button",text:t="Chat with us",icon:r="message",className:a="",size:c="md",context:m="general"})=>{let p=()=>{d.l.setSessionData({trigger_context:m,trigger_page:window.location.pathname,trigger_time:new Date().toISOString()}),d.l.openChat()},u=()=>{let e={className:"sm"===c?"w-4 h-4":"lg"===c?"w-6 h-6":"w-5 h-5"};switch(r){case"help":return(0,s.jsx)(i,{...e});case"phone":return(0,s.jsx)(l,{...e});default:return(0,s.jsx)(n.A,{...e})}};return"floating"===e?(0,s.jsxs)(o.P.button,{onClick:p,className:`fixed bottom-6 right-6 bg-electric-blue text-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 z-40 ${a}`,whileHover:{scale:1.05},whileTap:{scale:.95},initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{delay:2,duration:.3},title:t,children:[u(),(0,s.jsx)("span",{className:"sr-only",children:t})]}):"inline"===e?(0,s.jsxs)("button",{onClick:p,className:`inline-flex items-center gap-2 text-electric-blue hover:text-indigo-700 font-medium transition-colors duration-200 ${a}`,children:[u(),(0,s.jsx)("span",{children:t})]}):(0,s.jsxs)(o.P.button,{onClick:p,className:`inline-flex items-center gap-2 bg-electric-blue text-white rounded-lg font-semibold hover:opacity-90 transition-all duration-200 shadow-md hover:shadow-lg ${(()=>{switch(c){case"sm":return"px-3 py-2 text-sm";case"lg":return"px-6 py-4 text-lg";default:return"px-4 py-3 text-base"}})()} ${a}`,whileHover:{scale:1.02},whileTap:{scale:.98},children:[u(),(0,s.jsx)("span",{children:t})]})},m={ContactChat:()=>(0,s.jsx)(c,{variant:"button",text:"Chat with Our Team",icon:"message",context:"contact",size:"lg",className:"w-full sm:w-auto"})};var p=r(7378),u=r(1745);let x=()=>{let{formData:e,errors:t,isSubmitting:r,isSubmitted:a,submitSuccess:i,updateField:l,handleFieldFocus:n,validateSingleField:d,submitForm:c,resetForm:m}=(0,p.qF)(),x=(0,p.Mw)("contact"),b=async e=>{e.preventDefault(),await c()},h=e=>{l(e.target.name,e.target.value)},g=e=>{d(e.target.name)},f=e=>{n(e.target.name)};return a&&i?(0,s.jsxs)(o.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:u.UZ.duration.normal},className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6 text-center",children:[(0,s.jsx)("div",{className:"text-green-600 dark:text-green-400 text-lg font-semibold mb-2",children:"Message Sent Successfully!"}),(0,s.jsx)("p",{className:"text-green-700 dark:text-green-300 mb-4",children:x.successMessage}),(0,s.jsx)("button",{onClick:m,className:"text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 font-medium",children:"Send Another Message"})]}):(0,s.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[t.general&&(0,s.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600 dark:text-red-400 text-sm",children:t.general})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Full Name *"}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",value:e.name,onChange:h,onBlur:g,onFocus:f,className:`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-electric-blue focus:border-electric-blue transition-colors duration-200 dark:bg-gray-800 dark:border-gray-600 dark:text-white ${t.name?"border-red-500 focus:ring-red-500 focus:border-red-500":"border-gray-300"}`,placeholder:"Your full name",disabled:r}),t.name&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:t.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address *"}),(0,s.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:h,onBlur:g,onFocus:f,className:`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-electric-blue focus:border-electric-blue transition-colors duration-200 dark:bg-gray-800 dark:border-gray-600 dark:text-white ${t.email?"border-red-500 focus:ring-red-500 focus:border-red-500":"border-gray-300"}`,placeholder:"<EMAIL>",disabled:r}),t.email&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:t.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"company",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Company (Optional)"}),(0,s.jsx)("input",{type:"text",id:"company",name:"company",value:e.company,onChange:h,onBlur:g,onFocus:f,className:`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-electric-blue focus:border-electric-blue transition-colors duration-200 dark:bg-gray-800 dark:border-gray-600 dark:text-white ${t.company?"border-red-500 focus:ring-red-500 focus:border-red-500":"border-gray-300"}`,placeholder:"Your company name",disabled:r}),t.company&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:t.company})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"projectType",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Project Type *"}),(0,s.jsxs)("select",{id:"projectType",name:"projectType",value:e.projectType,onChange:h,onBlur:g,onFocus:f,className:`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-electric-blue focus:border-electric-blue transition-colors duration-200 dark:bg-gray-800 dark:border-gray-600 dark:text-white ${t.projectType?"border-red-500 focus:ring-red-500 focus:border-red-500":"border-gray-300"}`,disabled:r,children:[(0,s.jsx)("option",{value:"",children:"Select a project type"}),(0,s.jsx)("option",{value:"website-conversion",children:"Website to App Conversion"}),(0,s.jsx)("option",{value:"custom-app",children:"Custom App Development"}),(0,s.jsx)("option",{value:"enterprise",children:"Enterprise Solution"}),(0,s.jsx)("option",{value:"consultation",children:"Consultation Only"}),(0,s.jsx)("option",{value:"other",children:"Other"})]}),t.projectType&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:t.projectType})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Project Details *"}),(0,s.jsx)("textarea",{id:"message",name:"message",rows:5,value:e.message,onChange:h,onBlur:g,onFocus:f,className:`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-electric-blue focus:border-electric-blue transition-colors duration-200 dark:bg-gray-800 dark:border-gray-600 dark:text-white resize-vertical ${t.message?"border-red-500 focus:ring-red-500 focus:border-red-500":"border-gray-300"}`,placeholder:"Tell us about your project, timeline, and any specific requirements...",disabled:r}),t.message&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:t.message}),(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:[e.message.length,"/1000 characters"]})]}),(0,s.jsx)("button",{type:"submit",disabled:r,className:"w-full bg-electric-blue text-white py-4 px-6 rounded-lg font-semibold hover:opacity-90 focus:ring-2 focus:ring-electric-blue focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl",children:r?"Sending...":x.buttonText}),a&&!i&&(0,s.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600 dark:text-red-400 text-sm",children:x.errorMessage})})]})},b=()=>{let e=(0,p.Mw)("contact");return(0,s.jsx)("section",{id:"contact",className:"py-16 md:py-20 bg-white dark:bg-gray-900 transition-colors duration-300",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:u.UZ.duration.normal},viewport:{once:!0},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-dark-charcoal dark:text-white mb-4",children:e.headline}),(0,s.jsx)("p",{className:"text-lg md:text-xl leading-relaxed text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:e.subtext})]}),(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:u.UZ.duration.normal,delay:u.UZ.duration.fast},viewport:{once:!0},className:"max-w-2xl mx-auto",children:[(0,s.jsx)(x,{}),(0,s.jsxs)("div",{className:"mt-8 text-center",children:[(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mb-4",children:"Prefer to chat? Get instant answers to your questions."}),(0,s.jsx)(m.ContactChat,{})]})]})]})})}},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},71578:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),o=r(48088),a=r(88170),i=r.n(a),l=r(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21910)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,14974)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},73814:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\sections\\\\ServicesOverview.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\ServicesOverview.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82196:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k});var s=r(60687),o=r(43210),a=r(26001),i=r(7378),l=r(1745),n=r(62688);let d=(0,n.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var c=r(17581);let m=(0,n.A)("tablet",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["line",{x1:"12",x2:"12.01",y1:"18",y2:"18",key:"1dp563"}]]),p=[{id:"website",label:"Website to App",icon:(0,s.jsx)(d,{className:"w-5 h-5"}),description:"Convert your existing website into a mobile app"},{id:"mobile",label:"Mobile App",icon:(0,s.jsx)(c.A,{className:"w-5 h-5"}),description:"Build a custom mobile application from scratch"},{id:"tablet",label:"Tablet App",icon:(0,s.jsx)(m,{className:"w-5 h-5"}),description:"Create tablet-optimized applications"}],u=({activeTab:e,onTabChange:t,className:r=""})=>(0,s.jsx)("div",{className:`flex flex-col sm:flex-row gap-4 mb-8 ${r}`,children:p.map(r=>(0,s.jsxs)(a.P.button,{onClick:()=>t(r.id),className:`
            relative flex items-center gap-3 p-4 rounded-xl border-2 transition-all duration-300
            ${e===r.id?"border-electric-blue bg-electric-blue/10 text-electric-blue":"border-gray-200 dark:border-gray-700 hover:border-electric-blue/50 text-gray-700 dark:text-gray-300"}
          `,whileHover:{scale:1.02},whileTap:{scale:.98},"aria-label":`Switch to ${r.label} demo`,children:[e===r.id&&(0,s.jsx)(a.P.div,{layoutId:"activeTab",className:"absolute inset-0 bg-electric-blue/5 rounded-xl",initial:!1,transition:{type:"spring",bounce:.2,duration:.6}}),(0,s.jsxs)("div",{className:"relative z-10 flex items-center gap-3",children:[r.icon,(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("div",{className:"font-semibold",children:r.label}),(0,s.jsx)("div",{className:"text-sm opacity-75",children:r.description})]})]})]},r.id))});var x=r(25334),b=r(99270);let h=({activeTab:e,value:t,onChange:r,onPreview:o,placeholder:i,className:l=""})=>{let n=()=>{if(!t.trim())return!1;if("website"===e)try{return new URL(t),!0}catch{return!1}return t.trim().length>=3};return(0,s.jsxs)("div",{className:`space-y-4 ${l}`,children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:"website"===e?(0,s.jsx)(x.A,{className:"h-5 w-5 text-gray-400"}):(0,s.jsx)(b.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{type:"website"===e?"url":"text",value:t,onChange:e=>r(e.target.value),onKeyPress:e=>{"Enter"===e.key&&n()&&o()},placeholder:(()=>{switch(e){case"website":return i||"Enter your website URL (e.g., https://example.com)";case"mobile":return i||"Describe your mobile app idea";case"tablet":return i||"Describe your tablet app concept";default:return i||"Enter your input"}})(),className:" w-full pl-12 pr-4 py-4 text-lg border-2 border-gray-200 dark:border-gray-700  rounded-xl focus:border-electric-blue focus:ring-2 focus:ring-electric-blue/20  bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-300 ","aria-label":`Input for ${e} demo`})]}),(0,s.jsx)(a.P.button,{onClick:o,disabled:!n(),className:`
          w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-300
          ${n()?"bg-electric-blue text-white hover:bg-electric-blue/90 shadow-lg hover:shadow-xl":"bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed"}
        `,whileHover:n()?{scale:1.02}:{},whileTap:n()?{scale:.98}:{},"aria-label":"Generate preview",children:"website"===e?"Preview App Conversion":"Generate Preview"}),"website"===e&&t&&!n()&&(0,s.jsx)(a.P.p,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"text-sm text-red-500 dark:text-red-400",children:"Please enter a valid URL (including http:// or https://)"})]})};var g=r(88920);let f=(0,n.A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]),y=(0,n.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),v=(0,n.A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]),w=({activeTab:e,inputValue:t,isVisible:r,onAnimationComplete:o,className:i=""})=>{let n=(()=>{switch(e){case"website":return{title:"Website to App Conversion",description:`Converting ${t} into a mobile app`,icon:(0,s.jsx)(c.A,{className:"w-8 h-8"}),features:["Native mobile navigation","Offline functionality","Push notifications","App store optimization"]};case"mobile":return{title:"Custom Mobile App",description:`Building: ${t}`,icon:(0,s.jsx)(c.A,{className:"w-8 h-8"}),features:["Custom UI/UX design","Native performance","Cross-platform compatibility","Backend integration"]};case"tablet":return{title:"Tablet Application",description:`Creating: ${t}`,icon:(0,s.jsx)(m,{className:"w-8 h-8"}),features:["Tablet-optimized layout","Multi-window support","Enhanced productivity features","Responsive design"]};default:return null}})();return n?(0,s.jsx)(g.N,{children:r&&(0,s.jsxs)(a.P.div,{initial:{opacity:0,y:50,scale:.9},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.9},transition:{duration:l.UZ.duration.normal,ease:l.UZ.easing.easeInOut},onAnimationComplete:o,className:`bg-surface-light dark:bg-surface-gray-dark rounded-2xl shadow-2xl border border-border-light dark:border-border-dark overflow-hidden ${i}`,children:[(0,s.jsx)("div",{className:"bg-gradient-to-r from-electric-blue to-blue-600 text-white p-6",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("div",{className:"p-3 bg-white/20 rounded-xl",children:n.icon}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-bold",children:n.title}),(0,s.jsx)("p",{className:"text-white/80 mt-1",children:n.description})]})]})}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("div",{className:"relative mx-auto mb-6",style:{width:"280px",height:"500px"},children:(0,s.jsx)("div",{className:"absolute inset-0 bg-dark-charcoal rounded-[2.5rem] p-2",children:(0,s.jsxs)("div",{className:"w-full h-full bg-surface-light rounded-[2rem] overflow-hidden relative",children:[(0,s.jsxs)("div",{className:"h-6 bg-surface-gray flex items-center justify-between px-4 text-xs",children:[(0,s.jsx)("span",{children:"9:41"}),(0,s.jsxs)("div",{className:"flex gap-1",children:[(0,s.jsx)("div",{className:"w-4 h-2 bg-success rounded-sm"}),(0,s.jsx)("div",{className:"w-4 h-2 bg-border-light rounded-sm"}),(0,s.jsx)("div",{className:"w-4 h-2 bg-border-light rounded-sm"})]})]}),(0,s.jsxs)("div",{className:"p-4 h-full bg-gradient-to-b from-blue-50 to-white",children:[(0,s.jsxs)("div",{className:"text-center mb-4",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-electric-blue rounded-xl mx-auto mb-2 flex items-center justify-center",children:(0,s.jsx)(f,{className:"w-8 h-8 text-white"})}),(0,s.jsx)("h4",{className:"font-semibold text-text-primary",children:"Your App"})]}),(0,s.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,s.jsx)("div",{className:"h-12 bg-surface-gray rounded-lg animate-pulse"},e))})]})]})})}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:n.features.map((e,t)=>(0,s.jsxs)(a.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*t},className:"flex items-center gap-3 p-3 bg-surface-gray dark:bg-surface-gray-dark rounded-lg",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-electric-blue rounded-full"}),(0,s.jsx)("span",{className:"text-sm font-medium text-text-secondary dark:text-text-secondary-dark",children:e})]},e))}),(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsxs)(a.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex-1 bg-electric-blue text-white py-3 px-4 rounded-xl font-semibold flex items-center justify-center gap-2 hover:bg-electric-blue/90 transition-colors",children:[(0,s.jsx)(y,{className:"w-4 h-4"}),"Get Quote"]}),(0,s.jsx)(a.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"px-4 py-3 border-2 border-electric-blue text-electric-blue rounded-xl font-semibold flex items-center justify-center hover:bg-electric-blue/10 transition-colors",children:(0,s.jsx)(v,{className:"w-4 h-4"})})]})]})]})}):null},k=()=>{let[e,t]=(0,o.useState)("website"),[r,n]=(0,o.useState)(""),[d,c]=(0,o.useState)(!1),{trackDemoInteraction:m}=(0,i.st)(),p=(0,i.Mw)("services");return(0,s.jsx)("section",{id:"demo",className:"py-20 bg-gray-50 dark:bg-gray-900 transition-colors duration-300",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(a.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:l.UZ.duration.normal},viewport:{once:!0},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-dark-charcoal dark:text-white mb-4",children:p.headline||"From Zero to App, Instantly."}),(0,s.jsx)("p",{className:"text-lg md:text-xl leading-relaxed text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:p.subtext||"See how quickly we can transform your vision into a beautiful mobile app"})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-start",children:[(0,s.jsxs)(a.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:l.UZ.duration.normal,delay:.2},viewport:{once:!0},className:"space-y-8",children:[(0,s.jsx)(u,{activeTab:e,onTabChange:e=>{t(e),c(!1),n(""),m("tab_switch",e)}}),(0,s.jsx)(h,{activeTab:e,value:r,onChange:n,onPreview:()=>{r.trim()&&(c(!0),m("preview_click",e))}})]}),(0,s.jsx)(a.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:l.UZ.duration.normal,delay:.4},viewport:{once:!0},className:"flex justify-center",children:(0,s.jsx)(w,{activeTab:e,inputValue:r,isVisible:d,onAnimationComplete:()=>{m("animation_complete",e)}})})]})]})})}},83997:e=>{"use strict";e.exports=require("tty")},84712:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\layout\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\layout\\Footer.tsx","default")},91477:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(60687),o=r(43210),a=r(26001),i=r(31190);let l=({variant:e="inline",className:t=""})=>{let[r,l]=(0,o.useState)(""),[n,d]=(0,o.useState)(!1),[c,m]=(0,o.useState)("idle"),p=async t=>{if(t.preventDefault(),r.trim()){d(!0),m("idle");try{let t=process.env.NEXT_PUBLIC_MAILCHIMP_API_KEY,s=process.env.MAILCHIMP_LIST_ID;if(!t||!s){await new Promise(e=>setTimeout(e,1e3)),m("success"),l("");return}(await fetch("/api/newsletter",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r,source:e})})).ok?(m("success"),l("")):m("error")}catch(e){m("error")}finally{d(!1)}}};return(0,s.jsxs)("div",{className:`${t}`,children:[("inline"===e||"section"===e)&&(0,s.jsx)(a.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"py-16 md:py-20 bg-electric-blue",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"Stay Updated on Mobile Innovation"}),(0,s.jsx)("p",{className:"text-lg md:text-xl leading-relaxed text-blue-100 max-w-3xl mx-auto mb-8",children:"Get insights on mobile app development, industry trends, and exclusive tips delivered to your inbox."}),(0,s.jsxs)("form",{onSubmit:p,className:"max-w-md mx-auto",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,s.jsx)(i.pd,{type:"email",value:r,onChange:e=>l(e.target.value),placeholder:"Enter your email address",required:!0,className:"flex-1 bg-white border-white focus:ring-white focus:border-white",disabled:n}),(0,s.jsx)(i.$n,{type:"submit",variant:"secondary",isLoading:n,disabled:!r.trim()||n,className:"bg-white text-electric-blue hover:bg-gray-50 border-white",children:"Subscribe"})]}),"success"===c&&(0,s.jsx)(a.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4 text-green-100 text-sm",children:"✓ Successfully subscribed! Check your email for confirmation."}),"error"===c&&(0,s.jsx)(a.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4 text-red-100 text-sm",children:"✗ Something went wrong. Please try again."})]})]})}),"footer"===e&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-semibold mb-4 text-white",children:"Stay Connected"}),(0,s.jsx)("p",{className:"text-sm text-gray-400 dark:text-gray-300 mb-4 leading-relaxed",children:"Get the latest updates on mobile app development and industry insights."}),(0,s.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[(0,s.jsx)(i.pd,{type:"email",value:r,onChange:e=>l(e.target.value),placeholder:"Enter your email",required:!0,className:"bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:ring-electric-blue focus:border-electric-blue",disabled:n}),(0,s.jsx)(i.$n,{type:"submit",variant:"primary",size:"sm",isLoading:n,disabled:!r.trim()||n,className:"w-full",children:"Subscribe"}),"success"===c&&(0,s.jsx)(a.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-green-400 text-sm",children:"✓ Successfully subscribed!"}),"error"===c&&(0,s.jsx)(a.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-red-400 text-sm",children:"✗ Please try again."})]})]})]})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94759:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\sections\\\\Contact.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\sections\\Contact.tsx","default")},95047:(e,t,r)=>{let{createProxy:s}=r(39844);e.exports=s("C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\preload-chunks.js")},97275:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(60687);r(43210);var o=r(26001),a=r(99270),i=r(62688);let l=(0,i.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),n=(0,i.A)("rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]]);var d=r(31190);let c=()=>{let e=[{number:"01",icon:(0,s.jsx)(a.A,{className:"w-8 h-8"}),title:"Discovery & Strategy",description:"We dive deep into your vision and goals, understanding your target audience and business objectives to create the perfect roadmap."},{number:"02",icon:(0,s.jsx)(l,{className:"w-8 h-8"}),title:"Design & Development",description:"Our team builds your app with precision and care, focusing on user experience, performance, and beautiful design that represents your brand."},{number:"03",icon:(0,s.jsx)(n,{className:"w-8 h-8"}),title:"Launch & Support",description:"We handle app store submission and provide ongoing support to ensure your app succeeds in the market and continues to evolve."}];return(0,s.jsx)("section",{id:"process",className:"py-20 bg-white dark:bg-gray-900 transition-colors duration-300",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-dark-charcoal dark:text-white mb-4",children:"Your Clear Path to Launch"}),(0,s.jsx)("p",{className:"text-lg md:text-xl leading-relaxed text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"Our proven process ensures your app is built right, launched successfully, and supported long-term"})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-3 gap-8 lg:gap-12",children:e.map((t,r)=>(0,s.jsxs)(o.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2*r},viewport:{once:!0},className:"relative text-center",children:[r<e.length-1&&(0,s.jsx)("div",{className:"hidden md:block absolute top-16 left-1/2 w-full h-0.5 bg-gray-200 dark:bg-gray-700 z-0",children:(0,s.jsx)(o.P.div,{initial:{width:0},whileInView:{width:"100%"},transition:{duration:1,delay:.3*r+.5},viewport:{once:!0},className:"h-full bg-electric-blue"})}),(0,s.jsx)("div",{className:"relative z-10 mb-6",children:(0,s.jsxs)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-electric-blue text-white rounded-full mb-4 relative",children:[t.icon,(0,s.jsx)("div",{className:"absolute -top-2 -right-2 w-8 h-8 bg-white dark:bg-gray-800 text-electric-blue rounded-full flex items-center justify-center text-sm font-bold shadow-lg",children:t.number})]})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-dark-charcoal dark:text-white",children:t.title}),(0,s.jsx)("p",{className:"text-base leading-relaxed text-gray-600 dark:text-gray-300",children:t.description})]})]},t.number))}),(0,s.jsx)(o.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.8},viewport:{once:!0},className:"mt-16 text-center",children:(0,s.jsx)(d.Zp,{className:"max-w-4xl mx-auto",children:(0,s.jsxs)(d.Wu,{className:"p-8 text-center",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-dark-charcoal dark:text-white mb-2",children:"Transparent Timeline"}),(0,s.jsx)("p",{className:"text-base leading-relaxed text-gray-600 dark:text-gray-300",children:"Most projects are completed within 4-8 weeks, depending on complexity. We provide regular updates and maintain open communication throughout the entire process."})]})})})]})})}},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,759,476,550,449],()=>r(71578));module.exports=s})();