"use strict";exports.id=698,exports.ids=[698],exports.modules={363:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2688).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},1134:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2688).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},1279:(t,e,i)=>{i.d(e,{t:()=>r});let r=(0,i(3210).createContext)(null)},1860:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},2157:(t,e,i)=>{i.d(e,{L:()=>r});let r=(0,i(3210).createContext)({})},2348:(t,e,i)=>{i.d(e,{QP:()=>tu});let r=t=>{let e=a(t),{conflictingClassGroups:i,conflictingClassGroupModifiers:r}=t;return{getClassGroupId:t=>{let i=t.split("-");return""===i[0]&&1!==i.length&&i.shift(),s(i,e)||o(t)},getConflictingClassGroupIds:(t,e)=>{let s=i[t]||[];return e&&r[t]?[...s,...r[t]]:s}}},s=(t,e)=>{if(0===t.length)return e.classGroupId;let i=t[0],r=e.nextPart.get(i),n=r?s(t.slice(1),r):void 0;if(n)return n;if(0===e.validators.length)return;let o=t.join("-");return e.validators.find(({validator:t})=>t(o))?.classGroupId},n=/^\[(.+)\]$/,o=t=>{if(n.test(t)){let e=n.exec(t)[1],i=e?.substring(0,e.indexOf(":"));if(i)return"arbitrary.."+i}},a=t=>{let{theme:e,classGroups:i}=t,r={nextPart:new Map,validators:[]};for(let t in i)l(i[t],r,t,e);return r},l=(t,e,i,r)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:u(e,t)).classGroupId=i;return}if("function"==typeof t)return h(t)?void l(t(r),e,i,r):void e.validators.push({validator:t,classGroupId:i});Object.entries(t).forEach(([t,s])=>{l(s,u(e,t),i,r)})})},u=(t,e)=>{let i=t;return e.split("-").forEach(t=>{i.nextPart.has(t)||i.nextPart.set(t,{nextPart:new Map,validators:[]}),i=i.nextPart.get(t)}),i},h=t=>t.isThemeGetter,d=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,i=new Map,r=new Map,s=(s,n)=>{i.set(s,n),++e>t&&(e=0,r=i,i=new Map)};return{get(t){let e=i.get(t);return void 0!==e?e:void 0!==(e=r.get(t))?(s(t,e),e):void 0},set(t,e){i.has(t)?i.set(t,e):s(t,e)}}},c=t=>{let{prefix:e,experimentalParseClassName:i}=t,r=t=>{let e,i=[],r=0,s=0,n=0;for(let o=0;o<t.length;o++){let a=t[o];if(0===r&&0===s){if(":"===a){i.push(t.slice(n,o)),n=o+1;continue}if("/"===a){e=o;continue}}"["===a?r++:"]"===a?r--:"("===a?s++:")"===a&&s--}let o=0===i.length?t:t.substring(n),a=p(o);return{modifiers:i,hasImportantModifier:a!==o,baseClassName:a,maybePostfixModifierPosition:e&&e>n?e-n:void 0}};if(e){let t=e+":",i=r;r=e=>e.startsWith(t)?i(e.substring(t.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:e,maybePostfixModifierPosition:void 0}}if(i){let t=r;r=e=>i({className:e,parseClassName:t})}return r},p=t=>t.endsWith("!")?t.substring(0,t.length-1):t.startsWith("!")?t.substring(1):t,m=t=>{let e=Object.fromEntries(t.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;let i=[],r=[];return t.forEach(t=>{"["===t[0]||e[t]?(i.push(...r.sort(),t),r=[]):r.push(t)}),i.push(...r.sort()),i}},f=t=>({cache:d(t.cacheSize),parseClassName:c(t),sortModifiers:m(t),...r(t)}),g=/\s+/,y=(t,e)=>{let{parseClassName:i,getClassGroupId:r,getConflictingClassGroupIds:s,sortModifiers:n}=e,o=[],a=t.trim().split(g),l="";for(let t=a.length-1;t>=0;t-=1){let e=a[t],{isExternal:u,modifiers:h,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:p}=i(e);if(u){l=e+(l.length>0?" "+l:l);continue}let m=!!p,f=r(m?c.substring(0,p):c);if(!f){if(!m||!(f=r(c))){l=e+(l.length>0?" "+l:l);continue}m=!1}let g=n(h).join(":"),y=d?g+"!":g,v=y+f;if(o.includes(v))continue;o.push(v);let b=s(f,m);for(let t=0;t<b.length;++t){let e=b[t];o.push(y+e)}l=e+(l.length>0?" "+l:l)}return l};function v(){let t,e,i=0,r="";for(;i<arguments.length;)(t=arguments[i++])&&(e=b(t))&&(r&&(r+=" "),r+=e);return r}let b=t=>{let e;if("string"==typeof t)return t;let i="";for(let r=0;r<t.length;r++)t[r]&&(e=b(t[r]))&&(i&&(i+=" "),i+=e);return i},x=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},w=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,T=/^\d+\/\d+$/,P=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,S=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,A=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,E=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,V=t=>T.test(t),C=t=>!!t&&!Number.isNaN(Number(t)),D=t=>!!t&&Number.isInteger(Number(t)),R=t=>t.endsWith("%")&&C(t.slice(0,-1)),j=t=>P.test(t),L=()=>!0,F=t=>S.test(t)&&!A.test(t),B=()=>!1,O=t=>M.test(t),I=t=>E.test(t),z=t=>!N(t)&&!G(t),U=t=>tt(t,ts,B),N=t=>w.test(t),$=t=>tt(t,tn,F),W=t=>tt(t,to,C),Y=t=>tt(t,ti,B),X=t=>tt(t,tr,I),H=t=>tt(t,tl,O),G=t=>k.test(t),K=t=>te(t,tn),q=t=>te(t,ta),Z=t=>te(t,ti),_=t=>te(t,ts),Q=t=>te(t,tr),J=t=>te(t,tl,!0),tt=(t,e,i)=>{let r=w.exec(t);return!!r&&(r[1]?e(r[1]):i(r[2]))},te=(t,e,i=!1)=>{let r=k.exec(t);return!!r&&(r[1]?e(r[1]):i)},ti=t=>"position"===t||"percentage"===t,tr=t=>"image"===t||"url"===t,ts=t=>"length"===t||"size"===t||"bg-size"===t,tn=t=>"length"===t,to=t=>"number"===t,ta=t=>"family-name"===t,tl=t=>"shadow"===t;Symbol.toStringTag;let tu=function(t,...e){let i,r,s,n=function(a){return r=(i=f(e.reduce((t,e)=>e(t),t()))).cache.get,s=i.cache.set,n=o,o(a)};function o(t){let e=r(t);if(e)return e;let n=y(t,i);return s(t,n),n}return function(){return n(v.apply(null,arguments))}}(()=>{let t=x("color"),e=x("font"),i=x("text"),r=x("font-weight"),s=x("tracking"),n=x("leading"),o=x("breakpoint"),a=x("container"),l=x("spacing"),u=x("radius"),h=x("shadow"),d=x("inset-shadow"),c=x("text-shadow"),p=x("drop-shadow"),m=x("blur"),f=x("perspective"),g=x("aspect"),y=x("ease"),v=x("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],w=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...w(),G,N],T=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto","contain","none"],S=()=>[G,N,l],A=()=>[V,"full","auto",...S()],M=()=>[D,"none","subgrid",G,N],E=()=>["auto",{span:["full",D,G,N]},D,G,N],F=()=>[D,"auto",G,N],B=()=>["auto","min","max","fr",G,N],O=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],I=()=>["start","end","center","stretch","center-safe","end-safe"],tt=()=>["auto",...S()],te=()=>[V,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...S()],ti=()=>[t,G,N],tr=()=>[...w(),Z,Y,{position:[G,N]}],ts=()=>["no-repeat",{repeat:["","x","y","space","round"]}],tn=()=>["auto","cover","contain",_,U,{size:[G,N]}],to=()=>[R,K,$],ta=()=>["","none","full",u,G,N],tl=()=>["",C,K,$],tu=()=>["solid","dashed","dotted","double"],th=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],td=()=>[C,R,Z,Y],tc=()=>["","none",m,G,N],tp=()=>["none",C,G,N],tm=()=>["none",C,G,N],tf=()=>[C,G,N],tg=()=>[V,"full",...S()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[j],breakpoint:[j],color:[L],container:[j],"drop-shadow":[j],ease:["in","out","in-out"],font:[z],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[j],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[j],shadow:[j],spacing:["px",C],text:[j],"text-shadow":[j],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",V,N,G,g]}],container:["container"],columns:[{columns:[C,N,G,a]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:T()}],"overflow-x":[{"overflow-x":T()}],"overflow-y":[{"overflow-y":T()}],overscroll:[{overscroll:P()}],"overscroll-x":[{"overscroll-x":P()}],"overscroll-y":[{"overscroll-y":P()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:A()}],"inset-x":[{"inset-x":A()}],"inset-y":[{"inset-y":A()}],start:[{start:A()}],end:[{end:A()}],top:[{top:A()}],right:[{right:A()}],bottom:[{bottom:A()}],left:[{left:A()}],visibility:["visible","invisible","collapse"],z:[{z:[D,"auto",G,N]}],basis:[{basis:[V,"full","auto",a,...S()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[C,V,"auto","initial","none",N]}],grow:[{grow:["",C,G,N]}],shrink:[{shrink:["",C,G,N]}],order:[{order:[D,"first","last","none",G,N]}],"grid-cols":[{"grid-cols":M()}],"col-start-end":[{col:E()}],"col-start":[{"col-start":F()}],"col-end":[{"col-end":F()}],"grid-rows":[{"grid-rows":M()}],"row-start-end":[{row:E()}],"row-start":[{"row-start":F()}],"row-end":[{"row-end":F()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":B()}],"auto-rows":[{"auto-rows":B()}],gap:[{gap:S()}],"gap-x":[{"gap-x":S()}],"gap-y":[{"gap-y":S()}],"justify-content":[{justify:[...O(),"normal"]}],"justify-items":[{"justify-items":[...I(),"normal"]}],"justify-self":[{"justify-self":["auto",...I()]}],"align-content":[{content:["normal",...O()]}],"align-items":[{items:[...I(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...I(),{baseline:["","last"]}]}],"place-content":[{"place-content":O()}],"place-items":[{"place-items":[...I(),"baseline"]}],"place-self":[{"place-self":["auto",...I()]}],p:[{p:S()}],px:[{px:S()}],py:[{py:S()}],ps:[{ps:S()}],pe:[{pe:S()}],pt:[{pt:S()}],pr:[{pr:S()}],pb:[{pb:S()}],pl:[{pl:S()}],m:[{m:tt()}],mx:[{mx:tt()}],my:[{my:tt()}],ms:[{ms:tt()}],me:[{me:tt()}],mt:[{mt:tt()}],mr:[{mr:tt()}],mb:[{mb:tt()}],ml:[{ml:tt()}],"space-x":[{"space-x":S()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":S()}],"space-y-reverse":["space-y-reverse"],size:[{size:te()}],w:[{w:[a,"screen",...te()]}],"min-w":[{"min-w":[a,"screen","none",...te()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[o]},...te()]}],h:[{h:["screen","lh",...te()]}],"min-h":[{"min-h":["screen","lh","none",...te()]}],"max-h":[{"max-h":["screen","lh",...te()]}],"font-size":[{text:["base",i,K,$]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,G,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",R,N]}],"font-family":[{font:[q,N,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,G,N]}],"line-clamp":[{"line-clamp":[C,"none",G,W]}],leading:[{leading:[n,...S()]}],"list-image":[{"list-image":["none",G,N]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",G,N]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ti()}],"text-color":[{text:ti()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...tu(),"wavy"]}],"text-decoration-thickness":[{decoration:[C,"from-font","auto",G,$]}],"text-decoration-color":[{decoration:ti()}],"underline-offset":[{"underline-offset":[C,"auto",G,N]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:S()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",G,N]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",G,N]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:tr()}],"bg-repeat":[{bg:ts()}],"bg-size":[{bg:tn()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},D,G,N],radial:["",G,N],conic:[D,G,N]},Q,X]}],"bg-color":[{bg:ti()}],"gradient-from-pos":[{from:to()}],"gradient-via-pos":[{via:to()}],"gradient-to-pos":[{to:to()}],"gradient-from":[{from:ti()}],"gradient-via":[{via:ti()}],"gradient-to":[{to:ti()}],rounded:[{rounded:ta()}],"rounded-s":[{"rounded-s":ta()}],"rounded-e":[{"rounded-e":ta()}],"rounded-t":[{"rounded-t":ta()}],"rounded-r":[{"rounded-r":ta()}],"rounded-b":[{"rounded-b":ta()}],"rounded-l":[{"rounded-l":ta()}],"rounded-ss":[{"rounded-ss":ta()}],"rounded-se":[{"rounded-se":ta()}],"rounded-ee":[{"rounded-ee":ta()}],"rounded-es":[{"rounded-es":ta()}],"rounded-tl":[{"rounded-tl":ta()}],"rounded-tr":[{"rounded-tr":ta()}],"rounded-br":[{"rounded-br":ta()}],"rounded-bl":[{"rounded-bl":ta()}],"border-w":[{border:tl()}],"border-w-x":[{"border-x":tl()}],"border-w-y":[{"border-y":tl()}],"border-w-s":[{"border-s":tl()}],"border-w-e":[{"border-e":tl()}],"border-w-t":[{"border-t":tl()}],"border-w-r":[{"border-r":tl()}],"border-w-b":[{"border-b":tl()}],"border-w-l":[{"border-l":tl()}],"divide-x":[{"divide-x":tl()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":tl()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...tu(),"hidden","none"]}],"divide-style":[{divide:[...tu(),"hidden","none"]}],"border-color":[{border:ti()}],"border-color-x":[{"border-x":ti()}],"border-color-y":[{"border-y":ti()}],"border-color-s":[{"border-s":ti()}],"border-color-e":[{"border-e":ti()}],"border-color-t":[{"border-t":ti()}],"border-color-r":[{"border-r":ti()}],"border-color-b":[{"border-b":ti()}],"border-color-l":[{"border-l":ti()}],"divide-color":[{divide:ti()}],"outline-style":[{outline:[...tu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[C,G,N]}],"outline-w":[{outline:["",C,K,$]}],"outline-color":[{outline:ti()}],shadow:[{shadow:["","none",h,J,H]}],"shadow-color":[{shadow:ti()}],"inset-shadow":[{"inset-shadow":["none",d,J,H]}],"inset-shadow-color":[{"inset-shadow":ti()}],"ring-w":[{ring:tl()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ti()}],"ring-offset-w":[{"ring-offset":[C,$]}],"ring-offset-color":[{"ring-offset":ti()}],"inset-ring-w":[{"inset-ring":tl()}],"inset-ring-color":[{"inset-ring":ti()}],"text-shadow":[{"text-shadow":["none",c,J,H]}],"text-shadow-color":[{"text-shadow":ti()}],opacity:[{opacity:[C,G,N]}],"mix-blend":[{"mix-blend":[...th(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":th()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[C]}],"mask-image-linear-from-pos":[{"mask-linear-from":td()}],"mask-image-linear-to-pos":[{"mask-linear-to":td()}],"mask-image-linear-from-color":[{"mask-linear-from":ti()}],"mask-image-linear-to-color":[{"mask-linear-to":ti()}],"mask-image-t-from-pos":[{"mask-t-from":td()}],"mask-image-t-to-pos":[{"mask-t-to":td()}],"mask-image-t-from-color":[{"mask-t-from":ti()}],"mask-image-t-to-color":[{"mask-t-to":ti()}],"mask-image-r-from-pos":[{"mask-r-from":td()}],"mask-image-r-to-pos":[{"mask-r-to":td()}],"mask-image-r-from-color":[{"mask-r-from":ti()}],"mask-image-r-to-color":[{"mask-r-to":ti()}],"mask-image-b-from-pos":[{"mask-b-from":td()}],"mask-image-b-to-pos":[{"mask-b-to":td()}],"mask-image-b-from-color":[{"mask-b-from":ti()}],"mask-image-b-to-color":[{"mask-b-to":ti()}],"mask-image-l-from-pos":[{"mask-l-from":td()}],"mask-image-l-to-pos":[{"mask-l-to":td()}],"mask-image-l-from-color":[{"mask-l-from":ti()}],"mask-image-l-to-color":[{"mask-l-to":ti()}],"mask-image-x-from-pos":[{"mask-x-from":td()}],"mask-image-x-to-pos":[{"mask-x-to":td()}],"mask-image-x-from-color":[{"mask-x-from":ti()}],"mask-image-x-to-color":[{"mask-x-to":ti()}],"mask-image-y-from-pos":[{"mask-y-from":td()}],"mask-image-y-to-pos":[{"mask-y-to":td()}],"mask-image-y-from-color":[{"mask-y-from":ti()}],"mask-image-y-to-color":[{"mask-y-to":ti()}],"mask-image-radial":[{"mask-radial":[G,N]}],"mask-image-radial-from-pos":[{"mask-radial-from":td()}],"mask-image-radial-to-pos":[{"mask-radial-to":td()}],"mask-image-radial-from-color":[{"mask-radial-from":ti()}],"mask-image-radial-to-color":[{"mask-radial-to":ti()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":w()}],"mask-image-conic-pos":[{"mask-conic":[C]}],"mask-image-conic-from-pos":[{"mask-conic-from":td()}],"mask-image-conic-to-pos":[{"mask-conic-to":td()}],"mask-image-conic-from-color":[{"mask-conic-from":ti()}],"mask-image-conic-to-color":[{"mask-conic-to":ti()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:tr()}],"mask-repeat":[{mask:ts()}],"mask-size":[{mask:tn()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",G,N]}],filter:[{filter:["","none",G,N]}],blur:[{blur:tc()}],brightness:[{brightness:[C,G,N]}],contrast:[{contrast:[C,G,N]}],"drop-shadow":[{"drop-shadow":["","none",p,J,H]}],"drop-shadow-color":[{"drop-shadow":ti()}],grayscale:[{grayscale:["",C,G,N]}],"hue-rotate":[{"hue-rotate":[C,G,N]}],invert:[{invert:["",C,G,N]}],saturate:[{saturate:[C,G,N]}],sepia:[{sepia:["",C,G,N]}],"backdrop-filter":[{"backdrop-filter":["","none",G,N]}],"backdrop-blur":[{"backdrop-blur":tc()}],"backdrop-brightness":[{"backdrop-brightness":[C,G,N]}],"backdrop-contrast":[{"backdrop-contrast":[C,G,N]}],"backdrop-grayscale":[{"backdrop-grayscale":["",C,G,N]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[C,G,N]}],"backdrop-invert":[{"backdrop-invert":["",C,G,N]}],"backdrop-opacity":[{"backdrop-opacity":[C,G,N]}],"backdrop-saturate":[{"backdrop-saturate":[C,G,N]}],"backdrop-sepia":[{"backdrop-sepia":["",C,G,N]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":S()}],"border-spacing-x":[{"border-spacing-x":S()}],"border-spacing-y":[{"border-spacing-y":S()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",G,N]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[C,"initial",G,N]}],ease:[{ease:["linear","initial",y,G,N]}],delay:[{delay:[C,G,N]}],animate:[{animate:["none",v,G,N]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,G,N]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:tp()}],"rotate-x":[{"rotate-x":tp()}],"rotate-y":[{"rotate-y":tp()}],"rotate-z":[{"rotate-z":tp()}],scale:[{scale:tm()}],"scale-x":[{"scale-x":tm()}],"scale-y":[{"scale-y":tm()}],"scale-z":[{"scale-z":tm()}],"scale-3d":["scale-3d"],skew:[{skew:tf()}],"skew-x":[{"skew-x":tf()}],"skew-y":[{"skew-y":tf()}],transform:[{transform:[G,N,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:tg()}],"translate-x":[{"translate-x":tg()}],"translate-y":[{"translate-y":tg()}],"translate-z":[{"translate-z":tg()}],"translate-none":["translate-none"],accent:[{accent:ti()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ti()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",G,N]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":S()}],"scroll-mx":[{"scroll-mx":S()}],"scroll-my":[{"scroll-my":S()}],"scroll-ms":[{"scroll-ms":S()}],"scroll-me":[{"scroll-me":S()}],"scroll-mt":[{"scroll-mt":S()}],"scroll-mr":[{"scroll-mr":S()}],"scroll-mb":[{"scroll-mb":S()}],"scroll-ml":[{"scroll-ml":S()}],"scroll-p":[{"scroll-p":S()}],"scroll-px":[{"scroll-px":S()}],"scroll-py":[{"scroll-py":S()}],"scroll-ps":[{"scroll-ps":S()}],"scroll-pe":[{"scroll-pe":S()}],"scroll-pt":[{"scroll-pt":S()}],"scroll-pr":[{"scroll-pr":S()}],"scroll-pb":[{"scroll-pb":S()}],"scroll-pl":[{"scroll-pl":S()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",G,N]}],fill:[{fill:["none",...ti()]}],"stroke-w":[{stroke:[C,K,$,W]}],stroke:[{stroke:["none",...ti()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},2582:(t,e,i)=>{i.d(e,{Q:()=>r});let r=(0,i(3210).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},2688:(t,e,i)=>{i.d(e,{A:()=>d});var r=i(3210);let s=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,i)=>i?i.toUpperCase():e.toLowerCase()),o=t=>{let e=n(t);return e.charAt(0).toUpperCase()+e.slice(1)},a=(...t)=>t.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim(),l=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,r.forwardRef)(({color:t="currentColor",size:e=24,strokeWidth:i=2,absoluteStrokeWidth:s,className:n="",children:o,iconNode:h,...d},c)=>(0,r.createElement)("svg",{ref:c,...u,width:e,height:e,stroke:t,strokeWidth:s?24*Number(i)/Number(e):i,className:a("lucide",n),...!o&&!l(d)&&{"aria-hidden":"true"},...d},[...h.map(([t,e])=>(0,r.createElement)(t,e)),...Array.isArray(o)?o:[o]])),d=(t,e)=>{let i=(0,r.forwardRef)(({className:i,...n},l)=>(0,r.createElement)(h,{ref:l,iconNode:e,className:a(`lucide-${s(o(t))}`,`lucide-${t}`,i),...n}));return i.displayName=o(t),i}},2743:(t,e,i)=>{i.d(e,{E:()=>s});var r=i(3210);let s=i(7044).B?r.useLayoutEffect:r.useEffect},2789:(t,e,i)=>{i.d(e,{M:()=>s});var r=i(3210);function s(t){let e=(0,r.useRef)(null);return null===e.current&&(e.current=t()),e.current}},2941:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},3872:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2688).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},4479:(t,e,i)=>{i.d(e,{G:()=>r});function r(t){return"object"==typeof t&&null!==t}},6001:(t,e,i)=>{let r;function s(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function n(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,r){if("function"==typeof e){let[s,o]=n(r);e=e(void 0!==i?i:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[s,o]=n(r);e=e(void 0!==i?i:t.custom,s,o)}return e}function a(t,e,i){let r=t.getProps();return o(r,e,void 0!==i?i:r.custom,t)}function l(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>nM});let u=t=>t,h={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],c={value:null,addProjectionMetrics:null};function p(t,e){let i=!1,r=!0,s={delta:0,timestamp:0,isProcessing:!1},n=()=>i=!0,o=d.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,r=new Set,s=!1,n=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,n=!1)=>{let a=n&&s?i:r;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{r.delete(t),o.delete(t)},process:t=>{if(a=t,s){n=!0;return}s=!0,[i,r]=[r,i],i.forEach(u),e&&c.value&&c.value.frameloop[e].push(l),l=0,i.clear(),s=!1,n&&(n=!1,h.process(t))}};return h}(n,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:p,update:m,preRender:f,render:g,postRender:y}=o,v=()=>{let n=h.useManualTiming?s.timestamp:performance.now();i=!1,h.useManualTiming||(s.delta=r?1e3/60:Math.max(Math.min(n-s.timestamp,40),1)),s.timestamp=n,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),p.process(s),m.process(s),f.process(s),g.process(s),y.process(s),s.isProcessing=!1,i&&e&&(r=!1,t(v))},b=()=>{i=!0,r=!0,s.isProcessing||t(v)};return{schedule:d.reduce((t,e)=>{let r=o[e];return t[e]=(t,e=!1,s=!1)=>(i||b(),r.schedule(t,e,s)),t},{}),cancel:t=>{for(let e=0;e<d.length;e++)o[d[e]].cancel(t)},state:s,steps:o}}let{schedule:m,cancel:f,state:g,steps:y}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(v),x=new Set(["width","height","top","left","right","bottom",...v]);function w(t,e){-1===t.indexOf(e)&&t.push(e)}function k(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class T{constructor(){this.subscriptions=[]}add(t){return w(this.subscriptions,t),()=>k(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](t,e,i);else for(let s=0;s<r;s++){let r=this.subscriptions[s];r&&r(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function P(){r=void 0}let S={now:()=>(void 0===r&&S.set(g.isProcessing||h.useManualTiming?g.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(P)}},A=t=>!isNaN(parseFloat(t)),M={current:void 0};class E{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=S.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=S.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=A(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new T);let i=this.events[t].add(e);return"change"===t?()=>{i(),m.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return M.current&&M.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=S.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function V(t,e){return new E(t,e)}let C=t=>Array.isArray(t),D=t=>!!(t&&t.getVelocity);function R(t,e){let i=t.getValue("willChange");if(D(i)&&i.add)return i.add(e);if(!i&&h.WillChange){let i=new h.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let j=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),L="data-"+j("framerAppearId"),F=(t,e)=>i=>e(t(i)),B=(...t)=>t.reduce(F),O=(t,e,i)=>i>e?e:i<t?t:i,I=t=>1e3*t,z=t=>t/1e3,U={layout:0,mainThread:0,waapi:0},N=()=>{},$=()=>{},W=t=>e=>"string"==typeof e&&e.startsWith(t),Y=W("--"),X=W("var(--"),H=t=>!!X(t)&&G.test(t.split("/*")[0].trim()),G=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,K={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},q={...K,transform:t=>O(0,1,t)},Z={...K,default:1},_=t=>Math.round(1e5*t)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>i=>!!("string"==typeof i&&J.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),te=(t,e,i)=>r=>{if("string"!=typeof r)return r;let[s,n,o,a]=r.match(Q);return{[t]:parseFloat(s),[e]:parseFloat(n),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ti=t=>O(0,255,t),tr={...K,transform:t=>Math.round(ti(t))},ts={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+tr.transform(t)+", "+tr.transform(e)+", "+tr.transform(i)+", "+_(q.transform(r))+")"},tn={test:tt("#"),parse:function(t){let e="",i="",r="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,r+=r,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}},transform:ts.transform},to=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ta=to("deg"),tl=to("%"),tu=to("px"),th=to("vh"),td=to("vw"),tc={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},tp={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:r=1})=>"hsla("+Math.round(t)+", "+tl.transform(_(e))+", "+tl.transform(_(i))+", "+_(q.transform(r))+")"},tm={test:t=>ts.test(t)||tn.test(t)||tp.test(t),parse:t=>ts.test(t)?ts.parse(t):tp.test(t)?tp.parse(t):tn.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?ts.transform(t):tp.transform(t),getAnimatableNone:t=>{let e=tm.parse(t);return e.alpha=0,tm.transform(e)}},tf=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tg="number",ty="color",tv=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tb(t){let e=t.toString(),i=[],r={color:[],number:[],var:[]},s=[],n=0,o=e.replace(tv,t=>(tm.test(t)?(r.color.push(n),s.push(ty),i.push(tm.parse(t))):t.startsWith("var(")?(r.var.push(n),s.push("var"),i.push(t)):(r.number.push(n),s.push(tg),i.push(parseFloat(t))),++n,"${}")).split("${}");return{values:i,split:o,indexes:r,types:s}}function tx(t){return tb(t).values}function tw(t){let{split:e,types:i}=tb(t),r=e.length;return t=>{let s="";for(let n=0;n<r;n++)if(s+=e[n],void 0!==t[n]){let e=i[n];e===tg?s+=_(t[n]):e===ty?s+=tm.transform(t[n]):s+=t[n]}return s}}let tk=t=>"number"==typeof t?0:tm.test(t)?tm.getAnimatableNone(t):t,tT={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Q)?.length||0)+(t.match(tf)?.length||0)>0},parse:tx,createTransformer:tw,getAnimatableNone:function(t){let e=tx(t);return tw(t)(e.map(tk))}};function tP(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tS(t,e){return i=>i>0?e:t}let tA=(t,e,i)=>t+(e-t)*i,tM=(t,e,i)=>{let r=t*t,s=i*(e*e-r)+r;return s<0?0:Math.sqrt(s)},tE=[tn,ts,tp],tV=t=>tE.find(e=>e.test(t));function tC(t){let e=tV(t);if(N(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tp&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let s=0,n=0,o=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,a=2*i-r;s=tP(a,r,t+1/3),n=tP(a,r,t),o=tP(a,r,t-1/3)}else s=n=o=i;return{red:Math.round(255*s),green:Math.round(255*n),blue:Math.round(255*o),alpha:r}}(i)),i}let tD=(t,e)=>{let i=tC(t),r=tC(e);if(!i||!r)return tS(t,e);let s={...i};return t=>(s.red=tM(i.red,r.red,t),s.green=tM(i.green,r.green,t),s.blue=tM(i.blue,r.blue,t),s.alpha=tA(i.alpha,r.alpha,t),ts.transform(s))},tR=new Set(["none","hidden"]);function tj(t,e){return i=>tA(t,e,i)}function tL(t){return"number"==typeof t?tj:"string"==typeof t?H(t)?tS:tm.test(t)?tD:tO:Array.isArray(t)?tF:"object"==typeof t?tm.test(t)?tD:tB:tS}function tF(t,e){let i=[...t],r=i.length,s=t.map((t,i)=>tL(t)(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=s[e](t);return i}}function tB(t,e){let i={...t,...e},r={};for(let s in i)void 0!==t[s]&&void 0!==e[s]&&(r[s]=tL(t[s])(t[s],e[s]));return t=>{for(let e in r)i[e]=r[e](t);return i}}let tO=(t,e)=>{let i=tT.createTransformer(e),r=tb(t),s=tb(e);return r.indexes.var.length===s.indexes.var.length&&r.indexes.color.length===s.indexes.color.length&&r.indexes.number.length>=s.indexes.number.length?tR.has(t)&&!s.values.length||tR.has(e)&&!r.values.length?function(t,e){return tR.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):B(tF(function(t,e){let i=[],r={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let n=e.types[s],o=t.indexes[n][r[n]],a=t.values[o]??0;i[s]=a,r[n]++}return i}(r,s),s.values),i):(N(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tS(t,e))};function tI(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tA(t,e,i):tL(t)(t,e)}let tz=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>m.update(e,t),stop:()=>f(e),now:()=>g.isProcessing?g.timestamp:S.now()}},tU=(t,e,i=10)=>{let r="",s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)r+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function tN(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function t$(t,e,i){var r,s;let n=Math.max(e-5,0);return r=i-t(n),(s=e-n)?1e3/s*r:0}let tW={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tY(t,e){return t*Math.sqrt(1-e*e)}let tX=["duration","bounce"],tH=["stiffness","damping","mass"];function tG(t,e){return e.some(e=>void 0!==t[e])}function tK(t=tW.visualDuration,e=tW.bounce){let i,r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:s,restDelta:n}=r,o=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:tW.velocity,stiffness:tW.stiffness,damping:tW.damping,mass:tW.mass,isResolvedFromDuration:!1,...t};if(!tG(t,tH)&&tG(t,tX))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),r=i*i,s=2*O(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:tW.mass,stiffness:r,damping:s}}else{let i=function({duration:t=tW.duration,bounce:e=tW.bounce,velocity:i=tW.velocity,mass:r=tW.mass}){let s,n;N(t<=I(tW.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=O(tW.minDamping,tW.maxDamping,o),t=O(tW.minDuration,tW.maxDuration,z(t)),o<1?(s=e=>{let r=e*o,s=r*t;return .001-(r-i)/tY(e,o)*Math.exp(-s)},n=e=>{let r=e*o*t,n=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-r),l=tY(Math.pow(e,2),o);return(r*i+i-n)*a*(-s(e)+.001>0?-1:1)/l}):(s=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),n=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(s,n,5/t);if(t=I(t),isNaN(a))return{stiffness:tW.stiffness,damping:tW.damping,duration:t};{let e=Math.pow(a,2)*r;return{stiffness:e,damping:2*o*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...i,mass:tW.mass}).isResolvedFromDuration=!0}return e}({...r,velocity:-z(r.velocity||0)}),f=p||0,g=h/(2*Math.sqrt(u*d)),y=a-o,v=z(Math.sqrt(u/d)),b=5>Math.abs(y);if(s||(s=b?tW.restSpeed.granular:tW.restSpeed.default),n||(n=b?tW.restDelta.granular:tW.restDelta.default),g<1){let t=tY(v,g);i=e=>a-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),r=Math.min(t*e,300);return a-i*((f+g*v*y)*Math.sinh(r)+t*y*Math.cosh(r))/t}}let x={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let r=0===t?f:0;g<1&&(r=0===t?I(f):t$(i,t,e));let o=Math.abs(a-e)<=n;l.done=Math.abs(r)<=s&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(tN(x),2e4),e=tU(e=>x.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return x}function tq({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:s=10,bounceStiffness:n=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,y=i*e,v=p+y,b=void 0===o?v:o(v);b!==v&&(y=b-p);let x=t=>-y*Math.exp(-t/r),w=t=>b+x(t),k=t=>{let e=x(t),i=w(t);m.done=Math.abs(e)<=u,m.value=m.done?b:i},T=t=>{f(m.value)&&(d=t,c=tK({keyframes:[m.value,g(m.value)],velocity:t$(w,t,m.value),damping:s,stiffness:n,restDelta:u,restSpeed:h}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,k(t),T(t)),void 0!==d&&t>=d)?c.next(t-d):(e||k(t),m)}}}tK.applyToOptions=t=>{let e=function(t,e=100,i){let r=i({...t,keyframes:[0,e]}),s=Math.min(tN(r),2e4);return{type:"keyframes",ease:t=>r.next(s*t).value/e,duration:z(s)}}(t,100,tK);return t.ease=e.ease,t.duration=I(e.duration),t.type="keyframes",t};let tZ=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function t_(t,e,i,r){if(t===e&&i===r)return u;let s=e=>(function(t,e,i,r,s){let n,o,a=0;do(n=tZ(o=e+(i-e)/2,r,s)-t)>0?i=o:e=o;while(Math.abs(n)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tZ(s(t),e,r)}let tQ=t_(.42,0,1,1),tJ=t_(0,0,.58,1),t0=t_(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t2=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t3=t=>e=>1-t(1-e),t5=t_(.33,1.53,.69,.99),t4=t3(t5),t9=t2(t4),t6=t=>(t*=2)<1?.5*t4(t):.5*(2-Math.pow(2,-10*(t-1))),t8=t=>1-Math.sin(Math.acos(t)),t7=t3(t8),et=t2(t8),ee=t=>Array.isArray(t)&&"number"==typeof t[0],ei={linear:u,easeIn:tQ,easeInOut:t0,easeOut:tJ,circIn:t8,circInOut:et,circOut:t7,backIn:t4,backInOut:t9,backOut:t5,anticipate:t6},er=t=>"string"==typeof t,es=t=>{if(ee(t)){$(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,r,s]=t;return t_(e,i,r,s)}return er(t)?($(void 0!==ei[t],`Invalid easing type '${t}'`),ei[t]):t},en=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r};function eo({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){var s;let n=t1(r)?r.map(es):es(r),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:r,mixer:s}={}){let n=t.length;if($(n===e.length,"Both input and output ranges must be the same length"),1===n)return()=>e[0];if(2===n&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[n-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let r=[],s=i||h.mix||tI,n=t.length-1;for(let i=0;i<n;i++){let n=s(t[i],t[i+1]);e&&(n=B(Array.isArray(e)?e[i]||u:e,n)),r.push(n)}return r}(e,r,s),l=a.length,d=i=>{if(o&&i<t[0])return e[0];let r=0;if(l>1)for(;r<t.length-2&&!(i<t[r+1]);r++);let s=en(t[r],t[r+1],i);return a[r](s)};return i?e=>d(O(t[0],t[n-1],e)):d}((s=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let s=en(0,e,r);t.push(tA(i,1,s))}}(e,t.length-1),e}(e),s.map(e=>e*t)),e,{ease:Array.isArray(n)?n:e.map(()=>n||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let ea=t=>null!==t;function el(t,{repeat:e,repeatType:i="loop"},r,s=1){let n=t.filter(ea),o=s<0||e&&"loop"!==i&&e%2==1?0:n.length-1;return o&&void 0!==r?r:n[o]}let eu={decay:tq,inertia:tq,tween:eo,keyframes:eo,spring:tK};function eh(t){"string"==typeof t.type&&(t.type=eu[t.type])}class ed{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ec=t=>t/100;class ep extends ed{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==S.now()&&this.tick(S.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},U.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;eh(t);let{type:e=eo,repeat:i=0,repeatDelay:r=0,repeatType:s,velocity:n=0}=t,{keyframes:o}=t,a=e||eo;a!==eo&&"number"!=typeof o[0]&&(this.mixKeyframes=B(ec,tI(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-n})),null===l.calculatedDuration&&(l.calculatedDuration=tN(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:r,mixKeyframes:s,mirroredGenerator:n,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-r/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,b=i;if(h){let t=Math.min(this.currentTime,r)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/o)):"mirror"===d&&(b=n)),v=O(0,1,i)*o}let x=y?{done:!1,value:u[0]}:b.next(v);s&&(x.value=s(x.value));let{done:w}=x;y||null===a||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return k&&p!==tq&&(x.value=el(u,this.options,f,this.speed)),m&&m(x.value),k&&this.finish(),x}then(t,e){return this.finished.then(t,e)}get duration(){return z(this.calculatedDuration)}get time(){return z(this.currentTime)}set time(t){t=I(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(S.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=z(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tz,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(S.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,U.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let em=t=>180*t/Math.PI,ef=t=>ey(em(Math.atan2(t[1],t[0]))),eg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ef,rotateZ:ef,skewX:t=>em(Math.atan(t[1])),skewY:t=>em(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ey=t=>((t%=360)<0&&(t+=360),t),ev=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),eb=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ex={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ev,scaleY:eb,scale:t=>(ev(t)+eb(t))/2,rotateX:t=>ey(em(Math.atan2(t[6],t[5]))),rotateY:t=>ey(em(Math.atan2(-t[2],t[0]))),rotateZ:ef,rotate:ef,skewX:t=>em(Math.atan(t[4])),skewY:t=>em(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ew(t){return+!!t.includes("scale")}function ek(t,e){let i,r;if(!t||"none"===t)return ew(e);let s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(s)i=ex,r=s;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=eg,r=e}if(!r)return ew(e);let n=i[e],o=r[1].split(",").map(eP);return"function"==typeof n?n(o):o[n]}let eT=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return ek(i,e)};function eP(t){return parseFloat(t.trim())}let eS=t=>t===K||t===tu,eA=new Set(["x","y","z"]),eM=v.filter(t=>!eA.has(t)),eE={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>ek(e,"x"),y:(t,{transform:e})=>ek(e,"y")};eE.translateX=eE.x,eE.translateY=eE.y;let eV=new Set,eC=!1,eD=!1,eR=!1;function ej(){if(eD){let t=Array.from(eV).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eM.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eD=!1,eC=!1,eV.forEach(t=>t.complete(eR)),eV.clear()}function eL(){eV.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eD=!0)})}class eF{constructor(t,e,i,r,s,n=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=r,this.element=s,this.isAsync=n}scheduleResolve(){this.state="scheduled",this.isAsync?(eV.add(this),eC||(eC=!0,m.read(eL),m.resolveKeyframes(ej))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:r}=this;if(null===t[0]){let s=r?.get(),n=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){let r=i.readValue(e,n);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=n),r&&void 0===s&&r.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eV.delete(this)}cancel(){"scheduled"===this.state&&(eV.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eB=t=>t.startsWith("--");function eO(t){let e;return()=>(void 0===e&&(e=t()),e)}let eI=eO(()=>void 0!==window.ScrollTimeline),ez={},eU=function(t,e){let i=eO(t);return()=>ez[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eN=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,e$={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eN([0,.65,.55,1]),circOut:eN([.55,0,1,.45]),backIn:eN([.31,.01,.66,-.59]),backOut:eN([.33,1.53,.69,.99])};function eW(t){return"function"==typeof t&&"applyToOptions"in t}class eY extends ed{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:r,pseudoElement:s,allowFlatten:n=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!s,this.allowFlatten=n,this.options=t,$("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return eW(t)&&eU()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:r=0,duration:s=300,repeat:n=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?eU()?tU(e,i):"ease-out":ee(e)?eN(e):Array.isArray(e)?e.map(e=>t(e,i)||e$.easeOut):e$[e]}(a,s);Array.isArray(d)&&(h.easing=d),c.value&&U.waapi++;let p={delay:r,duration:s,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:n+1,direction:"reverse"===o?"alternate":"normal"};u&&(p.pseudoElement=u);let m=t.animate(h,p);return c.value&&m.finished.finally(()=>{U.waapi--}),m}(e,i,r,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){let t=el(r,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eB(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return z(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return z(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=I(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eI())?(this.animation.timeline=t,u):e(this)}}let eX={anticipate:t6,backInOut:t9,circInOut:et};class eH extends eY{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eX&&(t.ease=eX[t.ease])}(t),eh(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:r,element:s,...n}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new ep({...n,autoplay:!1}),a=I(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eG=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tT.test(t)||"0"===t)&&!t.startsWith("url("));var eK,eq,eZ=i(8171);let e_=new Set(["opacity","clipPath","filter","transform"]),eQ=eO(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eJ extends ed{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:r=0,repeatDelay:s=0,repeatType:n="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=S.now();let d={autoplay:t,delay:e,type:i,repeat:r,repeatDelay:s,repeatType:n,name:a,motionValue:l,element:u,...h},c=u?.KeyframeResolver||eF;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,r){this.keyframeResolver=void 0;let{name:s,type:n,velocity:o,delay:a,isHandoff:l,onUpdate:d}=i;this.resolvedAt=S.now(),!function(t,e,i,r){let s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;let n=t[t.length-1],o=eG(s,e),a=eG(n,e);return N(o===a,`You are trying to animate ${e} from "${s}" to "${n}". ${s} is not an animatable value - to enable this animation set ${s} to a value animatable to ${n} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||eW(i))&&r)}(t,s,n,o)&&((h.instantAnimations||!a)&&d?.(el(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let c={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},p=!l&&function(t){let{motionValue:e,name:i,repeatDelay:r,repeatType:s,damping:n,type:o}=t;if(!(0,eZ.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eQ()&&i&&e_.has(i)&&("transform"!==i||!l)&&!a&&!r&&"mirror"!==s&&0!==n&&"inertia"!==o}(c)?new eH({...c,element:c.motionValue.owner.current}):new ep(c);p.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eR=!0,eL(),ej(),eR=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e0=t=>null!==t,e1={type:"spring",stiffness:500,damping:25,restSpeed:10},e2=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e3={type:"keyframes",duration:.8},e5={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e4=(t,{keyframes:e})=>e.length>2?e3:b.has(t)?t.startsWith("scale")?e2(e[1]):e1:e5,e9=(t,e,i,r={},s,n)=>o=>{let a=l(r,t)||{},u=a.delay||r.delay||0,{elapsed:d=0}=r;d-=I(u);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-d,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:n?void 0:s};!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:s,repeat:n,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&Object.assign(c,e4(t,c)),c.duration&&(c.duration=I(c.duration)),c.repeatDelay&&(c.repeatDelay=I(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let p=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(p=!0)),(h.instantAnimations||h.skipAnimations)&&(p=!0,c.duration=0,c.delay=0),c.allowFlatten=!a.type&&!a.ease,p&&!n&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},r){let s=t.filter(e0),n=e&&"loop"!==i&&e%2==1?0:s.length-1;return s[n]}(c.keyframes,a);if(void 0!==t)return void m.update(()=>{c.onUpdate(t),c.onComplete()})}return a.isSync?new ep(c):new eJ(c)};function e6(t,e,{delay:i=0,transitionOverride:r,type:s}={}){let{transition:n=t.getDefaultTransition(),transitionEnd:o,...u}=e;r&&(n=r);let h=[],d=s&&t.animationState&&t.animationState.getState()[s];for(let e in u){let r=t.getValue(e,t.latestValues[e]??null),s=u[e];if(void 0===s||d&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(d,e))continue;let o={delay:i,...l(n||{},e)},a=r.get();if(void 0!==a&&!r.isAnimating&&!Array.isArray(s)&&s===a&&!o.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){let i=t.props[L];if(i){let t=window.MotionHandoffAnimation(i,e,m);null!==t&&(o.startTime=t,c=!0)}}R(t,e),r.start(e9(e,r,s,t.shouldReduceMotion&&x.has(e)?{type:!1}:o,t,c));let p=r.animation;p&&h.push(p)}return o&&Promise.all(h).then(()=>{m.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:r={},...s}=a(t,e)||{};for(let e in s={...s,...i}){var n;let i=C(n=s[e])?n[n.length-1]||0:n;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,V(i))}}(t,o)})}),h}function e8(t,e,i={}){let r=a(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(s=i.transitionOverride);let n=r?()=>Promise.all(e6(t,r,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:n=0,staggerChildren:o,staggerDirection:a}=s;return function(t,e,i=0,r=0,s=0,n=1,o){let a=[],l=t.variantChildren.size,u=(l-1)*s,h="function"==typeof r,d=h?t=>r(t,l):1===n?(t=0)=>t*s:(t=0)=>u-t*s;return Array.from(t.variantChildren).sort(e7).forEach((t,s)=>{t.notify("AnimationStart",e),a.push(e8(t,e,{...o,delay:i+(h?0:r)+d(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,r,n,o,a,i)}:()=>Promise.resolve(),{when:l}=s;if(!l)return Promise.all([n(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[n,o]:[o,n];return t().then(()=>e())}}function e7(t,e){return t.sortNodePosition(e)}function it(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}function ie(t){return"string"==typeof t||Array.isArray(t)}let ii=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ir=["initial",...ii],is=ir.length,io=[...ii].reverse(),ia=ii.length;function il(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iu(){return{animate:il(!0),whileInView:il(),whileHover:il(),whileTap:il(),whileDrag:il(),whileFocus:il(),exit:il()}}class ih{constructor(t){this.isMounted=!1,this.node=t}update(){}}class id extends ih{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>e8(t,e,i)));else if("string"==typeof e)r=e8(t,e,i);else{let s="function"==typeof e?a(t,e,i.custom):e;r=Promise.all(e6(t,s,i))}return r.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iu(),r=!0,n=e=>(i,r)=>{let s=a(t,r,"exit"===e?t.presenceContext?.custom:void 0);if(s){let{transition:t,transitionEnd:e,...r}=s;i={...i,...r,...e}}return i};function o(o){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<is;t++){let r=ir[t],s=e.props[r];(ie(s)||!1===s)&&(i[r]=s)}return i}(t.parent)||{},h=[],d=new Set,c={},p=1/0;for(let e=0;e<ia;e++){var m,f;let a=io[e],g=i[a],y=void 0!==l[a]?l[a]:u[a],v=ie(y),b=a===o?g.isActive:null;!1===b&&(p=e);let x=y===u[a]&&y!==l[a]&&v;if(x&&r&&t.manuallyAnimateOnMount&&(x=!1),g.protectedKeys={...c},!g.isActive&&null===b||!y&&!g.prevProp||s(y)||"boolean"==typeof y)continue;let w=(m=g.prevProp,"string"==typeof(f=y)?f!==m:!!Array.isArray(f)&&!it(f,m)),k=w||a===o&&g.isActive&&!x&&v||e>p&&v,T=!1,P=Array.isArray(y)?y:[y],S=P.reduce(n(a),{});!1===b&&(S={});let{prevResolvedValues:A={}}=g,M={...A,...S},E=e=>{k=!0,d.has(e)&&(T=!0,d.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in M){let e=S[t],i=A[t];if(c.hasOwnProperty(t))continue;let r=!1;(C(e)&&C(i)?it(e,i):e===i)?void 0!==e&&d.has(t)?E(t):g.protectedKeys[t]=!0:null!=e?E(t):d.add(t)}g.prevProp=y,g.prevResolvedValues=S,g.isActive&&(c={...c,...S}),r&&t.blockInitialAnimation&&(k=!1);let V=!(x&&w)||T;k&&V&&h.push(...P.map(t=>({animation:t,options:{type:a}})))}if(d.size){let e={};if("boolean"!=typeof l.initial){let i=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let r=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=r??null}),h.push({animation:e})}let g=!!h.length;return r&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(g=!1),r=!1,g?e(h):Promise.resolve()}return{animateChanges:o,setActive:function(e,r){if(i[e].isActive===r)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,r)),i[e].isActive=r;let s=o(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iu(),r=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();s(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ic=0;class ip extends ih{constructor(){super(...arguments),this.id=ic++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let im={x:!1,y:!1};function ig(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}let iy=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iv(t){return{point:{x:t.pageX,y:t.pageY}}}let ib=t=>e=>iy(e)&&t(e,iv(e));function ix(t,e,i,r){return ig(t,e,ib(i),r)}function iw({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}function ik(t){return t.max-t.min}function iT(t,e,i,r=.5){t.origin=r,t.originPoint=tA(e.min,e.max,t.origin),t.scale=ik(i)/ik(e),t.translate=tA(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iP(t,e,i,r){iT(t.x,e.x,i.x,r?r.originX:void 0),iT(t.y,e.y,i.y,r?r.originY:void 0)}function iS(t,e,i){t.min=i.min+e.min,t.max=t.min+ik(e)}function iA(t,e,i){t.min=e.min-i.min,t.max=t.min+ik(e)}function iM(t,e,i){iA(t.x,e.x,i.x),iA(t.y,e.y,i.y)}let iE=()=>({translate:0,scale:1,origin:0,originPoint:0}),iV=()=>({x:iE(),y:iE()}),iC=()=>({min:0,max:0}),iD=()=>({x:iC(),y:iC()});function iR(t){return[t("x"),t("y")]}function ij(t){return void 0===t||1===t}function iL({scale:t,scaleX:e,scaleY:i}){return!ij(t)||!ij(e)||!ij(i)}function iF(t){return iL(t)||iB(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iB(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iO(t,e,i,r,s){return void 0!==s&&(t=r+s*(t-r)),r+i*(t-r)+e}function iI(t,e=0,i=1,r,s){t.min=iO(t.min,e,i,r,s),t.max=iO(t.max,e,i,r,s)}function iz(t,{x:e,y:i}){iI(t.x,e.translate,e.scale,e.originPoint),iI(t.y,i.translate,i.scale,i.originPoint)}function iU(t,e){t.min=t.min+e,t.max=t.max+e}function iN(t,e,i,r,s=.5){let n=tA(t.min,t.max,s);iI(t,e,i,n,r)}function i$(t,e){iN(t.x,e.x,e.scaleX,e.scale,e.originX),iN(t.y,e.y,e.scaleY,e.scale,e.originY)}function iW(t,e){return iw(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}let iY=({current:t})=>t?t.ownerDocument.defaultView:null;function iX(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iH=(t,e)=>Math.abs(t-e);class iG{constructor(t,e,{transformPagePoint:i,contextWindow:r=window,dragSnapToOrigin:s=!1,distanceThreshold:n=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iZ(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iH(t.x,e.x)**2+iH(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:r}=t,{timestamp:s}=g;this.history.push({...r,timestamp:s});let{onStart:n,onMove:o}=this.handlers;e||(n&&n(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iK(e,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let n=iZ("pointercancel"===t.type?this.lastMoveEventInfo:iK(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,n),r&&r(t,n)},!iy(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=n,this.contextWindow=r||window;let o=iK(iv(t),this.transformPagePoint),{point:a}=o,{timestamp:l}=g;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=e;u&&u(t,iZ(o,this.history)),this.removeListeners=B(ix(this.contextWindow,"pointermove",this.handlePointerMove),ix(this.contextWindow,"pointerup",this.handlePointerUp),ix(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function iK(t,e){return e?{point:e(t.point)}:t}function iq(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iZ({point:t},e){return{point:t,delta:iq(t,i_(e)),offset:iq(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,r=null,s=i_(t);for(;i>=0&&(r=t[i],!(s.timestamp-r.timestamp>I(.1)));)i--;if(!r)return{x:0,y:0};let n=z(s.timestamp-r.timestamp);if(0===n)return{x:0,y:0};let o={x:(s.x-r.x)/n,y:(s.y-r.y)/n};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function i_(t){return t[t.length-1]}function iQ(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iJ(t,e){let i=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,r]=[r,i]),{min:i,max:r}}function i0(t,e,i){return{min:i1(t,e),max:i1(t,i)}}function i1(t,e){return"number"==typeof t?t:t[e]||0}let i2=new WeakMap;class i3{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iD(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new iG(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iv(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:r,onDragStart:s}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(im[t])return null;else return im[t]=!0,()=>{im[t]=!1};return im.x||im.y?null:(im.x=im.y=!0,()=>{im.x=im.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iR(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[t];r&&(e=ik(r)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&m.postRender(()=>s(t,e)),R(this.visualElement,"transform");let{animationState:n}=this.visualElement;n&&n.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:s,onDrag:n}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),n&&n(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>iR(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,distanceThreshold:i,contextWindow:iY(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,r=e||this.latestPanInfo,s=this.isDragging;if(this.cancel(),!s||!r||!i)return;let{velocity:n}=r;this.startAnimation(n);let{onDragEnd:o}=this.getProps();o&&m.postRender(()=>o(i,r))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:r}=this.getProps();if(!i||!i5(t,r,this.currentDirection))return;let s=this.getAxisMotionValue(t),n=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(n=function(t,{min:e,max:i},r){return void 0!==e&&t<e?t=r?tA(e,t,r.min):Math.max(t,e):void 0!==i&&t>i&&(t=r?tA(i,t,r.max):Math.min(t,i)),t}(n,this.constraints[t],this.elastic[t])),s.set(n)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;t&&iX(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:r,right:s}){return{x:iQ(t.x,i,s),y:iQ(t.y,e,r)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i0(t,"left","right"),y:i0(t,"top","bottom")}}(e),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iR(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iX(e))return!1;let r=e.current;$(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:s}=this.visualElement;if(!s||!s.layout)return!1;let n=function(t,e,i){let r=iW(t,i),{scroll:s}=e;return s&&(iU(r.x,s.offset.x),iU(r.y,s.offset.y)),r}(r,s.root,this.visualElement.getTransformPagePoint()),o=(t=s.layout.layoutBox,{x:iJ(t.x,n.x),y:iJ(t.y,n.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iw(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:r,dragTransition:s,dragSnapToOrigin:n,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iR(o=>{if(!i5(o,e,this.currentDirection))return;let l=a&&a[o]||{};n&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return R(this.visualElement,t),i.start(e9(t,i,0,e,this.visualElement,!1))}stopAnimation(){iR(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iR(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iR(e=>{let{drag:i}=this.getProps();if(!i5(e,i,this.currentDirection))return;let{projection:r}=this.visualElement,s=this.getAxisMotionValue(e);if(r&&r.layout){let{min:i,max:n}=r.layout.layoutBox[e];s.set(t[e]-tA(i,n,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iX(e)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};iR(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();r[t]=function(t,e){let i=.5,r=ik(t),s=ik(e);return s>r?i=en(e.min,e.max-r,t.min):r>s&&(i=en(t.min,t.max-s,e.min)),O(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iR(e=>{if(!i5(e,t,null))return;let i=this.getAxisMotionValue(e),{min:s,max:n}=this.constraints[e];i.set(tA(s,n,r[e]))})}addListeners(){if(!this.visualElement.current)return;i2.set(this.visualElement,this);let t=ix(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iX(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),m.read(e);let s=ig(window,"resize",()=>this.scalePositionWithinConstraints()),n=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iR(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),r(),n&&n()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:s=!1,dragElastic:n=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:r,dragConstraints:s,dragElastic:n,dragMomentum:o}}}function i5(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i4 extends ih{constructor(t){super(t),this.removeGroupControls=u,this.removeListeners=u,this.controls=new i3(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let i9=t=>(e,i)=>{t&&m.postRender(()=>t(e,i))};class i6 extends ih{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(t){this.session=new iG(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iY(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:i9(t),onStart:i9(e),onMove:i,onEnd:(t,e)=>{delete this.session,r&&m.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=ix(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i8=i(687);let{schedule:i7}=p(queueMicrotask,!1);var rt=i(3210),re=i(6044),ri=i(2157);let rr=(0,rt.createContext)({}),rs={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rn(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let ro={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tu.test(t))return t;else t=parseFloat(t);let i=rn(t,e.target.x),r=rn(t,e.target.y);return`${i}% ${r}%`}},ra={},rl=!1;class ru extends rt.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:s}=t;for(let t in rd)ra[t]=rd[t],Y(t)&&(ra[t].isCSSVariable=!0);s&&(e.group&&e.group.add(s),i&&i.register&&r&&i.register(s),rl&&s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),rs.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:r,isPresent:s}=this.props,{projection:n}=i;return n&&(n.isPresent=s,rl=!0,r||t.layoutDependency!==e||void 0===e||t.isPresent!==s?n.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?n.promote():n.relegate()||m.postRender(()=>{let t=n.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),i7.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function rh(t){let[e,i]=(0,re.xQ)(),r=(0,rt.useContext)(ri.L);return(0,i8.jsx)(ru,{...t,layoutGroup:r,switchLayoutGroup:(0,rt.useContext)(rr),isPresent:e,safeToRemove:i})}let rd={borderRadius:{...ro,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ro,borderTopRightRadius:ro,borderBottomLeftRadius:ro,borderBottomRightRadius:ro,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let r=tT.parse(t);if(r.length>5)return t;let s=tT.createTransformer(t),n=+("number"!=typeof r[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;r[0+n]/=o,r[1+n]/=a;let l=tA(o,a,.5);return"number"==typeof r[2+n]&&(r[2+n]/=l),"number"==typeof r[3+n]&&(r[3+n]/=l),s(r)}}};var rc=i(4479);function rp(t){return(0,rc.G)(t)&&"ownerSVGElement"in t}let rm=(t,e)=>t.depth-e.depth;class rf{constructor(){this.children=[],this.isDirty=!1}add(t){w(this.children,t),this.isDirty=!0}remove(t){k(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(rm),this.isDirty=!1,this.children.forEach(t)}}function rg(t){return D(t)?t.get():t}let ry=["TopLeft","TopRight","BottomLeft","BottomRight"],rv=ry.length,rb=t=>"string"==typeof t?parseFloat(t):t,rx=t=>"number"==typeof t||tu.test(t);function rw(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let rk=rP(0,.5,t7),rT=rP(.5,.95,u);function rP(t,e,i){return r=>r<t?0:r>e?1:i(en(t,e,r))}function rS(t,e){t.min=e.min,t.max=e.max}function rA(t,e){rS(t.x,e.x),rS(t.y,e.y)}function rM(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function rE(t,e,i,r,s){return t-=e,t=r+1/i*(t-r),void 0!==s&&(t=r+1/s*(t-r)),t}function rV(t,e,[i,r,s],n,o){!function(t,e=0,i=1,r=.5,s,n=t,o=t){if(tl.test(e)&&(e=parseFloat(e),e=tA(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tA(n.min,n.max,r);t===n&&(a-=e),t.min=rE(t.min,e,i,a,s),t.max=rE(t.max,e,i,a,s)}(t,e[i],e[r],e[s],e.scale,n,o)}let rC=["x","scaleX","originX"],rD=["y","scaleY","originY"];function rR(t,e,i,r){rV(t.x,e,rC,i?i.x:void 0,r?r.x:void 0),rV(t.y,e,rD,i?i.y:void 0,r?r.y:void 0)}function rj(t){return 0===t.translate&&1===t.scale}function rL(t){return rj(t.x)&&rj(t.y)}function rF(t,e){return t.min===e.min&&t.max===e.max}function rB(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function rO(t,e){return rB(t.x,e.x)&&rB(t.y,e.y)}function rI(t){return ik(t.x)/ik(t.y)}function rz(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class rU{constructor(){this.members=[]}add(t){w(this.members,t),t.scheduleRender()}remove(t){if(k(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rN={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},r$=["","X","Y","Z"],rW=0;function rY(t,e,i,r){let{latestValues:s}=e;s[t]&&(i[t]=s[t],e.setStaticValue(t,0),r&&(r[t]=0))}function rX({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:r,resetTransform:s}){return class{constructor(t={},i=e?.()){this.id=rW++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,c.value&&(rN.nodes=rN.calculatedTargetDeltas=rN.calculatedProjections=0),this.nodes.forEach(rK),this.nodes.forEach(r1),this.nodes.forEach(r2),this.nodes.forEach(rq),c.addProjectionMetrics&&c.addProjectionMetrics(rN)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new rf)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new T),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=rp(e)&&!(rp(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:r,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),t){let i,r=0,s=()=>this.root.updateBlockedByResize=!1;m.read(()=>{r=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==r&&(r=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=S.now(),r=({timestamp:s})=>{let n=s-i;n>=250&&(f(r),t(n-e))};return m.setup(r,!0),()=>f(r)}(s,250),rs.hasAnimatedSinceResize&&(rs.hasAnimatedSinceResize=!1,this.nodes.forEach(r0)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||s.getDefaultTransition()||r8,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=s.getProps(),u=!this.targetLayout||!rO(this.targetLayout,r),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...l(n,"layout"),onPlay:o,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||r0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r3),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let r=i.props[L];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",m,!(t||i))}let{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&t(s)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(r_);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(rQ);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(rJ),this.nodes.forEach(rH),this.nodes.forEach(rG)):this.nodes.forEach(rQ),this.clearAllSnapshots();let t=S.now();g.delta=O(0,1e3/60,t-g.timestamp),g.timestamp=t,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,i7.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rZ),this.sharedNodes.forEach(r5)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||ik(this.snapshot.measuredBox.x)||ik(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iD(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!rL(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,n=r!==this.prevTransformTemplateValue;t&&this.instance&&(e||iF(this.latestValues)||n)&&(s(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),se((e=r).x),se(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iD();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(sr))){let{scroll:t}=this.root;t&&(iU(e.x,t.offset.x),iU(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iD();if(rA(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:s,options:n}=r;r!==this.root&&s&&n.layoutScroll&&(s.wasRoot&&rA(e,t),iU(e.x,s.offset.x),iU(e.y,s.offset.y))}return e}applyTransform(t,e=!1){let i=iD();rA(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&i$(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),iF(r.latestValues)&&i$(i,r.latestValues)}return iF(this.latestValues)&&i$(i,this.latestValues),i}removeTransform(t){let e=iD();rA(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iF(i.latestValues))continue;iL(i.latestValues)&&i.updateSnapshot();let r=iD();rA(r,i.measurePageBox()),rR(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return iF(this.latestValues)&&rR(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:s}=this.options;if(this.layout&&(r||s)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iD(),this.relativeTargetOrigin=iD(),iM(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),rA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iD(),this.targetWithTransforms=iD()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var n,o,a;this.forceRelativeParentToResolveTarget(),n=this.target,o=this.relativeTarget,a=this.relativeParent.target,iS(n.x,o.x,a.x),iS(n.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rA(this.target,this.layout.layoutBox),iz(this.target,this.targetDelta)):rA(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iD(),this.relativeTargetOrigin=iD(),iM(this.relativeTargetOrigin,this.target,t.target),rA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}c.value&&rN.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iL(this.parent.latestValues)||iB(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===g.timestamp&&(i=!1),i)return;let{layout:r,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||s))return;rA(this.layoutCorrected,this.layout.layoutBox);let n=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,r=!1){let s,n,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){n=(s=i[a]).projectionDelta;let{visualElement:o}=s.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&i$(t,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),n&&(e.x*=n.x.scale,e.y*=n.y.scale,iz(t,n)),r&&iF(s.latestValues)&&i$(t,s.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iD());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rM(this.prevProjectionDelta.x,this.projectionDelta.x),rM(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iP(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===n&&this.treeScale.y===o&&rz(this.projectionDelta.x,this.prevProjectionDelta.x)&&rz(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),c.value&&rN.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iV(),this.projectionDelta=iV(),this.projectionDeltaWithTransform=iV()}setAnimationOrigin(t,e=!1){let i,r=this.snapshot,s=r?r.latestValues:{},n={...this.latestValues},o=iV();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iD(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(r6));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(r4(o.x,t.x,r),r4(o.y,t.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,g;iM(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=r,r9(p.x,m.x,f.x,g),r9(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,c=i,rF(u.x,c.x)&&rF(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iD()),rA(i,this.relativeTarget)}l&&(this.animationValues=n,function(t,e,i,r,s,n){s?(t.opacity=tA(0,i.opacity??1,rk(r)),t.opacityExit=tA(e.opacity??1,0,rT(r))):n&&(t.opacity=tA(e.opacity??1,i.opacity??1,r));for(let s=0;s<rv;s++){let n=`border${ry[s]}Radius`,o=rw(e,n),a=rw(i,n);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||rx(o)===rx(a)?(t[n]=Math.max(tA(rb(o),rb(a),r),0),(tl.test(a)||tl.test(o))&&(t[n]+="%")):t[n]=a)}(e.rotate||i.rotate)&&(t.rotate=tA(e.rotate||0,i.rotate||0,r))}(n,s,this.latestValues,r,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{rs.hasAnimatedSinceResize=!0,U.layout++,this.motionValue||(this.motionValue=V(0)),this.currentAnimation=function(t,e,i){let r=D(t)?t:V(t);return r.start(e9("",r,e,i)),r.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{U.layout--},onComplete:()=>{U.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:s}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&si(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||iD();let e=ik(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=ik(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}rA(e,i),i$(e,s),iP(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new rU),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let r={};i.z&&rY("z",t,r,this.animationValues);for(let e=0;e<r$.length;e++)rY(`rotate${r$[e]}`,t,r,this.animationValues),rY(`skew${r$[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=rg(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=rg(e?.pointerEvents)||""),this.hasProjected&&!iF(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let s=r.animationValues||r.latestValues;this.applyTransformsToTarget();let n=function(t,e,i){let r="",s=t.x.translate/e.x,n=t.y.translate/e.y,o=i?.z||0;if((s||n||o)&&(r=`translate3d(${s}px, ${n}px, ${o}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:s,rotateY:n,skewX:o,skewY:a}=i;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),s&&(r+=`rotateX(${s}deg) `),n&&(r+=`rotateY(${n}deg) `),o&&(r+=`skewX(${o}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,s);i&&(n=i(s,n)),t.transform=n;let{x:o,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,r.animationValues?t.opacity=r===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:t.opacity=r===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,ra){if(void 0===s[e])continue;let{correct:i,applyTo:o,isCSSVariable:a}=ra[e],l="none"===n?s[e]:i(s[e],r);if(o){let e=o.length;for(let i=0;i<e;i++)t[o[i]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=r===this?rg(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(r_),this.root.sharedNodes.clear()}}}function rH(t){t.updateLayout()}function rG(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:r}=t.layout,{animationType:s}=t.options,n=e.source!==t.layout.source;"size"===s?iR(t=>{let r=n?e.measuredBox[t]:e.layoutBox[t],s=ik(r);r.min=i[t].min,r.max=r.min+s}):si(s,e.layoutBox,i)&&iR(r=>{let s=n?e.measuredBox[r]:e.layoutBox[r],o=ik(i[r]);s.max=s.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+o)});let o=iV();iP(o,i,e.layoutBox);let a=iV();n?iP(a,t.applyTransform(r,!0),e.measuredBox):iP(a,i,e.layoutBox);let l=!rL(o),u=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:s,layout:n}=r;if(s&&n){let o=iD();iM(o,e.layoutBox,s.layoutBox);let a=iD();iM(a,i,n.layoutBox),rO(o,a)||(u=!0),r.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function rK(t){c.value&&rN.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function rq(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function rZ(t){t.clearSnapshot()}function r_(t){t.clearMeasurements()}function rQ(t){t.isLayoutDirty=!1}function rJ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function r0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function r1(t){t.resolveTargetDelta()}function r2(t){t.calcProjection()}function r3(t){t.resetSkewAndRotation()}function r5(t){t.removeLeadSnapshot()}function r4(t,e,i){t.translate=tA(e.translate,0,i),t.scale=tA(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function r9(t,e,i,r){t.min=tA(e.min,i.min,r),t.max=tA(e.max,i.max,r)}function r6(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let r8={duration:.45,ease:[.4,0,.1,1]},r7=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),st=r7("applewebkit/")&&!r7("chrome/")?Math.round:u;function se(t){t.min=st(t.min),t.max=st(t.max)}function si(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(rI(e)-rI(i)))}function sr(t){return t!==t.root&&t.scroll?.wasRoot}let ss=rX({attachResizeListener:(t,e)=>ig(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),sn={current:void 0},so=rX({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!sn.current){let t=new ss({});t.mount(window),t.setOptions({layoutScroll:!0}),sn.current=t}return sn.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function sa(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),r=new AbortController;return[i,{passive:!0,...e,signal:r.signal},()=>r.abort()]}function sl(t){return!("touch"===t.pointerType||im.x||im.y)}function su(t,e,i){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===i);let s=r["onHover"+i];s&&m.postRender(()=>s(e,iv(e)))}class sh extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,s,n]=sa(t,i),o=t=>{if(!sl(t))return;let{target:i}=t,r=e(i,t);if("function"!=typeof r||!i)return;let n=t=>{sl(t)&&(r(t),i.removeEventListener("pointerleave",n))};i.addEventListener("pointerleave",n,s)};return r.forEach(t=>{t.addEventListener("pointerenter",o,s)}),n}(t,(t,e)=>(su(this.node,e,"Start"),t=>su(this.node,t,"End"))))}unmount(){}}class sd extends ih{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=B(ig(this.node.current,"focus",()=>this.onFocus()),ig(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let sc=(t,e)=>!!e&&(t===e||sc(t,e.parentElement)),sp=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),sm=new WeakSet;function sf(t){return e=>{"Enter"===e.key&&t(e)}}function sg(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let sy=(t,e)=>{let i=t.currentTarget;if(!i)return;let r=sf(()=>{if(sm.has(i))return;sg(i,"down");let t=sf(()=>{sg(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>sg(i,"cancel"),e)});i.addEventListener("keydown",r,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),e)};function sv(t){return iy(t)&&!(im.x||im.y)}function sb(t,e,i){let{props:r}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===i);let s=r["onTap"+("End"===i?"":i)];s&&m.postRender(()=>s(e,iv(e)))}class sx extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,s,n]=sa(t,i),o=t=>{let r=t.currentTarget;if(!sv(t))return;sm.add(r);let n=e(r,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),sm.has(r)&&sm.delete(r),sv(t)&&"function"==typeof n&&n(t,{success:e})},a=t=>{o(t,r===window||r===document||i.useGlobalTarget||sc(r,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return r.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,s),(0,eZ.s)(t))&&(t.addEventListener("focus",t=>sy(t,s)),sp.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),n}(t,(t,e)=>(sb(this.node,e,"Start"),(t,{success:e})=>sb(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let sw=new WeakMap,sk=new WeakMap,sT=t=>{let e=sw.get(t.target);e&&e(t)},sP=t=>{t.forEach(sT)},sS={some:0,all:1};class sA extends ih{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:s}=t,n={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:sS[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;sk.has(i)||sk.set(i,{});let r=sk.get(i),s=JSON.stringify(e);return r[s]||(r[s]=new IntersectionObserver(sP,{root:t,...e})),r[s]}(e);return sw.set(t,i),r.observe(t),()=>{sw.delete(t),r.unobserve(t)}}(this.node.current,n,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,s&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),n=e?i:r;n&&n(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let sM=(0,rt.createContext)({strict:!1});var sE=i(2582);let sV=(0,rt.createContext)({});function sC(t){return s(t.animate)||ir.some(e=>ie(t[e]))}function sD(t){return!!(sC(t)||t.variants)}function sR(t){return Array.isArray(t)?t.join(" "):t}var sj=i(7044);let sL={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},sF={};for(let t in sL)sF[t]={isEnabled:e=>sL[t].some(t=>!!e[t])};let sB=Symbol.for("motionComponentSymbol");var sO=i(1279),sI=i(2743);function sz(t,{layout:e,layoutId:i}){return b.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!ra[t]||"opacity"===t)}let sU=(t,e)=>e&&"number"==typeof t?e.transform(t):t,sN={...K,transform:Math.round},s$={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:ta,rotateX:ta,rotateY:ta,rotateZ:ta,scale:Z,scaleX:Z,scaleY:Z,scaleZ:Z,skew:ta,skewX:ta,skewY:ta,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:q,originX:tc,originY:tc,originZ:tu,zIndex:sN,fillOpacity:q,strokeOpacity:q,numOctaves:sN},sW={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},sY=v.length;function sX(t,e,i){let{style:r,vars:s,transformOrigin:n}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(b.has(t)){o=!0;continue}if(Y(t)){s[t]=i;continue}{let e=sU(i,s$[t]);t.startsWith("origin")?(a=!0,n[t]=e):r[t]=e}}if(!e.transform&&(o||i?r.transform=function(t,e,i){let r="",s=!0;for(let n=0;n<sY;n++){let o=v[n],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=sU(a,s$[o]);if(!l){s=!1;let e=sW[o]||o;r+=`${e}(${t}) `}i&&(e[o]=t)}}return r=r.trim(),i?r=i(e,s?"":r):s&&(r="none"),r}(e,t.transform,i):r.transform&&(r.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=n;r.transformOrigin=`${t} ${e} ${i}`}}let sH=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function sG(t,e,i){for(let r in e)D(e[r])||sz(r,i)||(t[r]=e[r])}let sK={offset:"stroke-dashoffset",array:"stroke-dasharray"},sq={offset:"strokeDashoffset",array:"strokeDasharray"};function sZ(t,{attrX:e,attrY:i,attrScale:r,pathLength:s,pathSpacing:n=1,pathOffset:o=0,...a},l,u,h){if(sX(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=h?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==r&&(d.scale=r),void 0!==s&&function(t,e,i=1,r=0,s=!0){t.pathLength=1;let n=s?sK:sq;t[n.offset]=tu.transform(-r);let o=tu.transform(e),a=tu.transform(i);t[n.array]=`${o} ${a}`}(d,s,n,o,!1)}let s_=()=>({...sH(),attrs:{}}),sQ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),sJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function s0(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||sJ.has(t)}let s1=t=>!s0(t);try{!function(t){"function"==typeof t&&(s1=e=>e.startsWith("on")?!s0(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let s2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function s3(t){if("string"!=typeof t||t.includes("-"));else if(s2.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var s5=i(2789);let s4=t=>(e,i)=>{let r=(0,rt.useContext)(sV),n=(0,rt.useContext)(sO.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,r,n){return{latestValues:function(t,e,i,r){let n={},a=r(t,{});for(let t in a)n[t]=rg(a[t]);let{initial:l,animate:u}=t,h=sC(t),d=sD(t);e&&d&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!s(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let r=o(t,e[i]);if(r){let{transitionEnd:t,transition:e,...i}=r;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(n[t]=e)}for(let e in t)n[e]=t[e]}}}return n}(i,r,n,t),renderState:e()}})(t,e,r,n);return i?a():(0,s5.M)(a)};function s9(t,e,i){let{style:r}=t,s={};for(let n in r)(D(r[n])||e.style&&D(e.style[n])||sz(n,t)||i?.getValue(n)?.liveStyle!==void 0)&&(s[n]=r[n]);return s}let s6={useVisualState:s4({scrapeMotionValuesFromProps:s9,createRenderState:sH})};function s8(t,e,i){let r=s9(t,e,i);for(let i in t)(D(t[i])||D(e[i]))&&(r[-1!==v.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}let s7={useVisualState:s4({scrapeMotionValuesFromProps:s8,createRenderState:s_})},nt=t=>e=>e.test(t),ne=[K,tu,tl,ta,td,th,{test:t=>"auto"===t,parse:t=>t}],ni=t=>ne.find(nt(t)),nr=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ns=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,nn=t=>/^0[^.\s]+$/u.test(t),no=new Set(["brightness","contrast","saturate","opacity"]);function na(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(Q)||[];if(!r)return t;let s=i.replace(r,""),n=+!!no.has(e);return r!==i&&(n*=100),e+"("+n+s+")"}let nl=/\b([a-z-]*)\(.*?\)/gu,nu={...tT,getAnimatableNone:t=>{let e=t.match(nl);return e?e.map(na).join(" "):t}},nh={...s$,color:tm,backgroundColor:tm,outlineColor:tm,fill:tm,stroke:tm,borderColor:tm,borderTopColor:tm,borderRightColor:tm,borderBottomColor:tm,borderLeftColor:tm,filter:nu,WebkitFilter:nu},nd=t=>nh[t];function nc(t,e){let i=nd(t);return i!==nu&&(i=tT),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let np=new Set(["auto","none","0"]);class nm extends eF{constructor(t,e,i,r,s){super(t,e,i,r,s,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let r=t[i];if("string"==typeof r&&H(r=r.trim())){let s=function t(e,i,r=1){$(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[s,n]=function(t){let e=ns.exec(t);if(!e)return[,];let[,i,r,s]=e;return[`--${i??r}`,s]}(e);if(!s)return;let o=window.getComputedStyle(i).getPropertyValue(s);if(o){let t=o.trim();return nr(t)?parseFloat(t):t}return H(n)?t(n,i,r+1):n}(r,e.current);void 0!==s&&(t[i]=s),i===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!x.has(i)||2!==t.length)return;let[r,s]=t,n=ni(r),o=ni(s);if(n!==o)if(eS(n)&&eS(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eE[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var r;(null===t[e]||("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||nn(r)))&&i.push(e)}i.length&&function(t,e,i){let r,s=0;for(;s<t.length&&!r;){let e=t[s];"string"==typeof e&&!np.has(e)&&tb(e).values.length&&(r=t[s]),s++}if(r&&i)for(let s of e)t[s]=nc(i,r)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eE[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(i,r).jump(r,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let r=t.getValue(e);r&&r.jump(this.measuredOrigin,!1);let s=i.length-1,n=i[s];i[s]=eE[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==n&&void 0===this.finalKeyframe&&(this.finalKeyframe=n),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let nf=[...ne,tm,tT],ng=t=>nf.find(nt(t)),ny={current:null},nv={current:!1},nb=new WeakMap,nx=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class nw{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:s,visualState:n},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eF,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=S.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,m.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=n;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.blockInitialAnimation=!!s,this.isControllingVariants=sC(e),this.isVariantNode=sD(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&D(e)&&e.set(a[t],!1)}}mount(t){this.current=t,nb.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),nv.current||function(){if(nv.current=!0,sj.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ny.current=t.matches;t.addEventListener("change",e),e()}else ny.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ny.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=b.has(t);r&&this.onBindTransform&&this.onBindTransform();let s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&m.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),n=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),n(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in sF){let e=sF[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iD()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<nx.length;e++){let i=nx[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let r in e){let s=e[r],n=i[r];if(D(s))t.addValue(r,s);else if(D(n))t.addValue(r,V(s,{owner:t}));else if(n!==s)if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(r);t.addValue(r,V(void 0!==e?e:s,{owner:t}))}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=V(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(nr(i)||nn(i))?i=parseFloat(i):!ng(i)&&tT.test(e)&&(i=nc(t,e)),this.setBaseTarget(t,D(i)?i.get():i)),D(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let r=o(this.props,i,this.presenceContext?.custom);r&&(e=r[t])}if(i&&void 0!==e)return e;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||D(r)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new T),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class nk extends nw{constructor(){super(...arguments),this.KeyframeResolver=nm}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;D(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function nT(t,{style:e,vars:i},r,s){let n,o=t.style;for(n in e)o[n]=e[n];for(n in s?.applyProjectionStyles(o,r),i)o.setProperty(n,i[n])}class nP extends nk{constructor(){super(...arguments),this.type="html",this.renderInstance=nT}readValueFromInstance(t,e){if(b.has(e))return this.projection?.isProjecting?ew(e):eT(t,e);{let i=window.getComputedStyle(t),r=(Y(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iW(t,e)}build(t,e,i){sX(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return s9(t,e,i)}}let nS=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class nA extends nk{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iD}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(b.has(e)){let t=nd(e);return t&&t.default||0}return e=nS.has(e)?e:j(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return s8(t,e,i)}build(t,e,i){sZ(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,r){for(let i in nT(t,e,void 0,r),e.attrs)t.setAttribute(nS.has(i)?i:j(i),e.attrs[i])}mount(t){this.isSVGTag=sQ(t.tagName),super.mount(t)}}let nM=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,r)=>"create"===r?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}((eK={animation:{Feature:id},exit:{Feature:ip},inView:{Feature:sA},tap:{Feature:sx},focus:{Feature:sd},hover:{Feature:sh},pan:{Feature:i6},drag:{Feature:i4,ProjectionNode:so,MeasureLayout:rh},layout:{ProjectionNode:so,MeasureLayout:rh}},eq=(t,e)=>s3(t)?new nA(e):new nP(e,{allowProjection:t!==rt.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:r,Component:s}){function n(t,n){var o,a,l;let u,h={...(0,rt.useContext)(sE.Q),...t,layoutId:function({layoutId:t}){let e=(0,rt.useContext)(ri.L).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:d}=h,c=function(t){let{initial:e,animate:i}=function(t,e){if(sC(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ie(e)?e:void 0,animate:ie(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,rt.useContext)(sV));return(0,rt.useMemo)(()=>({initial:e,animate:i}),[sR(e),sR(i)])}(t),p=r(t,d);if(!d&&sj.B){a=0,l=0,(0,rt.useContext)(sM).strict;let t=function(t){let{drag:e,layout:i}=sF;if(!e&&!i)return{};let r={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(h);u=t.MeasureLayout,c.visualElement=function(t,e,i,r,s){let{visualElement:n}=(0,rt.useContext)(sV),o=(0,rt.useContext)(sM),a=(0,rt.useContext)(sO.t),l=(0,rt.useContext)(sE.Q).reducedMotion,u=(0,rt.useRef)(null);r=r||o.renderer,!u.current&&r&&(u.current=r(t,{visualState:e,parent:n,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let h=u.current,d=(0,rt.useContext)(rr);h&&!h.projection&&s&&("html"===h.type||"svg"===h.type)&&function(t,e,i,r){let{layoutId:s,layout:n,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:s,layout:n,alwaysMeasureLayout:!!o||a&&iX(a),visualElement:t,animationType:"string"==typeof n?n:"both",initialPromotionConfig:r,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,s,d);let c=(0,rt.useRef)(!1);(0,rt.useInsertionEffect)(()=>{h&&c.current&&h.update(i,a)});let p=i[L],m=(0,rt.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,sI.E)(()=>{h&&(c.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),i7.render(h.render),m.current&&h.animationState&&h.animationState.animateChanges())}),h}(s,p,h,e,t.ProjectionNode)}return(0,i8.jsxs)(sV.Provider,{value:c,children:[u&&c.visualElement?(0,i8.jsx)(u,{visualElement:c.visualElement,...h}):null,i(s,t,(o=c.visualElement,(0,rt.useCallback)(t=>{t&&p.onMount&&p.onMount(t),o&&(t?o.mount(t):o.unmount()),n&&("function"==typeof n?n(t):iX(n)&&(n.current=t))},[o])),p,d,c.visualElement)]})}t&&function(t){for(let e in t)sF[e]={...sF[e],...t[e]}}(t),n.displayName=`motion.${"string"==typeof s?s:`create(${s.displayName??s.name??""})`}`;let o=(0,rt.forwardRef)(n);return o[sB]=s,o}({...s3(t)?s7:s6,preloadedFeatures:eK,useRender:function(t=!1){return(e,i,r,{latestValues:s},n)=>{let o=(s3(e)?function(t,e,i,r){let s=(0,rt.useMemo)(()=>{let i=s_();return sZ(i,e,sQ(r),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};sG(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return sG(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,rt.useMemo)(()=>{let i=sH();return sX(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(i,s,n,e),a=function(t,e,i){let r={};for(let s in t)("values"!==s||"object"!=typeof t.values)&&(s1(s)||!0===i&&s0(s)||!e&&!s0(s)||t.draggable&&s.startsWith("onDrag"))&&(r[s]=t[s]);return r}(i,"string"==typeof e,t),l=e!==rt.Fragment?{...a,...o,ref:r}:{},{children:u}=i,h=(0,rt.useMemo)(()=>D(u)?u.get():u,[u]);return(0,rt.createElement)(e,{...l,children:h})}}(e),createVisualElement:eq,Component:t})}))},6044:(t,e,i)=>{i.d(e,{xQ:()=>n});var r=i(3210),s=i(1279);function n(t=!0){let e=(0,r.useContext)(s.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,r.useId)(),u=(0,r.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},7044:(t,e,i)=>{i.d(e,{B:()=>r});let r=!1},8171:(t,e,i)=>{i.d(e,{s:()=>s});var r=i(4479);function s(t){return(0,r.G)(t)&&"offsetHeight"in t}},9384:(t,e,i)=>{i.d(e,{$:()=>r});function r(){for(var t,e,i=0,r="",s=arguments.length;i<s;i++)(t=arguments[i])&&(e=function t(e){var i,r,s="";if("string"==typeof e||"number"==typeof e)s+=e;else if("object"==typeof e)if(Array.isArray(e)){var n=e.length;for(i=0;i<n;i++)e[i]&&(r=t(e[i]))&&(s&&(s+=" "),s+=r)}else for(r in e)e[r]&&(s&&(s+=" "),s+=r);return s}(t))&&(r&&(r+=" "),r+=e);return r}}};