"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[38],{637:(e,t,r)=>{r.d(t,{A:()=>s});var a=r(5155);r(2115);let s=e=>{let{className:t=""}=e;return(0,a.jsx)("div",{className:"inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ".concat(t),children:(0,a.jsx)("span",{className:"text-white font-bold text-xl",children:"M"})})}},740:(e,t,r)=>{r.d(t,{default:()=>b});var a=r(5155),s=r(2115),i=r(4416),o=r(4783),n=r(637),l=r(4843),c=r(1366);let d=()=>(0,a.jsxs)("button",{onClick:()=>{window.$crisp?window.$crisp.push(["do","chat:open"]):window.location.href="mailto:<EMAIL>?subject=Chat%20Request",window.gtag&&window.gtag("event","chat_opened",{event_category:"engagement",event_label:"header_chat"})},className:"inline-flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200","aria-label":"Open chat",children:[(0,a.jsx)(c.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Chat"})]});var u=r(7847),m=r(7067),g=r(6853);let b=()=>{let[e,t]=(0,s.useState)(!1),{trackNavigation:r}=(0,g.st)(),c=e=>{let a=document.getElementById(e);a&&(a.scrollIntoView({behavior:"smooth"}),r(e,"header_nav")),t(!1)},b=m.$U.main;return(0,a.jsxs)("header",{className:"fixed top-0 left-0 right-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 z-50 transition-colors duration-300",children:[(0,a.jsx)("div",{className:"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 w-full",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n.A,{}),(0,a.jsx)("span",{className:"ml-2 text-xl font-bold text-dark-charcoal dark:text-white",children:m.jx.name})]}),(0,a.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[b.map(e=>(0,a.jsx)("button",{onClick:()=>c(e.id),className:"text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200",children:e.label},e.label)),(0,a.jsx)(l.default,{children:(0,a.jsx)(d,{})}),(0,a.jsx)(l.default,{children:(0,a.jsx)(u.default,{size:"sm",className:"mr-4"})}),(0,a.jsx)("button",{onClick:()=>c("contact"),className:"bg-electric-blue text-white px-6 py-2 rounded-lg hover:opacity-90 transition-all duration-200",children:"Get a Quote"})]}),(0,a.jsx)("button",{onClick:()=>t(!e),className:"md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100",children:e?(0,a.jsx)(i.A,{size:24}):(0,a.jsx)(o.A,{size:24})})]})}),(0,a.jsx)(l.default,{children:e&&(0,a.jsx)("div",{className:"md:hidden fixed inset-0 top-16 bg-white z-40",children:(0,a.jsxs)("div",{className:"px-4 py-6 space-y-4",children:[b.map(e=>(0,a.jsx)("button",{onClick:()=>c(e.href.substring(1)),className:"block w-full text-left text-lg text-gray-600 hover:text-gray-900 py-2",children:e.label},e.label)),(0,a.jsx)("button",{onClick:()=>c("contact"),className:"block w-full bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors duration-200 mt-6",children:"Get a Quote"})]})})})]})}},2323:(e,t,r)=>{r.d(t,{$n:()=>c,Zp:()=>b,Wu:()=>g,pd:()=>u});var a=r(5155),s=r(2115),i=r(2596),o=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,i.$)(t))}let l=s.forwardRef((e,t)=>{let{className:r,variant:s="primary",size:i="md",isLoading:o=!1,disabled:l,children:c,...d}=e,u=n("inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-electric-blue text-white hover:opacity-90 focus:ring-electric-blue shadow-lg hover:shadow-xl",secondary:"border border-electric-blue text-electric-blue hover:bg-electric-blue hover:text-white focus:ring-electric-blue",ghost:"text-electric-blue hover:bg-electric-blue hover:bg-opacity-10 focus:ring-electric-blue"}[s],{sm:"px-4 py-2 text-sm",md:"px-6 py-3 text-base",lg:"px-8 py-4 text-lg"}[i],r);return(0,a.jsx)("button",{ref:t,className:u,disabled:l||o,...d,children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading..."]}):c})});l.displayName="Button";let c=l,d=s.forwardRef((e,t)=>{let{className:r,variant:s="base",label:i,helperText:o,errorMessage:l,...c}=e,d=n("w-full px-4 py-3 rounded-lg text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{base:"border border-gray-300 dark:border-gray-600 focus:ring-electric-blue focus:border-electric-blue bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400",error:"border border-red-500 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400",success:"border border-green-500 focus:ring-green-500 focus:border-green-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"}[s],r),u="error"===s&&l,m="error"!==s&&o;return(0,a.jsxs)("div",{className:"w-full",children:[i&&(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:i}),(0,a.jsx)("input",{ref:t,className:d,...c}),u&&(0,a.jsx)("p",{className:"mt-2 text-sm text-red-600 dark:text-red-400",children:l}),m&&(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:o})]})});d.displayName="Input";let u=d,m=s.forwardRef((e,t)=>{let{className:r,variant:s="base",children:i,...o}=e,l=n("bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",{base:"",hover:"hover:shadow-md transition-shadow duration-200",interactive:"hover:shadow-lg cursor-pointer transition-shadow duration-200"}[s],r);return(0,a.jsx)("div",{ref:t,className:l,...o,children:i})});m.displayName="Card",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:n("p-6 pb-0",r),...s})}).displayName="CardHeader";let g=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:n("p-6",r),...s})});g.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:n("p-6 pt-0",r),...s})}).displayName="CardFooter";let b=m},4843:(e,t,r)=>{r.r(t),r.d(t,{default:()=>i});var a=r(5155),s=r(2115);let i=e=>{let{children:t,fallback:r=null}=e,[i,o]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{o(!0)},[]),i)?(0,a.jsx)(a.Fragment,{children:t}):(0,a.jsx)(a.Fragment,{children:r})}},6129:(e,t,r)=>{r.r(t),r.d(t,{default:()=>l});var a=r(5155),s=r(2115),i=r(6408),o=r(2323),n=r(9509);let l=e=>{let{variant:t="inline",className:r=""}=e,[l,c]=(0,s.useState)(""),[d,u]=(0,s.useState)(!1),[m,g]=(0,s.useState)("idle"),b=async e=>{if(e.preventDefault(),l.trim()){u(!0),g("idle");try{let e=n.env.NEXT_PUBLIC_MAILCHIMP_API_KEY,r=n.env.MAILCHIMP_LIST_ID;if(!e||!r){await new Promise(e=>setTimeout(e,1e3)),g("success"),c(""),window.gtag&&window.gtag("event","newsletter_signup",{event_category:"engagement",event_label:t});return}(await fetch("/api/newsletter",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:l,source:t})})).ok?(g("success"),c(""),window.gtag&&window.gtag("event","newsletter_signup",{event_category:"engagement",event_label:t})):g("error")}catch(e){g("error")}finally{u(!1)}}};return(0,a.jsxs)("div",{className:"".concat(r),children:[("inline"===t||"section"===t)&&(0,a.jsx)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"py-16 md:py-20 bg-electric-blue",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"Stay Updated on Mobile Innovation"}),(0,a.jsx)("p",{className:"text-lg md:text-xl leading-relaxed text-blue-100 max-w-3xl mx-auto mb-8",children:"Get insights on mobile app development, industry trends, and exclusive tips delivered to your inbox."}),(0,a.jsxs)("form",{onSubmit:b,className:"max-w-md mx-auto",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)(o.pd,{type:"email",value:l,onChange:e=>c(e.target.value),placeholder:"Enter your email address",required:!0,className:"flex-1 bg-white border-white focus:ring-white focus:border-white",disabled:d}),(0,a.jsx)(o.$n,{type:"submit",variant:"secondary",isLoading:d,disabled:!l.trim()||d,className:"bg-white text-electric-blue hover:bg-gray-50 border-white",children:"Subscribe"})]}),"success"===m&&(0,a.jsx)(i.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4 text-green-100 text-sm",children:"✓ Successfully subscribed! Check your email for confirmation."}),"error"===m&&(0,a.jsx)(i.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4 text-red-100 text-sm",children:"✗ Something went wrong. Please try again."})]})]})}),"footer"===t&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4 text-white",children:"Stay Connected"}),(0,a.jsx)("p",{className:"text-sm text-gray-400 dark:text-gray-300 mb-4 leading-relaxed",children:"Get the latest updates on mobile app development and industry insights."}),(0,a.jsxs)("form",{onSubmit:b,className:"space-y-4",children:[(0,a.jsx)(o.pd,{type:"email",value:l,onChange:e=>c(e.target.value),placeholder:"Enter your email",required:!0,className:"bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:ring-electric-blue focus:border-electric-blue",disabled:d}),(0,a.jsx)(o.$n,{type:"submit",variant:"primary",size:"sm",isLoading:d,disabled:!l.trim()||d,className:"w-full",children:"Subscribe"}),"success"===m&&(0,a.jsx)(i.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-green-400 text-sm",children:"✓ Successfully subscribed!"}),"error"===m&&(0,a.jsx)(i.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-red-400 text-sm",children:"✗ Please try again."})]})]})]})}},6853:(e,t,r)=>{r.d(t,{st:()=>o,qF:()=>d,Mw:()=>y});var a=r(2115),s=r(7067);let i={HERO_CTA_CLICK:"hero_cta_click",CONTACT_CTA_CLICK:"contact_cta_click",SERVICES_CTA_CLICK:"services_cta_click",DEMO_INTERACTION:"demo_interaction",DEMO_TAB_SWITCH:"demo_tab_switch",DEMO_ANIMATION_COMPLETE:"demo_animation_complete",DEMO_PREVIEW_CLICK:"demo_preview_click",FORM_SUBMIT:"form_submit",FORM_SUCCESS:"form_success",FORM_ERROR:"form_error",FORM_FIELD_FOCUS:"form_field_focus",BLOG_POST_CLICK:"blog_post_click",FAQ_ITEM_EXPAND:"faq_item_expand",EXTERNAL_LINK_CLICK:"external_link_click",CHAT_TRIGGER_CLICK:"chat_trigger_click",PHONE_CLICK:"phone_click",EMAIL_CLICK:"email_click",PAGE_VIEW:"page_view",SCROLL_DEPTH:"scroll_depth",TIME_ON_PAGE:"time_on_page"};function o(){let e=(0,a.useCallback)((e,t)=>{if((s.PK.enableAnalytics||s.PK.isDevelopment)&&(s.PK.isDevelopment&&s.PK.enableDebugLogs,window.gtag))try{window.gtag("event",e,{event_category:(null==t?void 0:t.category)||"engagement",event_label:null==t?void 0:t.label,value:null==t?void 0:t.value,...null==t?void 0:t.custom_parameters})}catch(e){}},[]),t=(0,a.useCallback)((t,r)=>{e(i.HERO_CTA_CLICK,{category:"navigation",label:t,custom_parameters:{source:r}})},[e]),r=(0,a.useCallback)((t,r,a)=>{e({submit:i.FORM_SUBMIT,success:i.FORM_SUCCESS,error:i.FORM_ERROR,field_focus:i.FORM_FIELD_FOCUS}[t],{category:"form_interaction",label:r,custom_parameters:{field_name:a}})},[e]),o=(0,a.useCallback)((t,r)=>{e({tab_switch:i.DEMO_TAB_SWITCH,preview_click:i.DEMO_PREVIEW_CLICK,animation_complete:i.DEMO_ANIMATION_COMPLETE}[t],{category:"demo_interaction",label:r})},[e]),n=(0,a.useCallback)((t,r,a)=>{e({blog_post:i.BLOG_POST_CLICK,faq_item:i.FAQ_ITEM_EXPAND,external_link:i.EXTERNAL_LINK_CLICK}[t],{category:"content_interaction",label:r,custom_parameters:{action:a}})},[e]),l=(0,a.useCallback)((t,r)=>{e({chat:i.CHAT_TRIGGER_CLICK,phone:i.PHONE_CLICK,email:i.EMAIL_CLICK}[t],{category:"support_interaction",label:t,custom_parameters:{source:r}})},[e]),c=(0,a.useCallback)((t,r)=>{e(i.PAGE_VIEW,{category:"page_interaction",label:t,custom_parameters:{page_title:r,page_path:t}})},[e]);return{trackEvent:e,trackNavigation:t,trackFormInteraction:r,trackDemoInteraction:o,trackContentInteraction:n,trackSupportInteraction:l,trackPageView:c,EVENTS:i}}let n={data:{name:"",email:"",company:"",projectType:"",message:""},errors:{},isSubmitting:!1,isSubmitted:!1,submitSuccess:!1},l=(e,t)=>{switch(e){case"name":if(!t.trim())return"Name is required";if(t.trim().length<2)return"Name must be at least 2 characters";return;case"email":if(!t.trim())return"Email is required";if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t))return"Please enter a valid email address";return;case"company":if(t.trim()&&t.trim().length<2)return"Company name must be at least 2 characters";return;case"projectType":if(!t.trim())return"Please select a project type";return;case"message":if(!t.trim())return"Message is required";if(t.trim().length<10)return"Message must be at least 10 characters";if(t.trim().length>1e3)return"Message must be less than 1000 characters";return;default:return}},c=e=>{let t={};return Object.keys(e).forEach(r=>{let a=l(r,e[r]);a&&(t[r]=a)}),t};function d(){let[e,t]=(0,a.useState)(n),{trackFormInteraction:r}=o(),i=(0,a.useCallback)((e,r)=>{t(t=>({...t,data:{...t.data,[e]:r},errors:{...t.errors,[e]:void 0,general:void 0}}))},[]),d=(0,a.useCallback)(e=>{r("field_focus","contact_form",e)},[r]),u=(0,a.useCallback)(r=>{let a=l(r,e.data[r]);return t(e=>({...e,errors:{...e.errors,[r]:a}})),!a},[e.data]),m=(0,a.useCallback)(()=>{t(n)},[]),g=(0,a.useCallback)(async()=>{let a=c(e.data);if(Object.keys(a).length>0)return t(e=>({...e,errors:a})),!1;if(!s.f_.forms.web3forms)return t(e=>({...e,errors:{general:"Form service is not configured. Please contact us directly."}})),r("error","contact_form"),!1;t(e=>({...e,isSubmitting:!0,errors:{}})),r("submit","contact_form");try{let a=new FormData;a.append("access_key",s.f_.forms.web3forms),a.append("name",e.data.name),a.append("email",e.data.email),a.append("company",e.data.company),a.append("project_type",e.data.projectType),a.append("message",e.data.message),a.append("from_name","Mobilify Contact Form"),a.append("subject","New Contact Form Submission from ".concat(e.data.name));let i=await fetch("https://api.web3forms.com/submit",{method:"POST",body:a}),o=await i.json();if(o.success)return t(e=>({...e,isSubmitting:!1,isSubmitted:!0,submitSuccess:!0})),r("success","contact_form"),!0;throw Error(o.message||"Form submission failed")}catch(e){return t(e=>({...e,isSubmitting:!1,isSubmitted:!0,submitSuccess:!1,errors:{general:"There was an error sending your message. Please try again or contact us directly."}})),r("error","contact_form"),!1}},[e.data,r]);return{formData:e.data,errors:e.errors,isSubmitting:e.isSubmitting,isSubmitted:e.isSubmitted,submitSuccess:e.submitSuccess,updateField:i,handleFieldFocus:d,validateSingleField:u,submitForm:g,resetForm:m,isValid:0===Object.keys(e.errors).length,hasErrors:Object.keys(e.errors).length>0}}var u=r(3406),m=r(4612),g=r.n(m),b=r(9509);let h=!!(b.env.NEXT_PUBLIC_SANITY_PROJECT_ID&&b.env.NEXT_PUBLIC_SANITY_DATASET),p=h?(0,u.UU)({projectId:b.env.NEXT_PUBLIC_SANITY_PROJECT_ID,dataset:b.env.NEXT_PUBLIC_SANITY_DATASET||"production",apiVersion:"2024-01-01",useCdn:!0,token:b.env.SANITY_API_TOKEN}):null;async function x(){if(!p)return null;try{return await p.fetch('*[_type == "siteSettings"][0] {\n  _id,\n  heroHeadline,\n  heroSubtext,\n  heroButtonText,\n  contactHeadline,\n  contactSubtext,\n  contactButtonText,\n  formSuccessMessage,\n  formErrorMessage,\n  servicesHeadline,\n  servicesSubtext,\n  processHeadline,\n  processSubtext,\n  aboutHeadline,\n  aboutSubtext,\n  footerTagline,\n  footerCopyright\n}')}catch(e){return null}}h&&p&&g()(p);let f=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=new Date().getFullYear();return{heroHeadline:(null==e?void 0:e.heroHeadline)||s.zR.hero.headline,heroSubtext:(null==e?void 0:e.heroSubtext)||s.zR.hero.subtext,heroButtonText:(null==e?void 0:e.heroButtonText)||s.zR.hero.buttonText,contactHeadline:(null==e?void 0:e.contactHeadline)||s.zR.contact.headline,contactSubtext:(null==e?void 0:e.contactSubtext)||s.zR.contact.subtext,contactButtonText:(null==e?void 0:e.contactButtonText)||s.zR.contact.buttonText,formSuccessMessage:(null==e?void 0:e.formSuccessMessage)||s.zR.contact.successMessage,formErrorMessage:(null==e?void 0:e.formErrorMessage)||s.zR.contact.errorMessage,servicesHeadline:(null==e?void 0:e.servicesHeadline)||s.zR.services.headline,servicesSubtext:(null==e?void 0:e.servicesSubtext)||s.zR.services.subtext,processHeadline:(null==e?void 0:e.processHeadline)||s.zR.process.headline,processSubtext:(null==e?void 0:e.processSubtext)||s.zR.process.subtext,aboutHeadline:(null==e?void 0:e.aboutHeadline)||s.zR.about.headline,aboutSubtext:(null==e?void 0:e.aboutSubtext)||s.zR.about.subtext,footerTagline:(null==e?void 0:e.footerTagline)||"Building the future of mobile apps",footerCopyright:(null==e?void 0:e.footerCopyright)||"\xa9 ".concat(a," Mobilify. All rights reserved."),isLoading:t,isFromCMS:!!e,error:r}};function y(e){let t=function(){let[e,t]=(0,a.useState)(null),[r,s]=(0,a.useState)(!0),[i,o]=(0,a.useState)(null);return(0,a.useEffect)(()=>{let e=!0;return(async()=>{try{s(!0),o(null);let r=await x();e&&(t(r),s(!1))}catch(t){e&&(o(t instanceof Error?t.message:"Failed to fetch site settings"),s(!1))}})(),()=>{e=!1}},[]),f(e,r,i)}();switch(e){case"hero":return{headline:t.heroHeadline,subtext:t.heroSubtext,buttonText:t.heroButtonText,isLoading:t.isLoading,isFromCMS:t.isFromCMS};case"contact":return{headline:t.contactHeadline,subtext:t.contactSubtext,buttonText:t.contactButtonText,successMessage:t.formSuccessMessage,errorMessage:t.formErrorMessage,isLoading:t.isLoading,isFromCMS:t.isFromCMS};case"services":return{headline:t.servicesHeadline,subtext:t.servicesSubtext,isLoading:t.isLoading,isFromCMS:t.isFromCMS};case"process":return{headline:t.processHeadline,subtext:t.processSubtext,isLoading:t.isLoading,isFromCMS:t.isFromCMS};case"about":return{headline:t.aboutHeadline,subtext:t.aboutSubtext,isLoading:t.isLoading,isFromCMS:t.isFromCMS};default:return{isLoading:t.isLoading,isFromCMS:t.isFromCMS}}}},7067:(e,t,r)=>{r.d(t,{$U:()=>i,Bg:()=>n,PK:()=>d,UZ:()=>c,f_:()=>l,jx:()=>s,zR:()=>o});var a=r(9509);let s={name:"Mobilify",tagline:"Turn Your Website or Idea Into a Custom Mobile App",description:"Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. Get a beautiful, custom-designed app without the complexity.",url:a.env.NEXT_PUBLIC_SITE_URL||"https://mobilify.app",author:"Mobilify Team"},i={main:[{label:"Services",href:"#services-overview",id:"services-overview"},{label:"How It Works",href:"#process",id:"process"},{label:"About Us",href:"#about",id:"about"}],footer:{company:[{label:"About",href:"/about"},{label:"Services",href:"/services"},{label:"Blog",href:"/blog"},{label:"FAQ",href:"/faq"}],legal:[{label:"Privacy Policy",href:"/privacy"},{label:"Terms of Service",href:"/terms"},{label:"Cookie Policy",href:"/cookies"}],support:[{label:"Contact Us",href:"#contact"},{label:"Help Center",href:"/help"},{label:"Documentation",href:"/docs"}]}},o={hero:{headline:"Your Idea. Your App. Realized.",subtext:"Mobilify transforms your concepts and existing websites into stunning, high-performance mobile apps. We are the bridge from vision to launch.",buttonText:"See How It Works"},contact:{headline:"Ready to Build Your Mobile Future?",subtext:"Let's discuss your project. We're happy to provide a free, no-obligation consultation and quote.",buttonText:"Send Message",successMessage:"Thank you! Your message has been sent successfully. We'll get back to you within 24 hours.",errorMessage:"Sorry, there was an error sending your message. Please try again or contact us directly."},services:{headline:"Our Services",subtext:"Choose the perfect solution for your mobile app needs"},process:{headline:"How It Works",subtext:"Our proven process to turn your idea into a successful mobile app"},about:{headline:"About Mobilify",subtext:"We are passionate about helping businesses and entrepreneurs bring their ideas to life through mobile technology."}},n={starter:{name:"Starter App",description:"Perfect for converting existing websites into mobile apps.",price:"Starting at $5,000",features:["Website Conversion","iOS & Android","Basic Features"],popular:!1},custom:{name:"Custom App",description:"Turn your new ideas into reality with custom development.",price:"Starting at $15,000",features:["Idea to App","Custom UI/UX","Advanced Features"],popular:!0},enterprise:{name:"Enterprise Solution",description:"Comprehensive solutions for large-scale applications.",price:"Custom Pricing",features:["Full Development","Scalable Architecture","Ongoing Support"],popular:!1}},l={analytics:{googleAnalytics:""},forms:{web3forms:""},chat:{crisp:a.env.NEXT_PUBLIC_CRISP_WEBSITE_ID,tawkTo:a.env.NEXT_PUBLIC_TAWK_TO_PROPERTY_ID},newsletter:{mailchimp:a.env.NEXT_PUBLIC_MAILCHIMP_API_KEY,convertkit:a.env.CONVERTKIT_API_KEY}};s.name,s.tagline,s.name,s.description,s.url,s.name,s.name,s.tagline;let c={duration:{fast:.2,normal:.4,slow:.6,verySlow:1},easing:{easeInOut:[.4,0,.2,1],easeOut:[0,0,.2,1],easeIn:[.4,0,1,1]}},d={isDevelopment:!1,isProduction:!0,enableDebugLogs:!1,enableAnalytics:!0}},7740:(e,t,r)=>{r.d(t,{DP:()=>o,ThemeProvider:()=>n});var a=r(5155),s=r(2115);let i=(0,s.createContext)(void 0),o=()=>{let e=(0,s.useContext)(i);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e},n=e=>{let{children:t}=e,[r,o]=(0,s.useState)("light"),[n,l]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{let e=localStorage.getItem("mobilify-theme"),t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";o(e||t),l(!0)},[]),(0,s.useEffect)(()=>{if(!n)return;let e=document.documentElement;"dark"===r?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("mobilify-theme",r),window.gtag&&window.gtag("event","theme_changed",{event_category:"user_preference",event_label:r})},[r,n]),(0,s.useEffect)(()=>{if(!n)return;let e=window.matchMedia("(prefers-color-scheme: dark)"),t=e=>{localStorage.getItem("mobilify-theme")||o(e.matches?"dark":"light")};return e.addEventListener("change",t),()=>e.removeEventListener("change",t)},[n]),(0,a.jsx)(i.Provider,{value:{theme:r,toggleTheme:()=>{o(e=>"light"===e?"dark":"light")},setTheme:e=>{o(e)}},children:(0,a.jsx)("div",{suppressHydrationWarning:!0,children:t})})}},7847:(e,t,r)=>{r.d(t,{default:()=>n});var a=r(5155);r(2115);var s=r(2098),i=r(3509),o=r(7740);let n=e=>{let{className:t="",size:r="md"}=e,{theme:n,toggleTheme:l}=(0,o.DP)();return(0,a.jsx)("button",{onClick:l,className:"".concat((()=>{switch(r){case"sm":return"w-8 h-8 text-sm";case"lg":return"w-12 h-12 text-lg";default:return"w-10 h-10 text-base"}})()," flex items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 ").concat(t),"aria-label":"Switch to ".concat("dark"===n?"light":"dark"," mode"),children:"dark"===n?(0,a.jsx)(s.A,{className:"w-5 h-5"}):(0,a.jsx)(i.A,{className:"w-5 h-5"})})}}}]);