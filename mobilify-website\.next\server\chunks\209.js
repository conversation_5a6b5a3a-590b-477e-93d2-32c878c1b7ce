"use strict";exports.id=209,exports.ids=[209],exports.modules={261:(e,t,r)=>{r.d(t,{default:()=>m});var a=r(687),s=r(3210),l=r(1860),i=r(2941),o=r(9733),d=r(7009),c=r(3872);let n=()=>(0,a.jsxs)("button",{onClick:()=>{window.location.href="mailto:<EMAIL>?subject=Chat%20Request"},className:"inline-flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200","aria-label":"Open chat",children:[(0,a.jsx)(c.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Chat"})]});var x=r(3143);let m=()=>{let[e,t]=(0,s.useState)(!1),r=e=>{let r=document.getElementById(e);r&&r.scrollIntoView({behavior:"smooth"}),t(!1)},c=[{label:"Services",href:"#services-overview"},{label:"How It Works",href:"#process"},{label:"About Us",href:"#about"}];return(0,a.jsxs)("header",{className:"fixed top-0 left-0 right-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 z-50 transition-colors duration-300",children:[(0,a.jsx)("div",{className:"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 w-full",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{}),(0,a.jsx)("span",{className:"ml-2 text-xl font-bold text-dark-charcoal dark:text-white",children:"Mobilify"})]}),(0,a.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[c.map(e=>(0,a.jsx)("button",{onClick:()=>r(e.href.substring(1)),className:"text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200",children:e.label},e.label)),(0,a.jsx)(d.default,{children:(0,a.jsx)(n,{})}),(0,a.jsx)(d.default,{children:(0,a.jsx)(x.default,{size:"sm",className:"mr-4"})}),(0,a.jsx)("button",{onClick:()=>r("contact"),className:"bg-electric-blue text-white px-6 py-2 rounded-lg hover:opacity-90 transition-all duration-200",children:"Get a Quote"})]}),(0,a.jsx)("button",{onClick:()=>t(!e),className:"md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100",children:e?(0,a.jsx)(l.A,{size:24}):(0,a.jsx)(i.A,{size:24})})]})}),(0,a.jsx)(d.default,{children:e&&(0,a.jsx)("div",{className:"md:hidden fixed inset-0 top-16 bg-white z-40",children:(0,a.jsxs)("div",{className:"px-4 py-6 space-y-4",children:[c.map(e=>(0,a.jsx)("button",{onClick:()=>r(e.href.substring(1)),className:"block w-full text-left text-lg text-gray-600 hover:text-gray-900 py-2",children:e.label},e.label)),(0,a.jsx)("button",{onClick:()=>r("contact"),className:"block w-full bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors duration-200 mt-6",children:"Get a Quote"})]})})})]})}},1477:(e,t,r)=>{r.r(t),r.d(t,{default:()=>o});var a=r(687),s=r(3210),l=r(6001),i=r(7975);let o=({variant:e="inline",className:t=""})=>{let[r,o]=(0,s.useState)(""),[d,c]=(0,s.useState)(!1),[n,x]=(0,s.useState)("idle"),m=async t=>{if(t.preventDefault(),r.trim()){c(!0),x("idle");try{let t=process.env.NEXT_PUBLIC_MAILCHIMP_API_KEY,a=process.env.MAILCHIMP_LIST_ID;if(!t||!a){await new Promise(e=>setTimeout(e,1e3)),x("success"),o("");return}(await fetch("/api/newsletter",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r,source:e})})).ok?(x("success"),o("")):x("error")}catch(e){x("error")}finally{c(!1)}}};return(0,a.jsxs)("div",{className:`${t}`,children:[("inline"===e||"section"===e)&&(0,a.jsx)(l.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"py-16 md:py-20 bg-electric-blue",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"Stay Updated on Mobile Innovation"}),(0,a.jsx)("p",{className:"text-lg md:text-xl leading-relaxed text-blue-100 max-w-3xl mx-auto mb-8",children:"Get insights on mobile app development, industry trends, and exclusive tips delivered to your inbox."}),(0,a.jsxs)("form",{onSubmit:m,className:"max-w-md mx-auto",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)(i.pd,{type:"email",value:r,onChange:e=>o(e.target.value),placeholder:"Enter your email address",required:!0,className:"flex-1 bg-white border-white focus:ring-white focus:border-white",disabled:d}),(0,a.jsx)(i.$n,{type:"submit",variant:"secondary",isLoading:d,disabled:!r.trim()||d,className:"bg-white text-electric-blue hover:bg-gray-50 border-white",children:"Subscribe"})]}),"success"===n&&(0,a.jsx)(l.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4 text-green-100 text-sm",children:"✓ Successfully subscribed! Check your email for confirmation."}),"error"===n&&(0,a.jsx)(l.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4 text-red-100 text-sm",children:"✗ Something went wrong. Please try again."})]})]})}),"footer"===e&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4 text-white",children:"Stay Connected"}),(0,a.jsx)("p",{className:"text-sm text-gray-400 dark:text-gray-300 mb-4 leading-relaxed",children:"Get the latest updates on mobile app development and industry insights."}),(0,a.jsxs)("form",{onSubmit:m,className:"space-y-4",children:[(0,a.jsx)(i.pd,{type:"email",value:r,onChange:e=>o(e.target.value),placeholder:"Enter your email",required:!0,className:"bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:ring-electric-blue focus:border-electric-blue",disabled:d}),(0,a.jsx)(i.$n,{type:"submit",variant:"primary",size:"sm",isLoading:d,disabled:!r.trim()||d,className:"w-full",children:"Subscribe"}),"success"===n&&(0,a.jsx)(l.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-green-400 text-sm",children:"✓ Successfully subscribed!"}),"error"===n&&(0,a.jsx)(l.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-red-400 text-sm",children:"✗ Please try again."})]})]})]})}},3143:(e,t,r)=>{r.d(t,{default:()=>o});var a=r(687);r(3210);var s=r(1134),l=r(363),i=r(1188);let o=({className:e="",size:t="md"})=>{let{theme:r,toggleTheme:o}=(0,i.DP)();return(0,a.jsx)("button",{onClick:o,className:`${(()=>{switch(t){case"sm":return"w-8 h-8 text-sm";case"lg":return"w-12 h-12 text-lg";default:return"w-10 h-10 text-base"}})()} flex items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 ${e}`,"aria-label":`Switch to ${"dark"===r?"light":"dark"} mode`,children:"dark"===r?(0,a.jsx)(s.A,{className:"w-5 h-5"}):(0,a.jsx)(l.A,{className:"w-5 h-5"})})}},7975:(e,t,r)=>{r.d(t,{$n:()=>n,Zp:()=>b,Wu:()=>h,pd:()=>m});var a=r(687),s=r(3210),l=r.n(s),i=r(9384),o=r(2348);function d(...e){return(0,o.QP)((0,i.$)(e))}let c=l().forwardRef(({className:e,variant:t="primary",size:r="md",isLoading:s=!1,disabled:l,children:i,...o},c)=>{let n=d("inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-electric-blue text-white hover:opacity-90 focus:ring-electric-blue shadow-lg hover:shadow-xl",secondary:"border border-electric-blue text-electric-blue hover:bg-electric-blue hover:text-white focus:ring-electric-blue",ghost:"text-electric-blue hover:bg-electric-blue hover:bg-opacity-10 focus:ring-electric-blue"}[t],{sm:"px-4 py-2 text-sm",md:"px-6 py-3 text-base",lg:"px-8 py-4 text-lg"}[r],e);return(0,a.jsx)("button",{ref:c,className:n,disabled:l||s,...o,children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading..."]}):i})});c.displayName="Button";let n=c,x=l().forwardRef(({className:e,variant:t="base",label:r,helperText:s,errorMessage:l,...i},o)=>{let c=d("w-full px-4 py-3 rounded-lg text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{base:"border border-gray-300 dark:border-gray-600 focus:ring-electric-blue focus:border-electric-blue bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400",error:"border border-red-500 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400",success:"border border-green-500 focus:ring-green-500 focus:border-green-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"}[t],e),n="error"===t&&l,x="error"!==t&&s;return(0,a.jsxs)("div",{className:"w-full",children:[r&&(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r}),(0,a.jsx)("input",{ref:o,className:c,...i}),n&&(0,a.jsx)("p",{className:"mt-2 text-sm text-red-600 dark:text-red-400",children:l}),x&&(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:s})]})});x.displayName="Input";let m=x,u=l().forwardRef(({className:e,variant:t="base",children:r,...s},l)=>{let i=d("bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",{base:"",hover:"hover:shadow-md transition-shadow duration-200",interactive:"hover:shadow-lg cursor-pointer transition-shadow duration-200"}[t],e);return(0,a.jsx)("div",{ref:l,className:i,...s,children:r})});u.displayName="Card",l().forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:d("p-6 pb-0",e),...t})).displayName="CardHeader";let h=l().forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:d("p-6",e),...t}));h.displayName="CardContent",l().forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:d("p-6 pt-0",e),...t})).displayName="CardFooter";let b=u},9733:(e,t,r)=>{r.d(t,{A:()=>s});var a=r(687);r(3210);let s=({className:e=""})=>(0,a.jsx)("div",{className:`inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ${e}`,children:(0,a.jsx)("span",{className:"text-white font-bold text-xl",children:"M"})})}};