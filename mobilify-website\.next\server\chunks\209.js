"use strict";exports.id=209,exports.ids=[209],exports.modules={1745:(e,t,r)=>{r.d(t,{$U:()=>s,Bg:()=>o,PK:()=>c,UZ:()=>n,f_:()=>l,jx:()=>a,zR:()=>i});let a={name:"Mobilify",tagline:"Turn Your Website or Idea Into a Custom Mobile App",description:"Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. Get a beautiful, custom-designed app without the complexity.",url:process.env.NEXT_PUBLIC_SITE_URL||"https://mobilify.app",author:"Mobilify Team"},s={main:[{label:"Services",href:"#services-overview",id:"services-overview"},{label:"How It Works",href:"#process",id:"process"},{label:"About Us",href:"#about",id:"about"}],footer:{company:[{label:"About",href:"/about"},{label:"Services",href:"/services"},{label:"Blog",href:"/blog"},{label:"FAQ",href:"/faq"}],legal:[{label:"Privacy Policy",href:"/privacy"},{label:"Terms of Service",href:"/terms"},{label:"Cookie Policy",href:"/cookies"}],support:[{label:"Contact Us",href:"#contact"},{label:"Help Center",href:"/help"},{label:"Documentation",href:"/docs"}]}},i={hero:{headline:"Your Idea. Your App. Realized.",subtext:"Mobilify transforms your concepts and existing websites into stunning, high-performance mobile apps. We are the bridge from vision to launch.",buttonText:"See How It Works"},contact:{headline:"Ready to Build Your Mobile Future?",subtext:"Let's discuss your project. We're happy to provide a free, no-obligation consultation and quote.",buttonText:"Send Message",successMessage:"Thank you! Your message has been sent successfully. We'll get back to you within 24 hours.",errorMessage:"Sorry, there was an error sending your message. Please try again or contact us directly."},services:{headline:"Our Services",subtext:"Choose the perfect solution for your mobile app needs"},process:{headline:"How It Works",subtext:"Our proven process to turn your idea into a successful mobile app"},about:{headline:"About Mobilify",subtext:"We are passionate about helping businesses and entrepreneurs bring their ideas to life through mobile technology."}},o={starter:{name:"Starter App",description:"Perfect for converting existing websites into mobile apps.",price:"Starting at $5,000",features:["Website Conversion","iOS & Android","Basic Features"],popular:!1},custom:{name:"Custom App",description:"Turn your new ideas into reality with custom development.",price:"Starting at $15,000",features:["Idea to App","Custom UI/UX","Advanced Features"],popular:!0},enterprise:{name:"Enterprise Solution",description:"Comprehensive solutions for large-scale applications.",price:"Custom Pricing",features:["Full Development","Scalable Architecture","Ongoing Support"],popular:!1}},l={analytics:{googleAnalytics:""},forms:{web3forms:""},chat:{crisp:process.env.NEXT_PUBLIC_CRISP_WEBSITE_ID,tawkTo:process.env.NEXT_PUBLIC_TAWK_TO_PROPERTY_ID},newsletter:{mailchimp:process.env.NEXT_PUBLIC_MAILCHIMP_API_KEY,convertkit:process.env.CONVERTKIT_API_KEY}};a.name,a.tagline,a.name,a.description,a.url,a.name,a.name,a.tagline;let n={duration:{fast:.2,normal:.4,slow:.6,verySlow:1},easing:{easeInOut:[.4,0,.2,1],easeOut:[0,0,.2,1],easeIn:[.4,0,1,1]}},c={isDevelopment:!1,isProduction:!0,enableDebugLogs:!1,enableAnalytics:!0}},7378:(e,t,r)=>{r.d(t,{st:()=>o,qF:()=>d,Mw:()=>x});var a=r(43210),s=r(1745);let i={HERO_CTA_CLICK:"hero_cta_click",CONTACT_CTA_CLICK:"contact_cta_click",SERVICES_CTA_CLICK:"services_cta_click",DEMO_INTERACTION:"demo_interaction",DEMO_TAB_SWITCH:"demo_tab_switch",DEMO_ANIMATION_COMPLETE:"demo_animation_complete",DEMO_PREVIEW_CLICK:"demo_preview_click",FORM_SUBMIT:"form_submit",FORM_SUCCESS:"form_success",FORM_ERROR:"form_error",FORM_FIELD_FOCUS:"form_field_focus",BLOG_POST_CLICK:"blog_post_click",FAQ_ITEM_EXPAND:"faq_item_expand",EXTERNAL_LINK_CLICK:"external_link_click",CHAT_TRIGGER_CLICK:"chat_trigger_click",PHONE_CLICK:"phone_click",EMAIL_CLICK:"email_click",PAGE_VIEW:"page_view",SCROLL_DEPTH:"scroll_depth",TIME_ON_PAGE:"time_on_page"};function o(){let e=(0,a.useCallback)((e,t)=>{(s.PK.enableAnalytics||s.PK.isDevelopment)&&s.PK.isDevelopment&&s.PK.enableDebugLogs},[]),t=(0,a.useCallback)((t,r)=>{e(i.HERO_CTA_CLICK,{category:"navigation",label:t,custom_parameters:{source:r}})},[e]),r=(0,a.useCallback)((t,r,a)=>{e({submit:i.FORM_SUBMIT,success:i.FORM_SUCCESS,error:i.FORM_ERROR,field_focus:i.FORM_FIELD_FOCUS}[t],{category:"form_interaction",label:r,custom_parameters:{field_name:a}})},[e]),o=(0,a.useCallback)((t,r)=>{e({tab_switch:i.DEMO_TAB_SWITCH,preview_click:i.DEMO_PREVIEW_CLICK,animation_complete:i.DEMO_ANIMATION_COMPLETE}[t],{category:"demo_interaction",label:r})},[e]),l=(0,a.useCallback)((t,r,a)=>{e({blog_post:i.BLOG_POST_CLICK,faq_item:i.FAQ_ITEM_EXPAND,external_link:i.EXTERNAL_LINK_CLICK}[t],{category:"content_interaction",label:r,custom_parameters:{action:a}})},[e]),n=(0,a.useCallback)((t,r)=>{e({chat:i.CHAT_TRIGGER_CLICK,phone:i.PHONE_CLICK,email:i.EMAIL_CLICK}[t],{category:"support_interaction",label:t,custom_parameters:{source:r}})},[e]),c=(0,a.useCallback)((t,r)=>{e(i.PAGE_VIEW,{category:"page_interaction",label:t,custom_parameters:{page_title:r,page_path:t}})},[e]);return{trackEvent:e,trackNavigation:t,trackFormInteraction:r,trackDemoInteraction:o,trackContentInteraction:l,trackSupportInteraction:n,trackPageView:c,EVENTS:i}}let l={data:{name:"",email:"",company:"",projectType:"",message:""},errors:{},isSubmitting:!1,isSubmitted:!1,submitSuccess:!1},n=(e,t)=>{switch(e){case"name":if(!t.trim())return"Name is required";if(t.trim().length<2)return"Name must be at least 2 characters";return;case"email":if(!t.trim())return"Email is required";if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t))return"Please enter a valid email address";return;case"company":if(t.trim()&&t.trim().length<2)return"Company name must be at least 2 characters";return;case"projectType":if(!t.trim())return"Please select a project type";return;case"message":if(!t.trim())return"Message is required";if(t.trim().length<10)return"Message must be at least 10 characters";if(t.trim().length>1e3)return"Message must be less than 1000 characters";return;default:return}},c=e=>{let t={};return Object.keys(e).forEach(r=>{let a=n(r,e[r]);a&&(t[r]=a)}),t};function d(){let[e,t]=(0,a.useState)(l),{trackFormInteraction:r}=o(),i=(0,a.useCallback)((e,r)=>{t(t=>({...t,data:{...t.data,[e]:r},errors:{...t.errors,[e]:void 0,general:void 0}}))},[]),d=(0,a.useCallback)(e=>{r("field_focus","contact_form",e)},[r]),u=(0,a.useCallback)(r=>{let a=n(r,e.data[r]);return t(e=>({...e,errors:{...e.errors,[r]:a}})),!a},[e.data]),m=(0,a.useCallback)(()=>{t(l)},[]),b=(0,a.useCallback)(async()=>{let a=c(e.data);if(Object.keys(a).length>0)return t(e=>({...e,errors:a})),!1;if(!s.f_.forms.web3forms)return t(e=>({...e,errors:{general:"Form service is not configured. Please contact us directly."}})),r("error","contact_form"),!1;t(e=>({...e,isSubmitting:!0,errors:{}})),r("submit","contact_form");try{let a=new FormData;a.append("access_key",s.f_.forms.web3forms),a.append("name",e.data.name),a.append("email",e.data.email),a.append("company",e.data.company),a.append("project_type",e.data.projectType),a.append("message",e.data.message),a.append("from_name","Mobilify Contact Form"),a.append("subject",`New Contact Form Submission from ${e.data.name}`);let i=await fetch("https://api.web3forms.com/submit",{method:"POST",body:a}),o=await i.json();if(o.success)return t(e=>({...e,isSubmitting:!1,isSubmitted:!0,submitSuccess:!0})),r("success","contact_form"),!0;throw Error(o.message||"Form submission failed")}catch(e){return t(e=>({...e,isSubmitting:!1,isSubmitted:!0,submitSuccess:!1,errors:{general:"There was an error sending your message. Please try again or contact us directly."}})),r("error","contact_form"),!1}},[e.data,r]);return{formData:e.data,errors:e.errors,isSubmitting:e.isSubmitting,isSubmitted:e.isSubmitted,submitSuccess:e.submitSuccess,updateField:i,handleFieldFocus:d,validateSingleField:u,submitForm:b,resetForm:m,isValid:0===Object.keys(e.errors).length,hasErrors:Object.keys(e.errors).length>0}}var u=r(22038),m=r(46463),b=r.n(m);let p=!!(process.env.NEXT_PUBLIC_SANITY_PROJECT_ID&&process.env.NEXT_PUBLIC_SANITY_DATASET),h=p?(0,u.UU)({projectId:process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,dataset:process.env.NEXT_PUBLIC_SANITY_DATASET||"production",apiVersion:"2024-01-01",useCdn:!0,token:process.env.SANITY_API_TOKEN}):null,g=(p&&h&&b()(h),(e,t=!1,r=null)=>{let a=new Date().getFullYear();return{heroHeadline:e?.heroHeadline||s.zR.hero.headline,heroSubtext:e?.heroSubtext||s.zR.hero.subtext,heroButtonText:e?.heroButtonText||s.zR.hero.buttonText,contactHeadline:e?.contactHeadline||s.zR.contact.headline,contactSubtext:e?.contactSubtext||s.zR.contact.subtext,contactButtonText:e?.contactButtonText||s.zR.contact.buttonText,formSuccessMessage:e?.formSuccessMessage||s.zR.contact.successMessage,formErrorMessage:e?.formErrorMessage||s.zR.contact.errorMessage,servicesHeadline:e?.servicesHeadline||s.zR.services.headline,servicesSubtext:e?.servicesSubtext||s.zR.services.subtext,processHeadline:e?.processHeadline||s.zR.process.headline,processSubtext:e?.processSubtext||s.zR.process.subtext,aboutHeadline:e?.aboutHeadline||s.zR.about.headline,aboutSubtext:e?.aboutSubtext||s.zR.about.subtext,footerTagline:e?.footerTagline||"Building the future of mobile apps",footerCopyright:e?.footerCopyright||`\xa9 ${a} Mobilify. All rights reserved.`,isLoading:t,isFromCMS:!!e,error:r}});function x(e){let t=function(){let[e,t]=(0,a.useState)(null),[r,s]=(0,a.useState)(!0),[i,o]=(0,a.useState)(null);return g(e,r,i)}();switch(e){case"hero":return{headline:t.heroHeadline,subtext:t.heroSubtext,buttonText:t.heroButtonText,isLoading:t.isLoading,isFromCMS:t.isFromCMS};case"contact":return{headline:t.contactHeadline,subtext:t.contactSubtext,buttonText:t.contactButtonText,successMessage:t.formSuccessMessage,errorMessage:t.formErrorMessage,isLoading:t.isLoading,isFromCMS:t.isFromCMS};case"services":return{headline:t.servicesHeadline,subtext:t.servicesSubtext,isLoading:t.isLoading,isFromCMS:t.isFromCMS};case"process":return{headline:t.processHeadline,subtext:t.processSubtext,isLoading:t.isLoading,isFromCMS:t.isFromCMS};case"about":return{headline:t.aboutHeadline,subtext:t.aboutSubtext,isLoading:t.isLoading,isFromCMS:t.isFromCMS};default:return{isLoading:t.isLoading,isFromCMS:t.isFromCMS}}}},13143:(e,t,r)=>{r.d(t,{default:()=>l});var a=r(60687);r(43210);var s=r(21134),i=r(363),o=r(1188);let l=({className:e="",size:t="md"})=>{let{theme:r,toggleTheme:l}=(0,o.DP)();return(0,a.jsx)("button",{onClick:l,className:`${(()=>{switch(t){case"sm":return"w-8 h-8 text-sm";case"lg":return"w-12 h-12 text-lg";default:return"w-10 h-10 text-base"}})()} flex items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 ${e}`,"aria-label":`Switch to ${"dark"===r?"light":"dark"} mode`,children:"dark"===r?(0,a.jsx)(s.A,{className:"w-5 h-5"}):(0,a.jsx)(i.A,{className:"w-5 h-5"})})}},87975:(e,t,r)=>{r.d(t,{$n:()=>d,Zp:()=>h,Wu:()=>p,pd:()=>m});var a=r(60687),s=r(43210),i=r.n(s),o=r(49384),l=r(82348);function n(...e){return(0,l.QP)((0,o.$)(e))}let c=i().forwardRef(({className:e,variant:t="primary",size:r="md",isLoading:s=!1,disabled:i,children:o,...l},c)=>{let d=n("inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-electric-blue text-white hover:opacity-90 focus:ring-electric-blue shadow-lg hover:shadow-xl",secondary:"border border-electric-blue text-electric-blue hover:bg-electric-blue hover:text-white focus:ring-electric-blue",ghost:"text-electric-blue hover:bg-electric-blue hover:bg-opacity-10 focus:ring-electric-blue"}[t],{sm:"px-4 py-2 text-sm",md:"px-6 py-3 text-base",lg:"px-8 py-4 text-lg"}[r],e);return(0,a.jsx)("button",{ref:c,className:d,disabled:i||s,...l,children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading..."]}):o})});c.displayName="Button";let d=c,u=i().forwardRef(({className:e,variant:t="base",label:r,helperText:s,errorMessage:i,...o},l)=>{let c=n("w-full px-4 py-3 rounded-lg text-base transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{base:"border border-gray-300 dark:border-gray-600 focus:ring-electric-blue focus:border-electric-blue bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400",error:"border border-red-500 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400",success:"border border-green-500 focus:ring-green-500 focus:border-green-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"}[t],e),d="error"===t&&i,u="error"!==t&&s;return(0,a.jsxs)("div",{className:"w-full",children:[r&&(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r}),(0,a.jsx)("input",{ref:l,className:c,...o}),d&&(0,a.jsx)("p",{className:"mt-2 text-sm text-red-600 dark:text-red-400",children:i}),u&&(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:s})]})});u.displayName="Input";let m=u,b=i().forwardRef(({className:e,variant:t="base",children:r,...s},i)=>{let o=n("bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",{base:"",hover:"hover:shadow-md transition-shadow duration-200",interactive:"hover:shadow-lg cursor-pointer transition-shadow duration-200"}[t],e);return(0,a.jsx)("div",{ref:i,className:o,...s,children:r})});b.displayName="Card",i().forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:n("p-6 pb-0",e),...t})).displayName="CardHeader";let p=i().forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:n("p-6",e),...t}));p.displayName="CardContent",i().forwardRef(({className:e,...t},r)=>(0,a.jsx)("div",{ref:r,className:n("p-6 pt-0",e),...t})).displayName="CardFooter";let h=b},89733:(e,t,r)=>{r.d(t,{A:()=>s});var a=r(60687);r(43210);let s=({className:e=""})=>(0,a.jsx)("div",{className:`inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ${e}`,children:(0,a.jsx)("span",{className:"text-white font-bold text-xl",children:"M"})})},90261:(e,t,r)=>{r.d(t,{default:()=>p});var a=r(60687),s=r(43210),i=r(11860),o=r(12941),l=r(89733),n=r(47009),c=r(33872);let d=()=>(0,a.jsxs)("button",{onClick:()=>{window.location.href="mailto:<EMAIL>?subject=Chat%20Request"},className:"inline-flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200","aria-label":"Open chat",children:[(0,a.jsx)(c.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Chat"})]});var u=r(13143),m=r(1745),b=r(7378);let p=()=>{let[e,t]=(0,s.useState)(!1),{trackNavigation:r}=(0,b.st)(),c=e=>{let a=document.getElementById(e);a&&(a.scrollIntoView({behavior:"smooth"}),r(e,"header_nav")),t(!1)},p=m.$U.main;return(0,a.jsxs)("header",{className:"fixed top-0 left-0 right-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 z-50 transition-colors duration-300",children:[(0,a.jsx)("div",{className:"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 w-full",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(l.A,{}),(0,a.jsx)("span",{className:"ml-2 text-xl font-bold text-dark-charcoal dark:text-white",children:m.jx.name})]}),(0,a.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[p.map(e=>(0,a.jsx)("button",{onClick:()=>c(e.id),className:"text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200",children:e.label},e.label)),(0,a.jsx)(n.default,{children:(0,a.jsx)(d,{})}),(0,a.jsx)(n.default,{children:(0,a.jsx)(u.default,{size:"sm",className:"mr-4"})}),(0,a.jsx)("button",{onClick:()=>c("contact"),className:"bg-electric-blue text-white px-6 py-2 rounded-lg hover:opacity-90 transition-all duration-200",children:"Get a Quote"})]}),(0,a.jsx)("button",{onClick:()=>t(!e),className:"md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100",children:e?(0,a.jsx)(i.A,{size:24}):(0,a.jsx)(o.A,{size:24})})]})}),(0,a.jsx)(n.default,{children:e&&(0,a.jsx)("div",{className:"md:hidden fixed inset-0 top-16 bg-white z-40",children:(0,a.jsxs)("div",{className:"px-4 py-6 space-y-4",children:[p.map(e=>(0,a.jsx)("button",{onClick:()=>c(e.href.substring(1)),className:"block w-full text-left text-lg text-gray-600 hover:text-gray-900 py-2",children:e.label},e.label)),(0,a.jsx)("button",{onClick:()=>c("contact"),className:"block w-full bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors duration-200 mt-6",children:"Get a Quote"})]})})})]})}},91477:(e,t,r)=>{r.r(t),r.d(t,{default:()=>l});var a=r(60687),s=r(43210),i=r(26001),o=r(87975);let l=({variant:e="inline",className:t=""})=>{let[r,l]=(0,s.useState)(""),[n,c]=(0,s.useState)(!1),[d,u]=(0,s.useState)("idle"),m=async t=>{if(t.preventDefault(),r.trim()){c(!0),u("idle");try{let t=process.env.NEXT_PUBLIC_MAILCHIMP_API_KEY,a=process.env.MAILCHIMP_LIST_ID;if(!t||!a){await new Promise(e=>setTimeout(e,1e3)),u("success"),l("");return}(await fetch("/api/newsletter",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r,source:e})})).ok?(u("success"),l("")):u("error")}catch(e){u("error")}finally{c(!1)}}};return(0,a.jsxs)("div",{className:`${t}`,children:[("inline"===e||"section"===e)&&(0,a.jsx)(i.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:"py-16 md:py-20 bg-electric-blue",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"Stay Updated on Mobile Innovation"}),(0,a.jsx)("p",{className:"text-lg md:text-xl leading-relaxed text-blue-100 max-w-3xl mx-auto mb-8",children:"Get insights on mobile app development, industry trends, and exclusive tips delivered to your inbox."}),(0,a.jsxs)("form",{onSubmit:m,className:"max-w-md mx-auto",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)(o.pd,{type:"email",value:r,onChange:e=>l(e.target.value),placeholder:"Enter your email address",required:!0,className:"flex-1 bg-white border-white focus:ring-white focus:border-white",disabled:n}),(0,a.jsx)(o.$n,{type:"submit",variant:"secondary",isLoading:n,disabled:!r.trim()||n,className:"bg-white text-electric-blue hover:bg-gray-50 border-white",children:"Subscribe"})]}),"success"===d&&(0,a.jsx)(i.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4 text-green-100 text-sm",children:"✓ Successfully subscribed! Check your email for confirmation."}),"error"===d&&(0,a.jsx)(i.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4 text-red-100 text-sm",children:"✗ Something went wrong. Please try again."})]})]})}),"footer"===e&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4 text-white",children:"Stay Connected"}),(0,a.jsx)("p",{className:"text-sm text-gray-400 dark:text-gray-300 mb-4 leading-relaxed",children:"Get the latest updates on mobile app development and industry insights."}),(0,a.jsxs)("form",{onSubmit:m,className:"space-y-4",children:[(0,a.jsx)(o.pd,{type:"email",value:r,onChange:e=>l(e.target.value),placeholder:"Enter your email",required:!0,className:"bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:ring-electric-blue focus:border-electric-blue",disabled:n}),(0,a.jsx)(o.$n,{type:"submit",variant:"primary",size:"sm",isLoading:n,disabled:!r.trim()||n,className:"w-full",children:"Subscribe"}),"success"===d&&(0,a.jsx)(i.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-green-400 text-sm",children:"✓ Successfully subscribed!"}),"error"===d&&(0,a.jsx)(i.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-red-400 text-sm",children:"✗ Please try again."})]})]})]})}}};