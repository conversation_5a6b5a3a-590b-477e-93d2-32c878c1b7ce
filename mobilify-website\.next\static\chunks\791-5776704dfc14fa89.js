(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[791],{845:(t,e,r)=>{"use strict";r.d(e,{t:()=>n});let n=(0,r(2115).createContext)(null)},869:(t,e,r)=>{"use strict";r.d(e,{L:()=>n});let n=(0,r(2115).createContext)({})},1366:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},1501:(t,e,r)=>{"use strict";function n(t){return"object"==typeof t&&null!==t&&!Array.isArray(t)}r.d(e,{C:()=>a,Q:()=>c,u4:()=>n});var i={0:8203,1:8204,2:8205,3:8290,4:8291,5:8288,6:65279,7:8289,8:119155,9:119156,a:119157,b:119158,c:119159,d:119160,e:119161,f:119162},o={0:8203,1:8204,2:8205,3:65279},s=[,,,,].fill(String.fromCodePoint(o[0])).join("");function a(t,e,r="auto"){let n;return!0===r||"auto"===r&&(!(!Number.isNaN(Number(t))||/[a-z]/i.test(t)&&!/\d+(?:[-:\/]\d+){2}(?:T\d+(?:[-:\/]\d+){1,2}(\.\d+)?Z?)?/.test(t))&&Date.parse(t)||function(t){try{new URL(t,t.startsWith("/")?"https://acme.com":void 0)}catch{return!1}return!0}(t))?t:`${t}${n=JSON.stringify(e),`${s}${Array.from(n).map(t=>{let e=t.charCodeAt(0);if(e>255)throw Error(`Only ASCII edit info can be encoded. Error attempting to encode ${n} on character ${t} (${e})`);return Array.from(e.toString(4).padStart(4,"0")).map(t=>String.fromCodePoint(o[t])).join("")}).join("")}`}`}Object.fromEntries(Object.entries(o).map(t=>t.reverse())),Object.fromEntries(Object.entries(i).map(t=>t.reverse()));var l=`${Object.values(i).map(t=>`\\u{${t.toString(16)}}`).join("")}`,u=RegExp(`[${l}]{4,}`,"gu");function c(t){var e,r;return t&&JSON.parse({cleaned:(e=JSON.stringify(t)).replace(u,""),encoded:(null==(r=e.match(u))?void 0:r[0])||""}.cleaned)}},1508:(t,e,r)=>{"use strict";r.d(e,{Q:()=>n});let n=(0,r(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},2082:(t,e,r)=>{"use strict";r.d(e,{xQ:()=>o});var n=r(2115),i=r(845);function o(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0],e=(0,n.useContext)(i.t);if(null===e)return[!0,null];let{isPresent:r,onExitComplete:o,register:s}=e,a=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return s(a)},[t]);let l=(0,n.useCallback)(()=>t&&o&&o(a),[a,o,t]);return!r&&o?[!1,l]:[!0]}},2098:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2596:(t,e,r)=>{"use strict";function n(){for(var t,e,r=0,n="",i=arguments.length;r<i;r++)(t=arguments[r])&&(e=function t(e){var r,n,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(r=0;r<o;r++)e[r]&&(n=t(e[r]))&&(i&&(i+=" "),i+=n)}else for(n in e)e[n]&&(i&&(i+=" "),i+=n);return i}(t))&&(n&&(n+=" "),n+=e);return n}r.d(e,{$:()=>n})},2885:(t,e,r)=>{"use strict";r.d(e,{M:()=>i});var n=r(2115);function i(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},3406:(t,e,r)=>{"use strict";r.d(e,{UU:()=>nw});let n=!(typeof navigator>"u")&&"ReactNative"===navigator.product,i={timeout:n?6e4:12e4},o=function(t){let e={...i,..."string"==typeof t?{url:t}:t};if(e.timeout=function t(e){if(!1===e||0===e)return!1;if(e.connect||e.socket)return e;let r=Number(e);return isNaN(r)?t(i.timeout):{connect:r,socket:r}}(e.timeout),e.query){let{url:t,searchParams:r}=function(t){let e=t.indexOf("?");if(-1===e)return{url:t,searchParams:new URLSearchParams};let r=t.slice(0,e),i=t.slice(e+1);if(!n)return{url:r,searchParams:new URLSearchParams(i)};if("function"!=typeof decodeURIComponent)throw Error("Broken `URLSearchParams` implementation, and `decodeURIComponent` is not defined");let o=new URLSearchParams;for(let t of i.split("&")){let[e,r]=t.split("=");e&&o.append(s(e),s(r||""))}return{url:r,searchParams:o}}(e.url);for(let[n,i]of Object.entries(e.query)){if(void 0!==i)if(Array.isArray(i))for(let t of i)r.append(n,t);else r.append(n,i);let o=r.toString();o&&(e.url=`${t}?${o}`)}}return e.method=e.body&&!e.method?"POST":(e.method||"GET").toUpperCase(),e};function s(t){return decodeURIComponent(t.replace(/\+/g," "))}let a=/^https?:\/\//i,l=function(t){if(!a.test(t.url))throw Error(`"${t.url}" is not a valid URL`)};function u(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}let c=["request","response","progress","error","abort"],h=["processOptions","validateOptions","interceptRequest","finalizeOptions","onRequest","onResponse","onError","onReturn","onHeaders"];var d,p,f=u(function(){if(p)return d;p=1;var t=function(t){return t.replace(/^\s+|\s+$/g,"")};return d=function(e){if(!e)return{};for(var r=Object.create(null),n=t(e).split("\n"),i=0;i<n.length;i++){var o,s=n[i],a=s.indexOf(":"),l=t(s.slice(0,a)).toLowerCase(),u=t(s.slice(a+1));typeof r[l]>"u"?r[l]=u:(o=r[l],"[object Array]"===Object.prototype.toString.call(o))?r[l].push(u):r[l]=[r[l],u]}return r}}());class m{onabort;onerror;onreadystatechange;ontimeout;readyState=0;response;responseText="";responseType="";status;statusText;withCredentials;#t;#e;#r;#n={};#i;#o={};#s;open(t,e,r){this.#t=t,this.#e=e,this.#r="",this.readyState=1,this.onreadystatechange?.(),this.#i=void 0}abort(){this.#i&&this.#i.abort()}getAllResponseHeaders(){return this.#r}setRequestHeader(t,e){this.#n[t]=e}setInit(t,e=!0){this.#o=t,this.#s=e}send(t){let e="arraybuffer"!==this.responseType,r={...this.#o,method:this.#t,headers:this.#n,body:t};"function"==typeof AbortController&&this.#s&&(this.#i=new AbortController,"u">typeof EventTarget&&this.#i.signal instanceof EventTarget&&(r.signal=this.#i.signal)),"u">typeof document&&(r.credentials=this.withCredentials?"include":"omit"),fetch(this.#e,r).then(t=>(t.headers.forEach((t,e)=>{this.#r+=`${e}: ${t}\r
`}),this.status=t.status,this.statusText=t.statusText,this.readyState=3,this.onreadystatechange?.(),e?t.text():t.arrayBuffer())).then(t=>{"string"==typeof t?this.responseText=t:this.response=t,this.readyState=4,this.onreadystatechange?.()}).catch(t=>{"AbortError"!==t.name?this.onerror?.(t):this.onabort?.()})}}let g="function"==typeof XMLHttpRequest?"xhr":"fetch",y="xhr"===g?XMLHttpRequest:m,v=(t,e)=>{let r=t.options,n=t.applyMiddleware("finalizeOptions",r),i={},o=t.applyMiddleware("interceptRequest",void 0,{adapter:g,context:t});if(o){let t=setTimeout(e,0,null,o);return{abort:()=>clearTimeout(t)}}let s=new y;s instanceof m&&"object"==typeof n.fetch&&s.setInit(n.fetch,n.useAbortSignal??!0);let a=n.headers,l=n.timeout,u=!1,c=!1,h=!1;if(s.onerror=t=>{v(s instanceof m?t instanceof Error?t:Error(`Request error while attempting to reach is ${n.url}`,{cause:t}):Error(`Request error while attempting to reach is ${n.url}${t.lengthComputable?`(${t.loaded} of ${t.total} bytes transferred)`:""}`))},s.ontimeout=t=>{v(Error(`Request timeout while attempting to reach ${n.url}${t.lengthComputable?`(${t.loaded} of ${t.total} bytes transferred)`:""}`))},s.onabort=()=>{p(!0),u=!0},s.onreadystatechange=function(){l&&(p(),i.socket=setTimeout(()=>d("ESOCKETTIMEDOUT"),l.socket)),!u&&s&&4===s.readyState&&0!==s.status&&function(){if(!(u||c||h)){if(0===s.status)return v(Error("Unknown XHR error"));p(),c=!0,e(null,{body:s.response||(""===s.responseType||"text"===s.responseType?s.responseText:""),url:n.url,method:n.method,headers:f(s.getAllResponseHeaders()),statusCode:s.status,statusMessage:s.statusText})}}()},s.open(n.method,n.url,!0),s.withCredentials=!!n.withCredentials,a&&s.setRequestHeader)for(let t in a)a.hasOwnProperty(t)&&s.setRequestHeader(t,a[t]);return n.rawBody&&(s.responseType="arraybuffer"),t.applyMiddleware("onRequest",{options:n,adapter:g,request:s,context:t}),s.send(n.body||null),l&&(i.connect=setTimeout(()=>d("ETIMEDOUT"),l.connect)),{abort:function(){u=!0,s&&s.abort()}};function d(e){h=!0,s.abort();let r=Error("ESOCKETTIMEDOUT"===e?`Socket timed out on request to ${n.url}`:`Connection timed out on request to ${n.url}`);r.code=e,t.channels.error.publish(r)}function p(t){(t||u||s&&s.readyState>=2&&i.connect)&&clearTimeout(i.connect),i.socket&&clearTimeout(i.socket)}function v(t){if(c)return;p(!0),c=!0,s=null;let r=t||Error(`Network error while attempting to reach ${n.url}`);r.isNetworkError=!0,r.request=n,e(r)}},b=(t=[],e=v)=>(function t(e,r){let n=[],i=h.reduce((t,e)=>(t[e]=t[e]||[],t),{processOptions:[o],validateOptions:[l]});function s(t){let e,n=c.reduce((t,e)=>(t[e]=function(){let t=Object.create(null),e=0;return{publish:function(e){for(let r in t)t[r](e)},subscribe:function(r){let n=e++;return t[n]=r,function(){delete t[n]}}}}(),t),{}),o=function(t,e,...r){let n="onError"===t,o=e;for(let e=0;e<i[t].length&&(o=(0,i[t][e])(o,...r),!n||o);e++);return o},s=o("processOptions",t);o("validateOptions",s);let a={options:s,channels:n,applyMiddleware:o},l=n.request.subscribe(t=>{e=r(t,(e,r)=>((t,e,r)=>{let i=t,s=e;if(!i)try{s=o("onResponse",e,r)}catch(t){s=null,i=t}(i=i&&o("onError",i,r))?n.error.publish(i):s&&n.response.publish(s)})(e,r,t))});n.abort.subscribe(()=>{l(),e&&e.abort()});let u=o("onReturn",n,a);return u===n&&n.request.publish(a),u}return s.use=function(t){if(!t)throw Error("Tried to add middleware that resolved to falsey value");if("function"==typeof t)throw Error("Tried to add middleware that was a function. It probably expects you to pass options to it.");if(t.onReturn&&i.onReturn.length>0)throw Error("Tried to add new middleware with `onReturn` handler, but another handler has already been registered for this event");return h.forEach(e=>{t[e]&&i[e].push(t[e])}),n.push(t),s},s.clone=()=>t(n,r),e.forEach(s.use),s})(t,e);var w=r(9509),x=r(9641).Buffer,E,T,C,A,S,k={exports:{}};S||(S=1,function(t,e){let r;e.formatArgs=function(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+t.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;e.splice(1,0,r,"color: inherit");let n=0,i=0;e[0].replace(/%[a-zA-Z%]/g,t=>{"%%"!==t&&(n++,"%c"===t&&(i=n))}),e.splice(i,0,r)},e.save=function(t){try{t?e.storage.setItem("debug",t):e.storage.removeItem("debug")}catch{}},e.load=function(){let t;try{t=e.storage.getItem("debug")||e.storage.getItem("DEBUG")}catch{}return!t&&"u">typeof w&&"env"in w&&(t=w.env.DEBUG),t},e.useColors=function(){let t;return"u">typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("u">typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("u">typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"u">typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"u">typeof navigator&&navigator.userAgent&&(t=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(t[1],10)>=31||"u">typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},e.storage=function(){try{return localStorage}catch{}}(),r=!1,e.destroy=()=>{r||(r=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))},e.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],e.log=console.debug||console.log||(()=>{}),t.exports=(A?C:(A=1,C=function(t){function e(t){let n,i,o,s=null;function a(...t){if(!a.enabled)return;let r=Number(new Date);a.diff=r-(n||r),a.prev=n,a.curr=r,n=r,t[0]=e.coerce(t[0]),"string"!=typeof t[0]&&t.unshift("%O");let i=0;t[0]=t[0].replace(/%([a-zA-Z%])/g,(r,n)=>{if("%%"===r)return"%";i++;let o=e.formatters[n];if("function"==typeof o){let e=t[i];r=o.call(a,e),t.splice(i,1),i--}return r}),e.formatArgs.call(a,t),(a.log||e.log).apply(a,t)}return a.namespace=t,a.useColors=e.useColors(),a.color=e.selectColor(t),a.extend=r,a.destroy=e.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(i!==e.namespaces&&(i=e.namespaces,o=e.enabled(t)),o),set:t=>{s=t}}),"function"==typeof e.init&&e.init(a),a}function r(t,r){let n=e(this.namespace+(typeof r>"u"?":":r)+t);return n.log=this.log,n}function n(t,e){let r=0,n=0,i=-1,o=0;for(;r<t.length;)if(n<e.length&&(e[n]===t[r]||"*"===e[n]))"*"===e[n]?(i=n,o=r):r++,n++;else{if(-1===i)return!1;n=i+1,r=++o}for(;n<e.length&&"*"===e[n];)n++;return n===e.length}return e.debug=e,e.default=e,e.coerce=function(t){return t instanceof Error?t.stack||t.message:t},e.disable=function(){let t=[...e.names,...e.skips.map(t=>"-"+t)].join(",");return e.enable(""),t},e.enable=function(t){for(let r of(e.save(t),e.namespaces=t,e.names=[],e.skips=[],("string"==typeof t?t:"").trim().replace(/\s+/g,",").split(",").filter(Boolean)))"-"===r[0]?e.skips.push(r.slice(1)):e.names.push(r)},e.enabled=function(t){for(let r of e.skips)if(n(t,r))return!1;for(let r of e.names)if(n(t,r))return!0;return!1},e.humanize=function(){if(T)return E;function t(t,e,r,n){return Math.round(t/r)+" "+n+(e>=1.5*r?"s":"")}return T=1,E=function(e,r){r=r||{};var n,i,o=typeof e;if("string"===o&&e.length>0){var s=e;if(!((s=String(s)).length>100)){var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(s);if(a){var l=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*l;case"weeks":case"week":case"w":return 6048e5*l;case"days":case"day":case"d":return 864e5*l;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*l;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*l;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*l;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return l}}}return}if("number"===o&&isFinite(e))return r.long?(i=Math.abs(e))>=864e5?t(e,i,864e5,"day"):i>=36e5?t(e,i,36e5,"hour"):i>=6e4?t(e,i,6e4,"minute"):i>=1e3?t(e,i,1e3,"second"):e+" ms":(n=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":n>=36e5?Math.round(e/36e5)+"h":n>=6e4?Math.round(e/6e4)+"m":n>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}}(),e.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(t).forEach(r=>{e[r]=t[r]}),e.names=[],e.skips=[],e.formatters={},e.selectColor=function(t){let r=0;for(let e=0;e<t.length;e++)r=(r<<5)-r+t.charCodeAt(e)|0;return e.colors[Math.abs(r)%e.colors.length]},e.enable(e.load()),e}))(e);let{formatters:n}=t.exports;n.j=function(t){try{return JSON.stringify(t)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}}(k,k.exports)),k.exports,Object.prototype.hasOwnProperty;let P=typeof x>"u"?()=>!1:t=>x.isBuffer(t);function R(t){return"[object Object]"===Object.prototype.toString.call(t)}let M=["boolean","string","number"],j={};"u">typeof globalThis?j=globalThis:"u">typeof window?j=window:"u">typeof global?j=global:"u">typeof self&&(j=self);var O=j;let I=(t={})=>{let e=t.implementation||Promise;if(!e)throw Error("`Promise` is not available in global scope, and no implementation was passed");return{onReturn:(r,n)=>new e((e,i)=>{let o=n.options.cancelToken;o&&o.promise.then(t=>{r.abort.publish(t),i(t)}),r.error.subscribe(i),r.response.subscribe(r=>{e(t.onlyBody?r.body:r)}),setTimeout(()=>{try{r.request.publish(n)}catch(t){i(t)}},0)})}};class D{__CANCEL__=!0;message;constructor(t){this.message=t}toString(){return"Cancel"+(this.message?`: ${this.message}`:"")}}class V{promise;reason;constructor(t){if("function"!=typeof t)throw TypeError("executor must be a function.");let e=null;this.promise=new Promise(t=>{e=t}),t(t=>{this.reason||(this.reason=new D(t),e(this.reason))})}static source=()=>{let t;return{token:new V(e=>{t=e}),cancel:t}}}I.Cancel=D,I.CancelToken=V,I.isCancel=t=>!(!t||!t?.__CANCEL__);var q=(t,e,r)=>("GET"===r.method||"HEAD"===r.method)&&(t.isNetworkError||!1);function F(t){return 100*Math.pow(2,t)+100*Math.random()}let $=(t={})=>(t=>{let e=t.maxRetries||5,r=t.retryDelay||F,n=t.shouldRetry;return{onError:(t,i)=>{var o;let s=i.options,a=s.maxRetries||e,l=s.retryDelay||r,u=s.shouldRetry||n,c=s.attemptNumber||0;if(null!==(o=s.body)&&"object"==typeof o&&"function"==typeof o.pipe||!u(t,c,s)||c>=a)return t;let h=Object.assign({},i,{options:Object.assign({},s,{attemptNumber:c+1})});return setTimeout(()=>i.channels.request.publish(h),l(c)),null}}})({shouldRetry:q,...t});$.shouldRetry=q;var L=function(t,e){return(L=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function B(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}L(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function _(t,e){var r,n,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},s=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return s.next=a(0),s.throw=a(1),s.return=a(2),"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(a){return function(l){var u=[a,l];if(r)throw TypeError("Generator is already executing.");for(;s&&(s=0,u[0]&&(o=0)),o;)try{if(r=1,n&&(i=2&u[0]?n.return:u[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,u[1])).done)return i;switch(n=0,i&&(u=[2&u[0],i.value]),u[0]){case 0:case 1:i=u;break;case 4:return o.label++,{value:u[1],done:!1};case 5:o.label++,n=u[1],u=[0];continue;case 7:u=o.ops.pop(),o.trys.pop();continue;default:if(!(i=(i=o.trys).length>0&&i[i.length-1])&&(6===u[0]||2===u[0])){o=0;continue}if(3===u[0]&&(!i||u[1]>i[0]&&u[1]<i[3])){o.label=u[1];break}if(6===u[0]&&o.label<i[1]){o.label=i[1],i=u;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(u);break}i[2]&&o.ops.pop(),o.trys.pop();continue}u=e.call(t,o)}catch(t){u=[6,t],n=0}finally{r=i=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}}}function U(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function z(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(n=o.next()).done;)s.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s}function N(t,e,r){if(r||2==arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))}function W(t){return this instanceof W?(this.v=t,this):new W(t)}function H(t){return"function"==typeof t}function Y(t){var e=t(function(t){Error.call(t),t.stack=Error().stack});return e.prototype=Object.create(Error.prototype),e.prototype.constructor=e,e}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var G=Y(function(t){return function(e){t(this),this.message=e?e.length+" errors occurred during unsubscription:\n"+e.map(function(t,e){return e+1+") "+t.toString()}).join("\n  "):"",this.name="UnsubscriptionError",this.errors=e}});function X(t,e){if(t){var r=t.indexOf(e);0<=r&&t.splice(r,1)}}var K=function(){var t;function e(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}return e.prototype.unsubscribe=function(){if(!this.closed){this.closed=!0;var t,e,r,n,i,o=this._parentage;if(o)if(this._parentage=null,Array.isArray(o))try{for(var s=U(o),a=s.next();!a.done;a=s.next())a.value.remove(this)}catch(e){t={error:e}}finally{try{a&&!a.done&&(e=s.return)&&e.call(s)}finally{if(t)throw t.error}}else o.remove(this);var l=this.initialTeardown;if(H(l))try{l()}catch(t){i=t instanceof G?t.errors:[t]}var u=this._finalizers;if(u){this._finalizers=null;try{for(var c=U(u),h=c.next();!h.done;h=c.next()){var d=h.value;try{Q(d)}catch(t){i=null!=i?i:[],t instanceof G?i=N(N([],z(i)),z(t.errors)):i.push(t)}}}catch(t){r={error:t}}finally{try{h&&!h.done&&(n=c.return)&&n.call(c)}finally{if(r)throw r.error}}}if(i)throw new G(i)}},e.prototype.add=function(t){var r;if(t&&t!==this)if(this.closed)Q(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=null!=(r=this._finalizers)?r:[]).push(t)}},e.prototype._hasParent=function(t){var e=this._parentage;return e===t||Array.isArray(e)&&e.includes(t)},e.prototype._addParent=function(t){var e=this._parentage;this._parentage=Array.isArray(e)?(e.push(t),e):e?[e,t]:t},e.prototype._removeParent=function(t){var e=this._parentage;e===t?this._parentage=null:Array.isArray(e)&&X(e,t)},e.prototype.remove=function(t){var r=this._finalizers;r&&X(r,t),t instanceof e&&t._removeParent(this)},(t=new e).closed=!0,e.EMPTY=t,e}(),Z=K.EMPTY;function J(t){return t instanceof K||t&&"closed"in t&&H(t.remove)&&H(t.add)&&H(t.unsubscribe)}function Q(t){H(t)?t():t.unsubscribe()}var tt={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},te={setTimeout:function(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var i=te.delegate;return(null==i?void 0:i.setTimeout)?i.setTimeout.apply(i,N([t,e],z(r))):setTimeout.apply(void 0,N([t,e],z(r)))},clearTimeout:function(t){var e=te.delegate;return((null==e?void 0:e.clearTimeout)||clearTimeout)(t)},delegate:void 0};function tr(t){te.setTimeout(function(){var e=tt.onUnhandledError;if(e)e(t);else throw t})}function tn(){}var ti=to("C",void 0,void 0);function to(t,e,r){return{kind:t,value:e,error:r}}var ts=null;function ta(t){if(tt.useDeprecatedSynchronousErrorHandling){var e=!ts;if(e&&(ts={errorThrown:!1,error:null}),t(),e){var r=ts,n=r.errorThrown,i=r.error;if(ts=null,n)throw i}}else t()}var tl=function(t){function e(e){var r=t.call(this)||this;return r.isStopped=!1,e?(r.destination=e,J(e)&&e.add(r)):r.destination=tm,r}return B(e,t),e.create=function(t,e,r){return new td(t,e,r)},e.prototype.next=function(t){this.isStopped?tf(to("N",t,void 0),this):this._next(t)},e.prototype.error=function(t){this.isStopped?tf(to("E",void 0,t),this):(this.isStopped=!0,this._error(t))},e.prototype.complete=function(){this.isStopped?tf(ti,this):(this.isStopped=!0,this._complete())},e.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,t.prototype.unsubscribe.call(this),this.destination=null)},e.prototype._next=function(t){this.destination.next(t)},e.prototype._error=function(t){try{this.destination.error(t)}finally{this.unsubscribe()}},e.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},e}(K),tu=Function.prototype.bind;function tc(t,e){return tu.call(t,e)}var th=function(){function t(t){this.partialObserver=t}return t.prototype.next=function(t){var e=this.partialObserver;if(e.next)try{e.next(t)}catch(t){tp(t)}},t.prototype.error=function(t){var e=this.partialObserver;if(e.error)try{e.error(t)}catch(t){tp(t)}else tp(t)},t.prototype.complete=function(){var t=this.partialObserver;if(t.complete)try{t.complete()}catch(t){tp(t)}},t}(),td=function(t){function e(e,r,n){var i,o,s=t.call(this)||this;return H(e)||!e?i={next:null!=e?e:void 0,error:null!=r?r:void 0,complete:null!=n?n:void 0}:s&&tt.useDeprecatedNextContext?((o=Object.create(e)).unsubscribe=function(){return s.unsubscribe()},i={next:e.next&&tc(e.next,o),error:e.error&&tc(e.error,o),complete:e.complete&&tc(e.complete,o)}):i=e,s.destination=new th(i),s}return B(e,t),e}(tl);function tp(t){if(tt.useDeprecatedSynchronousErrorHandling)tt.useDeprecatedSynchronousErrorHandling&&ts&&(ts.errorThrown=!0,ts.error=t);else tr(t)}function tf(t,e){var r=tt.onStoppedNotification;r&&te.setTimeout(function(){return r(t,e)})}var tm={closed:!0,next:tn,error:function(t){throw t},complete:tn},tg="function"==typeof Symbol&&Symbol.observable||"@@observable";function ty(t){return t}function tv(t){return 0===t.length?ty:1===t.length?t[0]:function(e){return t.reduce(function(t,e){return e(t)},e)}}var tb=function(){function t(t){t&&(this._subscribe=t)}return t.prototype.lift=function(e){var r=new t;return r.source=this,r.operator=e,r},t.prototype.subscribe=function(t,e,r){var n=this,i=!function(t){return t&&t instanceof tl||t&&H(t.next)&&H(t.error)&&H(t.complete)&&J(t)}(t)?new td(t,e,r):t;return ta(function(){var t=n.operator,e=n.source;i.add(t?t.call(i,e):e?n._subscribe(i):n._trySubscribe(i))}),i},t.prototype._trySubscribe=function(t){try{return this._subscribe(t)}catch(e){t.error(e)}},t.prototype.forEach=function(t,e){var r=this;return new(e=tw(e))(function(e,n){var i=new td({next:function(e){try{t(e)}catch(t){n(t),i.unsubscribe()}},error:n,complete:e});r.subscribe(i)})},t.prototype._subscribe=function(t){var e;return null==(e=this.source)?void 0:e.subscribe(t)},t.prototype[tg]=function(){return this},t.prototype.pipe=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return tv(t)(this)},t.prototype.toPromise=function(t){var e=this;return new(t=tw(t))(function(t,r){var n;e.subscribe(function(t){return n=t},function(t){return r(t)},function(){return t(n)})})},t.create=function(e){return new t(e)},t}();function tw(t){var e;return null!=(e=null!=t?t:tt.Promise)?e:Promise}var tx=function(t){return t&&"number"==typeof t.length&&"function"!=typeof t};function tE(t){return H(null==t?void 0:t.then)}function tT(t){return Symbol.asyncIterator&&H(null==t?void 0:t[Symbol.asyncIterator])}function tC(t){return TypeError("You provided "+(null!==t&&"object"==typeof t?"an invalid object":"'"+t+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}var tA="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator";function tS(t){return H(null==t?void 0:t[tA])}function tk(t){return function(t,e,r){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,i=r.apply(t,e||[]),o=[];return n=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),s("next"),s("throw"),s("return",function(t){return function(e){return Promise.resolve(e).then(t,u)}}),n[Symbol.asyncIterator]=function(){return this},n;function s(t,e){i[t]&&(n[t]=function(e){return new Promise(function(r,n){o.push([t,e,r,n])>1||a(t,e)})},e&&(n[t]=e(n[t])))}function a(t,e){try{var r;(r=i[t](e)).value instanceof W?Promise.resolve(r.value.v).then(l,u):c(o[0][2],r)}catch(t){c(o[0][3],t)}}function l(t){a("next",t)}function u(t){a("throw",t)}function c(t,e){t(e),o.shift(),o.length&&a(o[0][0],o[0][1])}}(this,arguments,function(){var e,r,n;return _(this,function(i){switch(i.label){case 0:e=t.getReader(),i.label=1;case 1:i.trys.push([1,,9,10]),i.label=2;case 2:return[4,W(e.read())];case 3:if(n=(r=i.sent()).value,!r.done)return[3,5];return[4,W(void 0)];case 4:return[2,i.sent()];case 5:return[4,W(n)];case 6:return[4,i.sent()];case 7:return i.sent(),[3,2];case 8:return[3,10];case 9:return e.releaseLock(),[7];case 10:return[2]}})})}function tP(t){return H(null==t?void 0:t.getReader)}function tR(t){if(t instanceof tb)return t;if(null!=t){var e,r,n,i;if(H(t[tg])){return e=t,new tb(function(t){var r=e[tg]();if(H(r.subscribe))return r.subscribe(t);throw TypeError("Provided object does not correctly implement Symbol.observable")})}if(tx(t)){return r=t,new tb(function(t){for(var e=0;e<r.length&&!t.closed;e++)t.next(r[e]);t.complete()})}if(tE(t)){return n=t,new tb(function(t){n.then(function(e){t.closed||(t.next(e),t.complete())},function(e){return t.error(e)}).then(null,tr)})}if(tT(t))return tM(t);if(tS(t)){return i=t,new tb(function(t){var e,r;try{for(var n=U(i),o=n.next();!o.done;o=n.next()){var s=o.value;if(t.next(s),t.closed)return}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}t.complete()})}if(tP(t))return tM(tk(t))}throw tC(t)}function tM(t){return new tb(function(e){(function(t,e){var r,n,i,o,s,a,l,u;return s=this,a=void 0,l=void 0,u=function(){var s;return _(this,function(a){switch(a.label){case 0:a.trys.push([0,5,6,11]),r=function(t){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var e,r=t[Symbol.asyncIterator];return r?r.call(t):(t=U(t),e={},n("next"),n("throw"),n("return"),e[Symbol.asyncIterator]=function(){return this},e);function n(r){e[r]=t[r]&&function(e){return new Promise(function(n,i){var o,s,a;o=n,s=i,a=(e=t[r](e)).done,Promise.resolve(e.value).then(function(t){o({value:t,done:a})},s)})}}}(t),a.label=1;case 1:return[4,r.next()];case 2:if((n=a.sent()).done)return[3,4];if(s=n.value,e.next(s),e.closed)return[2];a.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return i={error:a.sent()},[3,11];case 6:if(a.trys.push([6,,9,10]),!(n&&!n.done&&(o=r.return)))return[3,8];return[4,o.call(r)];case 7:a.sent(),a.label=8;case 8:return[3,10];case 9:if(i)throw i.error;return[7];case 10:return[7];case 11:return e.complete(),[2]}})},new(l||(l=Promise))(function(t,e){function r(t){try{i(u.next(t))}catch(t){e(t)}}function n(t){try{i(u.throw(t))}catch(t){e(t)}}function i(e){var i;e.done?t(e.value):((i=e.value)instanceof l?i:new l(function(t){t(i)})).then(r,n)}i((u=u.apply(s,a||[])).next())})})(t,e).catch(function(t){return e.error(t)})})}function tj(t){return new tb(function(e){tR(t()).subscribe(e)})}function tO(t){return t[t.length-1]}function tI(t){var e;return(e=tO(t))&&H(e.schedule)?t.pop():void 0}function tD(t,e,r,n,i){void 0===n&&(n=0),void 0===i&&(i=!1);var o=e.schedule(function(){r(),i?t.add(this.schedule(null,n)):this.unsubscribe()},n);if(t.add(o),!i)return o}function tV(t){return function(e){if(H(null==e?void 0:e.lift))return e.lift(function(e){try{return t(e,this)}catch(t){this.error(t)}});throw TypeError("Unable to lift unknown Observable type")}}function tq(t,e,r,n,i){return new tF(t,e,r,n,i)}var tF=function(t){function e(e,r,n,i,o,s){var a=t.call(this,e)||this;return a.onFinalize=o,a.shouldUnsubscribe=s,a._next=r?function(t){try{r(t)}catch(t){e.error(t)}}:t.prototype._next,a._error=i?function(t){try{i(t)}catch(t){e.error(t)}finally{this.unsubscribe()}}:t.prototype._error,a._complete=n?function(){try{n()}catch(t){e.error(t)}finally{this.unsubscribe()}}:t.prototype._complete,a}return B(e,t),e.prototype.unsubscribe=function(){var e;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;t.prototype.unsubscribe.call(this),r||null==(e=this.onFinalize)||e.call(this)}},e}(tl);function t$(t,e){return void 0===e&&(e=0),tV(function(r,n){r.subscribe(tq(n,function(r){return tD(n,t,function(){return n.next(r)},e)},function(){return tD(n,t,function(){return n.complete()},e)},function(r){return tD(n,t,function(){return n.error(r)},e)}))})}function tL(t,e){return void 0===e&&(e=0),tV(function(r,n){n.add(t.schedule(function(){return r.subscribe(n)},e))})}function tB(t,e){if(!t)throw Error("Iterable cannot be null");return new tb(function(r){tD(r,e,function(){var n=t[Symbol.asyncIterator]();tD(r,e,function(){n.next().then(function(t){t.done?r.complete():r.next(t.value)})},0,!0)})})}function t_(t,e){return e?function(t,e){if(null!=t){if(H(t[tg]))return tR(t).pipe(tL(e),t$(e));if(tx(t))return new tb(function(r){var n=0;return e.schedule(function(){n===t.length?r.complete():(r.next(t[n++]),r.closed||this.schedule())})});if(tE(t))return tR(t).pipe(tL(e),t$(e));if(tT(t))return tB(t,e);if(tS(t))return new tb(function(r){var n;return tD(r,e,function(){n=t[tA](),tD(r,e,function(){var t,e,i;try{e=(t=n.next()).value,i=t.done}catch(t){r.error(t);return}i?r.complete():r.next(e)},0,!0)}),function(){return H(null==n?void 0:n.return)&&n.return()}});if(tP(t))return tB(tk(t),e)}throw tC(t)}(t,e):tR(t)}function tU(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=tI(t);return t_(t,r)}function tz(t,e){return tV(function(r,n){var i=0;r.subscribe(tq(n,function(r){n.next(t.call(e,r,i++))}))})}function tN(t,e,r){return(void 0===r&&(r=1/0),H(e))?tN(function(r,n){return tz(function(t,i){return e(r,t,n,i)})(tR(t(r,n)))},r):("number"==typeof e&&(r=e),tV(function(e,n){var i,o,s,a,l,u,c,h,d;return i=r,s=[],a=0,l=0,u=!1,c=function(){!u||s.length||a||n.complete()},h=function(t){return a<i?d(t):s.push(t)},d=function(e){a++;var r=!1;tR(t(e,l++)).subscribe(tq(n,function(t){o?h(t):n.next(t)},function(){r=!0},void 0,function(){if(r)try{for(a--;s.length&&a<i;)!function(){var t=s.shift();d(t)}();c()}catch(t){n.error(t)}}))},e.subscribe(tq(n,h,function(){u=!0,c()})),function(){}}))}var tW=Y(function(t){return function(){t(this),this.name="EmptyError",this.message="no elements in sequence"}});function tH(t,e){var r="object"==typeof e;return new Promise(function(n,i){var o,s=!1;t.subscribe({next:function(t){o=t,s=!0},error:i,complete:function(){s?n(o):r?n(e.defaultValue):i(new tW)}})})}var tY=Y(function(t){return function(){t(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}}),tG=function(t){function e(){var e=t.call(this)||this;return e.closed=!1,e.currentObservers=null,e.observers=[],e.isStopped=!1,e.hasError=!1,e.thrownError=null,e}return B(e,t),e.prototype.lift=function(t){var e=new tX(this,this);return e.operator=t,e},e.prototype._throwIfClosed=function(){if(this.closed)throw new tY},e.prototype.next=function(t){var e=this;ta(function(){var r,n;if(e._throwIfClosed(),!e.isStopped){e.currentObservers||(e.currentObservers=Array.from(e.observers));try{for(var i=U(e.currentObservers),o=i.next();!o.done;o=i.next())o.value.next(t)}catch(t){r={error:t}}finally{try{o&&!o.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}}})},e.prototype.error=function(t){var e=this;ta(function(){if(e._throwIfClosed(),!e.isStopped){e.hasError=e.isStopped=!0,e.thrownError=t;for(var r=e.observers;r.length;)r.shift().error(t)}})},e.prototype.complete=function(){var t=this;ta(function(){if(t._throwIfClosed(),!t.isStopped){t.isStopped=!0;for(var e=t.observers;e.length;)e.shift().complete()}})},e.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(e.prototype,"observed",{get:function(){var t;return(null==(t=this.observers)?void 0:t.length)>0},enumerable:!1,configurable:!0}),e.prototype._trySubscribe=function(e){return this._throwIfClosed(),t.prototype._trySubscribe.call(this,e)},e.prototype._subscribe=function(t){return this._throwIfClosed(),this._checkFinalizedStatuses(t),this._innerSubscribe(t)},e.prototype._innerSubscribe=function(t){var e=this,r=this.hasError,n=this.isStopped,i=this.observers;return r||n?Z:(this.currentObservers=null,i.push(t),new K(function(){e.currentObservers=null,X(i,t)}))},e.prototype._checkFinalizedStatuses=function(t){var e=this.hasError,r=this.thrownError,n=this.isStopped;e?t.error(r):n&&t.complete()},e.prototype.asObservable=function(){var t=new tb;return t.source=this,t},e.create=function(t,e){return new tX(t,e)},e}(tb),tX=function(t){function e(e,r){var n=t.call(this)||this;return n.destination=e,n.source=r,n}return B(e,t),e.prototype.next=function(t){var e,r;null==(r=null==(e=this.destination)?void 0:e.next)||r.call(e,t)},e.prototype.error=function(t){var e,r;null==(r=null==(e=this.destination)?void 0:e.error)||r.call(e,t)},e.prototype.complete=function(){var t,e;null==(e=null==(t=this.destination)?void 0:t.complete)||e.call(t)},e.prototype._subscribe=function(t){var e,r;return null!=(r=null==(e=this.source)?void 0:e.subscribe(t))?r:Z},e}(tG),tK={now:function(){return(tK.delegate||Date).now()},delegate:void 0},tZ=function(t){function e(e,r,n){void 0===e&&(e=1/0),void 0===r&&(r=1/0),void 0===n&&(n=tK);var i=t.call(this)||this;return i._bufferSize=e,i._windowTime=r,i._timestampProvider=n,i._buffer=[],i._infiniteTimeWindow=!0,i._infiniteTimeWindow=r===1/0,i._bufferSize=Math.max(1,e),i._windowTime=Math.max(1,r),i}return B(e,t),e.prototype.next=function(e){var r=this.isStopped,n=this._buffer,i=this._infiniteTimeWindow,o=this._timestampProvider,s=this._windowTime;!r&&(n.push(e),i||n.push(o.now()+s)),this._trimBuffer(),t.prototype.next.call(this,e)},e.prototype._subscribe=function(t){this._throwIfClosed(),this._trimBuffer();for(var e=this._innerSubscribe(t),r=this._infiniteTimeWindow,n=this._buffer.slice(),i=0;i<n.length&&!t.closed;i+=r?1:2)t.next(n[i]);return this._checkFinalizedStatuses(t),e},e.prototype._trimBuffer=function(){var t=this._bufferSize,e=this._timestampProvider,r=this._buffer,n=this._infiniteTimeWindow,i=(n?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!n){for(var o=e.now(),s=0,a=1;a<r.length&&r[a]<=o;a+=2)s=a;s&&r.splice(0,s+1)}},e}(tG);function tJ(t){void 0===t&&(t={});var e=t.connector,r=void 0===e?function(){return new tG}:e,n=t.resetOnError,i=void 0===n||n,o=t.resetOnComplete,s=void 0===o||o,a=t.resetOnRefCountZero,l=void 0===a||a;return function(t){var e,n,o,a=0,u=!1,c=!1,h=function(){null==n||n.unsubscribe(),n=void 0},d=function(){h(),e=o=void 0,u=c=!1},p=function(){var t=e;d(),null==t||t.unsubscribe()};return tV(function(t,f){a++,c||u||h();var m=o=null!=o?o:r();f.add(function(){0!=--a||c||u||(n=tQ(p,l))}),m.subscribe(f),!e&&a>0&&(e=new td({next:function(t){return m.next(t)},error:function(t){c=!0,h(),n=tQ(d,i,t),m.error(t)},complete:function(){u=!0,h(),n=tQ(d,s),m.complete()}}),tR(t).subscribe(e))})(t)}}function tQ(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];if(!0===e)return void t();if(!1!==e){var i=new td({next:function(){i.unsubscribe(),t()}});return tR(e.apply(void 0,N([],z(r)))).subscribe(i)}}function t0(t){return tV(function(e,r){var n,i=null,o=!1;i=e.subscribe(tq(r,void 0,void 0,function(s){n=tR(t(s,t0(t)(e))),i?(i.unsubscribe(),i=null,n.subscribe(r)):o=!0})),o&&(i.unsubscribe(),i=null,n.subscribe(r))})}function t1(t){return void 0===t&&(t=1/0),tN(ty,t)}function t2(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return t1(1)(t_(t,tI(t)))}var t3=function(t){function e(e,r){return t.call(this)||this}return B(e,t),e.prototype.schedule=function(t,e){return void 0===e&&(e=0),this},e}(K),t5={setInterval:function(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var i=t5.delegate;return(null==i?void 0:i.setInterval)?i.setInterval.apply(i,N([t,e],z(r))):setInterval.apply(void 0,N([t,e],z(r)))},clearInterval:function(t){var e=t5.delegate;return((null==e?void 0:e.clearInterval)||clearInterval)(t)},delegate:void 0},t6=function(t){function e(e,r){var n=t.call(this,e,r)||this;return n.scheduler=e,n.work=r,n.pending=!1,n}return B(e,t),e.prototype.schedule=function(t,e){if(void 0===e&&(e=0),this.closed)return this;this.state=t;var r,n=this.id,i=this.scheduler;return null!=n&&(this.id=this.recycleAsyncId(i,n,e)),this.pending=!0,this.delay=e,this.id=null!=(r=this.id)?r:this.requestAsyncId(i,this.id,e),this},e.prototype.requestAsyncId=function(t,e,r){return void 0===r&&(r=0),t5.setInterval(t.flush.bind(t,this),r)},e.prototype.recycleAsyncId=function(t,e,r){if(void 0===r&&(r=0),null!=r&&this.delay===r&&!1===this.pending)return e;null!=e&&t5.clearInterval(e)},e.prototype.execute=function(t,e){if(this.closed)return Error("executing a cancelled action");this.pending=!1;var r=this._execute(t,e);if(r)return r;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},e.prototype._execute=function(t,e){var r,n=!1;try{this.work(t)}catch(t){n=!0,r=t||Error("Scheduled action threw falsy error")}if(n)return this.unsubscribe(),r},e.prototype.unsubscribe=function(){if(!this.closed){var e=this.id,r=this.scheduler,n=r.actions;this.work=this.state=this.scheduler=null,this.pending=!1,X(n,this),null!=e&&(this.id=this.recycleAsyncId(r,e,null)),this.delay=null,t.prototype.unsubscribe.call(this)}},e}(t3),t8=function(){function t(e,r){void 0===r&&(r=t.now),this.schedulerActionCtor=e,this.now=r}return t.prototype.schedule=function(t,e,r){return void 0===e&&(e=0),new this.schedulerActionCtor(this,t).schedule(r,e)},t.now=tK.now,t}(),t4=new(function(t){function e(e,r){void 0===r&&(r=t8.now);var n=t.call(this,e,r)||this;return n.actions=[],n._active=!1,n}return B(e,t),e.prototype.flush=function(t){var e,r=this.actions;if(this._active)return void r.push(t);this._active=!0;do if(e=t.execute(t.state,t.delay))break;while(t=r.shift());if(this._active=!1,e){for(;t=r.shift();)t.unsubscribe();throw e}},e}(t8))(t6);function t9(t,e){var r=H(t)?t:function(){return t},n=function(t){return t.error(r())};return new tb(e?function(t){return e.schedule(n,0,t)}:n)}function t7(t){return tV(function(e,r){try{e.subscribe(r)}finally{r.add(t)}})}var et=new tb(function(t){return t.complete()});function ee(t,e){var r="object"==typeof e;return new Promise(function(n,i){var o=new td({next:function(t){n(t),o.unsubscribe()},error:i,complete:function(){r?n(e.defaultValue):i(new tW)}});t.subscribe(o)})}var er=r(1501),en=Array.isArray,ei=Array.isArray;function eo(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=H(tO(t))?t.pop():void 0;return r?function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return tv(t)}(eo.apply(void 0,N([],z(t))),tz(function(t){return ei(t)?r.apply(void 0,N([],z(t))):r(t)})):tV(function(e,r){var n,i,o;(n=N([e],z(1===t.length&&en(t[0])?t[0]:t)),void 0===o&&(o=ty),function(t){var e,r,s;e=void 0,r=function(){for(var e=n.length,r=Array(e),s=e,a=e,l=function(e){var l,u,c;l=i,u=function(){var l=t_(n[e],i),u=!1;l.subscribe(tq(t,function(n){r[e]=n,!u&&(u=!0,a--),a||t.next(o(r.slice()))},function(){--s||t.complete()}))},c=t,l?tD(c,l,u):u()},u=0;u<e;u++)l(u)},s=t,e?tD(s,e,r):r()})(r)})}function es(t,e){return tV(function(r,n){var i=0;r.subscribe(tq(n,function(r){return t.call(e,r,i++)&&n.next(r)}))})}let ea=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,el=/_key\s*==\s*['"](.*)['"]/,eu=/^\d*:\d*$/;function ec(t){return"string"==typeof t?el.test(t.trim()):"object"==typeof t&&"_key"in t}function eh(t){var e;return"number"==typeof(e=t)||"string"==typeof e&&/^\[\d+\]$/.test(e)?Number(t.replace(/[^\d]/g,"")):ec(t)?{_key:t.match(el)[1]}:!function(t){if("string"==typeof t&&eu.test(t))return!0;if(!Array.isArray(t)||2!==t.length)return!1;let[e,r]=t;return("number"==typeof e||""===e)&&("number"==typeof r||""===r)}(t)?t:function(t){let[e,r]=t.split(":").map(t=>""===t?t:Number(t));return[e,r]}(t)}let ed="drafts.",ep="versions.";function ef(t){return t.startsWith(ed)}function em(t){return t.startsWith(ep)}function eg(t,e){if("drafts"===e||"published"===e)throw Error('Version can not be "published" or "drafts"');return`${ep}${e}.${ev(t)}`}function ey(t){if(!em(t))return;let[e,r,...n]=t.split(".");return r}function ev(t){return em(t)?t.split(".").slice(2).join("."):ef(t)?t.slice(ed.length):t}let eb=t=>crypto.getRandomValues(new Uint8Array(t)),ew=(t,e,r)=>{let n=(2<<Math.log(t.length-1)/Math.LN2)-1,i=-~(1.6*n*e/t.length);return (o=e)=>{let s="";for(;;){let e=r(i),a=0|i;for(;a--;)if((s+=t[e[a]&n]||"").length===o)return s}}},ex=/\r\n|[\n\r\u2028\u2029]/;function eE(t,e){let r=0;for(let n=0;n<e.length;n++){let i=e[n].length+1;if(r+i>t)return{line:n+1,column:t-r};r+=i}return{line:e.length,column:e[e.length-1]?.length??0}}class eT extends Error{response;statusCode=400;responseBody;details;constructor(t,e){let r=eA(t,e);super(r.message),Object.assign(this,r)}}class eC extends Error{response;statusCode=500;responseBody;details;constructor(t){let e=eA(t);super(e.message),Object.assign(this,e)}}function eA(t,e){var r,n,i;let o=t.body,s={response:t,statusCode:t.statusCode,responseBody:(r=o,-1!==(t.headers["content-type"]||"").toLowerCase().indexOf("application/json")?JSON.stringify(r,null,2):r),message:"",details:void 0};if(!(0,er.u4)(o))return s.message=eP(t,o),s;let a=o.error;if("string"==typeof a&&"string"==typeof o.message)return s.message=`${a} - ${o.message}`,s;if("object"!=typeof a||null===a)return"string"==typeof a?s.message=a:"string"==typeof o.message?s.message=o.message:s.message=eP(t,o),s;if("type"in(n=a)&&"mutationError"===n.type&&"description"in n&&"string"==typeof n.description||"type"in(i=a)&&"actionError"===i.type&&"description"in i&&"string"==typeof i.description){let t=a.items||[],e=t.slice(0,5).map(t=>t.error?.description).filter(Boolean),r=e.length?`:
- ${e.join(`
- `)}`:"";return t.length>5&&(r+=`
...and ${t.length-5} more`),s.message=`${a.description}${r}`,s.details=o.error,s}return eS(a)?(s.message=ek(a,e?.options?.query?.tag),s.details=o.error):"description"in a&&"string"==typeof a.description?(s.message=a.description,s.details=a):s.message=eP(t,o),s}function eS(t){return(0,er.u4)(t)&&"queryParseError"===t.type&&"string"==typeof t.query&&"number"==typeof t.start&&"number"==typeof t.end}function ek(t,e){let{query:r,start:n,end:i,description:o}=t;if(!r||typeof n>"u")return`GROQ query parse error: ${o}`;let s=e?`

Tag: ${e}`:"";return`GROQ query parse error:
${function(t,e,r){let n=t.split(ex),{start:i,end:o,markerLines:s}=function(t,e){let r={...t.start},n={...r,...t.end},i=r.line??-1,o=r.column??0,s=n.line,a=n.column,l=Math.max(i-3,0),u=Math.min(e.length,s+3);-1===i&&(l=0),-1===s&&(u=e.length);let c=s-i,h={};if(c)for(let t=0;t<=c;t++){let r=t+i;if(o)if(0===t){let t=e[r-1].length;h[r]=[o,t-o+1]}else if(t===c)h[r]=[0,a];else{let n=e[r-t].length;h[r]=[0,n]}else h[r]=!0}else o===a?o?h[i]=[o,0]:h[i]=!0:h[i]=[o,a-o];return{start:l,end:u,markerLines:h}}({start:eE(e.start,n),end:e.end?eE(e.end,n):void 0},n),a=`${o}`.length;return t.split(ex,o).slice(i,o).map((t,e)=>{let n=i+1+e,o=` ${` ${n}`.slice(-a)} |`,l=s[n],u=!s[n+1];if(!l)return` ${o}${t.length>0?` ${t}`:""}`;let c="";if(Array.isArray(l)){let e=t.slice(0,Math.max(l[0]-1,0)).replace(/[^\t]/g," "),n=l[1]||1;c=[`
 `,o.replace(/\d/g," ")," ",e,"^".repeat(n)].join(""),u&&r&&(c+=" "+r)}return[">",o,t.length>0?` ${t}`:"",c].join("")}).join(`
`)}(r,{start:n,end:i},o)}${s}`}function eP(t,e){var r,n;let i="string"==typeof e?` (${n=100,(r=e).length>100?`${r.slice(0,n)}\u2026`:r})`:"",o=t.statusMessage?` ${t.statusMessage}`:"";return`${t.method}-request to ${t.url} resulted in HTTP ${t.statusCode}${o}${i}`}class eR extends Error{projectId;addOriginUrl;constructor({projectId:t}){super("CorsOriginError"),this.name="CorsOriginError",this.projectId=t;let e=new URL(`https://sanity.io/manage/project/${t}/api`);if("u">typeof location){let{origin:t}=location;e.searchParams.set("cors","add"),e.searchParams.set("origin",t),this.addOriginUrl=e,this.message=`The current origin is not allowed to connect to the Live Content API. Add it here: ${e}`}else this.message=`The current origin is not allowed to connect to the Live Content API. Change your configuration here: ${e}`}}let eM={onResponse:(t,e)=>{if(t.statusCode>=500)throw new eC(t);if(t.statusCode>=400)throw new eT(t,e);return t}};function ej(t){return b([$({shouldRetry:eO}),...t,function(){let t={};return{onResponse:e=>{let r=e.headers["x-sanity-warning"];for(let e of Array.isArray(r)?r:[r])!e||t[e]||(t[e]=!0,console.warn(e));return e}}}(),{processOptions:t=>{let e=t.body;return!e||"function"==typeof e.pipe||P(e)||-1===M.indexOf(typeof e)&&!Array.isArray(e)&&!function(t){if(!1===R(t))return!1;let e=t.constructor;if(void 0===e)return!0;let r=e.prototype;return!1!==R(r)&&!1!==r.hasOwnProperty("isPrototypeOf")}(e)?t:Object.assign({},t,{body:JSON.stringify(t.body),headers:Object.assign({},t.headers,{"Content-Type":"application/json"})})}},{onResponse:t=>{let e=t.headers["content-type"]||"",r=-1!==e.indexOf("application/json");return t.body&&e&&r?Object.assign({},t,{body:function(t){try{return JSON.parse(t)}catch(t){throw t.message=`Failed to parsed response body as JSON: ${t.message}`,t}}(t.body)}):t},processOptions:t=>Object.assign({},t,{headers:Object.assign({Accept:"application/json"},t.headers)})},{onRequest:t=>{if("xhr"!==t.adapter)return;let e=t.request,r=t.context;function n(t){return e=>{let n=e.lengthComputable?e.loaded/e.total*100:-1;r.channels.progress.publish({stage:t,percent:n,total:e.total,loaded:e.loaded,lengthComputable:e.lengthComputable})}}"upload"in e&&"onprogress"in e.upload&&(e.upload.onprogress=n("upload")),"onprogress"in e&&(e.onprogress=n("download"))}},eM,function(t={}){let e=t.implementation||O.Observable;if(!e)throw Error("`Observable` is not available in global scope, and no implementation was passed");return{onReturn:(t,r)=>new e(e=>(t.error.subscribe(t=>e.error(t)),t.progress.subscribe(t=>e.next(Object.assign({type:"progress"},t))),t.response.subscribe(t=>{e.next(Object.assign({type:"response"},t)),e.complete()}),t.request.publish(r),()=>t.abort.publish()))}}({implementation:tb})])}function eO(t,e,r){if(0===r.maxRetries)return!1;let n="GET"===r.method||"HEAD"===r.method,i=(r.uri||r.url).startsWith("/data/query"),o=t.response&&(429===t.response.statusCode||502===t.response.statusCode||503===t.response.statusCode);return(!!n||!!i)&&!!o||$.shouldRetry(t,e,r)}function eI(t){return"https://www.sanity.io/help/"+t}let eD=["image","file"],eV=["before","after","replace"],eq=t=>{if(!/^(~[a-z0-9]{1}[-\w]{0,63}|[a-z0-9]{1}[-\w]{0,63})$/.test(t))throw Error("Datasets can only contain lowercase characters, numbers, underscores and dashes, and start with tilde, and be maximum 64 characters")},eF=t=>{if(!/^[-a-z0-9]+$/i.test(t))throw Error("`projectId` can only contain only a-z, 0-9 and dashes")},e$=t=>{if(-1===eD.indexOf(t))throw Error(`Invalid asset type: ${t}. Must be one of ${eD.join(", ")}`)},eL=(t,e)=>{if(null===e||"object"!=typeof e||Array.isArray(e))throw Error(`${t}() takes an object of properties`)},eB=(t,e)=>{if("string"!=typeof e||!/^[a-z0-9_][a-z0-9_.-]{0,127}$/i.test(e)||e.includes(".."))throw Error(`${t}(): "${e}" is not a valid document ID`)},e_=(t,e)=>{if(!e._id)throw Error(`${t}() requires that the document contains an ID ("_id" property)`);eB(t,e._id)},eU=(t,e)=>{if("string"!=typeof e)throw Error(`\`${t}()\`: \`${e}\` is not a valid document type`)},ez=(t,e)=>{if(!e._type)throw Error(`\`${t}()\` requires that the document contains a type (\`_type\` property)`);eU(t,e._type)},eN=(t,e)=>{if(e._id&&e._id!==t)throw Error(`The provided document ID (\`${e._id}\`) does not match the generated version ID (\`${t}\`)`)},eW=(t,e,r)=>{let n="insert(at, selector, items)";if(-1===eV.indexOf(t)){let t=eV.map(t=>`"${t}"`).join(", ");throw Error(`${n} takes an "at"-argument which is one of: ${t}`)}if("string"!=typeof e)throw Error(`${n} takes a "selector"-argument which must be a string`);if(!Array.isArray(r))throw Error(`${n} takes an "items"-argument which must be an array`)},eH=t=>{if(!t.dataset)throw Error("`dataset` must be provided to perform queries");return t.dataset||""},eY=t=>{if("string"!=typeof t||!/^[a-z0-9._-]{1,75}$/i.test(t))throw Error("Tag can only contain alphanumeric characters, underscores, dashes and dots, and be between one and 75 characters long.");return t},eG=t=>{if(!t["~experimental_resource"])throw Error("`resource` must be provided to perform resource queries");let{type:e,id:r}=t["~experimental_resource"];switch(e){case"dataset":if(2!==r.split(".").length)throw Error('Dataset resource ID must be in the format "project.dataset"');return;case"dashboard":case"media-library":case"canvas":return;default:throw Error(`Unsupported resource type: ${e.toString()}`)}},eX=(t,e)=>{if(e["~experimental_resource"])throw Error(`\`${t}\` does not support resource-based operations`)},eK=t=>(function(t){let e=!1,r;return(...n)=>(e||(r=t(...n),e=!0),r)})((...e)=>console.warn(t.join(" "),...e)),eZ=eK(["Because you set `withCredentials` to true, we will override your `useCdn`","setting to be false since (cookie-based) credentials are never set on the CDN"]),eJ=eK(["Since you haven't set a value for `useCdn`, we will deliver content using our","global, edge-cached API-CDN. If you wish to have content delivered faster, set","`useCdn: false` to use the Live API. Note: You may incur higher costs using the live API."]),eQ=eK(["The Sanity client is configured with the `perspective` set to `drafts` or `previewDrafts`, which doesn't support the API-CDN.","The Live API will be used instead. Set `useCdn: false` in your configuration to hide this warning."]),e0=eK(["The `previewDrafts` perspective has been renamed to  `drafts` and will be removed in a future API version"]),e1=eK(["You have configured Sanity client to use a token in the browser. This may cause unintentional security issues.",`See ${eI("js-client-browser-token")} for more information and how to hide this warning.`]),e2=eK(["You have configured Sanity client to use a token, but also provided `withCredentials: true`.","This is no longer supported - only token will be used - remove `withCredentials: true`."]),e3=eK(["Using the Sanity client without specifying an API version is deprecated.",`See ${eI("js-client-api-version")}`]),e5=(eK(["The default export of @sanity/client has been deprecated. Use the named export `createClient` instead."]),{apiHost:"https://api.sanity.io",apiVersion:"1",useProjectHostname:!0,stega:{enabled:!1}}),e6=["localhost","127.0.0.1","0.0.0.0"],e8=t=>-1!==e6.indexOf(t);function e4(t){if(Array.isArray(t)&&t.length>1&&t.includes("raw"))throw TypeError('Invalid API perspective value: "raw". The raw-perspective can not be combined with other perspectives')}let e9=(t,e)=>{let r={...e,...t,stega:{..."boolean"==typeof e.stega?{enabled:e.stega}:e.stega||e5.stega,..."boolean"==typeof t.stega?{enabled:t.stega}:t.stega||{}}};r.apiVersion||e3();let n={...e5,...r},i=n.useProjectHostname&&!n["~experimental_resource"];if(typeof Promise>"u"){let t=eI("js-client-promise-polyfill");throw Error(`No native Promise-implementation found, polyfill needed - see ${t}`)}if(i&&!n.projectId)throw Error("Configuration must contain `projectId`");if(n["~experimental_resource"]&&eG(n),"u">typeof n.perspective&&e4(n.perspective),"encodeSourceMap"in n)throw Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMap' is not supported in '@sanity/client'. Did you mean 'stega.enabled'?");if("encodeSourceMapAtPath"in n)throw Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMapAtPath' is not supported in '@sanity/client'. Did you mean 'stega.filter'?");if("boolean"!=typeof n.stega.enabled)throw Error(`stega.enabled must be a boolean, received ${n.stega.enabled}`);if(n.stega.enabled&&void 0===n.stega.studioUrl)throw Error("stega.studioUrl must be defined when stega.enabled is true");if(n.stega.enabled&&"string"!=typeof n.stega.studioUrl&&"function"!=typeof n.stega.studioUrl)throw Error(`stega.studioUrl must be a string or a function, received ${n.stega.studioUrl}`);let o="u">typeof window&&window.location&&window.location.hostname,s=o&&e8(window.location.hostname),a=!!n.token;n.withCredentials&&a&&(e2(),n.withCredentials=!1),o&&s&&a&&!0!==n.ignoreBrowserTokenWarning?e1():typeof n.useCdn>"u"&&eJ(),i&&eF(n.projectId),n.dataset&&eq(n.dataset),"requestTagPrefix"in n&&(n.requestTagPrefix=n.requestTagPrefix?eY(n.requestTagPrefix).replace(/\.+$/,""):void 0),n.apiVersion=`${n.apiVersion}`.replace(/^v/,""),n.isDefaultApi=n.apiHost===e5.apiHost,!0===n.useCdn&&n.withCredentials&&eZ(),n.useCdn=!1!==n.useCdn&&!n.withCredentials,function(t){if("1"===t||"X"===t)return;let e=new Date(t);if(!(/^\d{4}-\d{2}-\d{2}$/.test(t)&&e instanceof Date&&e.getTime()>0))throw Error("Invalid API version string, expected `1` or date in format `YYYY-MM-DD`")}(n.apiVersion);let l=n.apiHost.split("://",2),u=l[0],c=l[1],h=n.isDefaultApi?"apicdn.sanity.io":c;return i?(n.url=`${u}://${n.projectId}.${c}/v${n.apiVersion}`,n.cdnUrl=`${u}://${n.projectId}.${h}/v${n.apiVersion}`):(n.url=`${n.apiHost}/v${n.apiVersion}`,n.cdnUrl=n.url),n};class e7 extends Error{name="ConnectionFailedError"}class rt extends Error{name="DisconnectError";reason;constructor(t,e,r={}){super(t,r),this.reason=e}}class re extends Error{name="ChannelError";data;constructor(t,e){super(t),this.data=e}}class rr extends Error{name="MessageError";data;constructor(t,e,r={}){super(t,r),this.data=e}}class rn extends Error{name="MessageParseError"}let ri=["channelError","disconnect"];function ro(t,e){return tj(()=>{let e=t();return e&&(e instanceof tb||H(e.lift)&&H(e.subscribe))?e:tU(e)}).pipe(tN(t=>{var r,n;return r=t,n=e,new tb(t=>{let e=n.includes("open"),i=n.includes("reconnect");function o(e){if("data"in e){let[r,n]=rs(e);t.error(r?new rn("Unable to parse EventSource error message",{cause:n}):new rr((n?.data).message,n));return}r.readyState===r.CLOSED?t.error(new e7("EventSource connection failed")):i&&t.next({type:"reconnect"})}function s(){t.next({type:"open"})}function a(e){let[n,i]=rs(e);if(n)return void t.error(new rn("Unable to parse EventSource message",{cause:n}));if("channelError"===e.type){let e=new URL(r.url).searchParams.get("tag");t.error(new re(function(t,e){let r=t.error;return r?eS(r)?ek(r,e):r.description?r.description:"string"==typeof r?r:JSON.stringify(r,null,2):t.message||"Unknown listener error"}(i?.data,e),i.data));return}if("disconnect"===e.type)return void t.error(new rt(`Server disconnected client: ${i.data?.reason||"unknown error"}`));t.next({type:e.type,id:e.lastEventId,...i.data?{data:i.data}:{}})}r.addEventListener("error",o),e&&r.addEventListener("open",s);let l=[...new Set([...ri,...n])].filter(t=>"error"!==t&&"open"!==t&&"reconnect"!==t);return l.forEach(t=>r.addEventListener(t,a)),()=>{r.removeEventListener("error",o),e&&r.removeEventListener("open",s),l.forEach(t=>r.removeEventListener(t,a)),r.close()}})}))}function rs(t){try{let e="string"==typeof t.data&&JSON.parse(t.data);return[null,{type:t.type,id:t.lastEventId,...!function(t){for(let e in t)return!1;return!0}(e)?{data:e}:{}}]}catch(t){return[t,null]}}function ra(t){if("string"==typeof t)return{id:t};if(Array.isArray(t))return{query:"*[_id in $ids]",params:{ids:t}};if("object"==typeof t&&null!==t&&"query"in t&&"string"==typeof t.query)return"params"in t&&"object"==typeof t.params&&null!==t.params?{query:t.query,params:t.params}:{query:t.query};let e=["* Document ID (<docId>)","* Array of document IDs","* Object containing `query`"].join(`
`);throw Error(`Unknown selection - must be one of:

${e}`)}class rl{selection;operations;constructor(t,e={}){this.selection=t,this.operations=e}set(t){return this._assign("set",t)}setIfMissing(t){return this._assign("setIfMissing",t)}diffMatchPatch(t){return eL("diffMatchPatch",t),this._assign("diffMatchPatch",t)}unset(t){if(!Array.isArray(t))throw Error("unset(attrs) takes an array of attributes to unset, non-array given");return this.operations=Object.assign({},this.operations,{unset:t}),this}inc(t){return this._assign("inc",t)}dec(t){return this._assign("dec",t)}insert(t,e,r){return eW(t,e,r),this._assign("insert",{[t]:e,items:r})}append(t,e){return this.insert("after",`${t}[-1]`,e)}prepend(t,e){return this.insert("before",`${t}[0]`,e)}splice(t,e,r,n){let i=e<0?e-1:e,o=typeof r>"u"||-1===r?-1:Math.max(0,e+r),s=`${t}[${i}:${i<0&&o>=0?"":o}]`;return this.insert("replace",s,n||[])}ifRevisionId(t){return this.operations.ifRevisionID=t,this}serialize(){return{...ra(this.selection),...this.operations}}toJSON(){return this.serialize()}reset(){return this.operations={},this}_assign(t,e,r=!0){return eL(t,e),this.operations=Object.assign({},this.operations,{[t]:Object.assign({},r&&this.operations[t]||{},e)}),this}_set(t,e){return this._assign(t,e,!1)}}class ru extends rl{#a;constructor(t,e,r){super(t,e),this.#a=r}clone(){return new ru(this.selection,{...this.operations},this.#a)}commit(t){if(!this.#a)throw Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");let e=Object.assign({returnFirst:"string"==typeof this.selection,returnDocuments:!0},t);return this.#a.mutate({patch:this.serialize()},e)}}class rc extends rl{#a;constructor(t,e,r){super(t,e),this.#a=r}clone(){return new rc(this.selection,{...this.operations},this.#a)}commit(t){if(!this.#a)throw Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");let e=Object.assign({returnFirst:"string"==typeof this.selection,returnDocuments:!0},t);return this.#a.mutate({patch:this.serialize()},e)}}let rh={returnDocuments:!1};class rd{operations;trxId;constructor(t=[],e){this.operations=t,this.trxId=e}create(t){return eL("create",t),this._add({create:t})}createIfNotExists(t){let e="createIfNotExists";return eL(e,t),e_(e,t),this._add({[e]:t})}createOrReplace(t){let e="createOrReplace";return eL(e,t),e_(e,t),this._add({[e]:t})}delete(t){return eB("delete",t),this._add({delete:{id:t}})}transactionId(t){return t?(this.trxId=t,this):this.trxId}serialize(){return[...this.operations]}toJSON(){return this.serialize()}reset(){return this.operations=[],this}_add(t){return this.operations.push(t),this}}class rp extends rd{#a;constructor(t,e,r){super(t,r),this.#a=e}clone(){return new rp([...this.operations],this.#a,this.trxId)}commit(t){if(!this.#a)throw Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return this.#a.mutate(this.serialize(),Object.assign({transactionId:this.trxId},rh,t||{}))}patch(t,e){let r="function"==typeof e,n="string"!=typeof t&&t instanceof rc,i="object"==typeof t&&("query"in t||"id"in t);if(n)return this._add({patch:t.serialize()});if(r){let r=e(new rc(t,{},this.#a));if(!(r instanceof rc))throw Error("function passed to `patch()` must return the patch");return this._add({patch:r.serialize()})}if(i){let r=new rc(t,e||{},this.#a);return this._add({patch:r.serialize()})}return this._add({patch:{id:t,...e}})}}class rf extends rd{#a;constructor(t,e,r){super(t,r),this.#a=e}clone(){return new rf([...this.operations],this.#a,this.trxId)}commit(t){if(!this.#a)throw Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return this.#a.mutate(this.serialize(),Object.assign({transactionId:this.trxId},rh,t||{}))}patch(t,e){let r="function"==typeof e;if("string"!=typeof t&&t instanceof ru)return this._add({patch:t.serialize()});if(r){let r=e(new ru(t,{},this.#a));if(!(r instanceof ru))throw Error("function passed to `patch()` must return the patch");return this._add({patch:r.serialize()})}return this._add({patch:{id:t,...e}})}}let rm=({query:t,params:e={},options:r={}})=>{let n=new URLSearchParams,{tag:i,includeMutations:o,returnQuery:s,...a}=r;for(let[r,o]of(i&&n.append("tag",i),n.append("query",t),Object.entries(e)))void 0!==o&&n.append(`$${r}`,JSON.stringify(o));for(let[t,e]of Object.entries(a))e&&n.append(t,`${e}`);return!1===s&&n.append("returnQuery","false"),!1===o&&n.append("includeMutations","false"),`?${n}`},rg=(t,e)=>!1===t?void 0:typeof t>"u"?e:t,ry=(t={})=>({dryRun:t.dryRun,returnIds:!0,returnDocuments:rg(t.returnDocuments,!0),visibility:t.visibility||"sync",autoGenerateArrayKeys:t.autoGenerateArrayKeys,skipCrossDatasetReferenceValidation:t.skipCrossDatasetReferenceValidation}),rv=t=>"response"===t.type,rb=t=>t.body,rw=(t,e)=>t.reduce((t,r)=>(t[e(r)]=r,t),Object.create(null));function rx(t,e,n,i,o={},s={}){let a="stega"in s?{...n||{},..."boolean"==typeof s.stega?{enabled:s.stega}:s.stega||{}}:n,l=a.enabled?(0,er.Q)(o):o,u=!1===s.filterResponse?t=>t:t=>t.result,{cache:c,next:h,...d}={useAbortSignal:"u">typeof s.signal,resultSourceMap:a.enabled?"withKeyArraySelector":s.resultSourceMap,...s,returnQuery:!1===s.filterResponse&&!1!==s.returnQuery},p=rD(t,e,"query",{query:i,params:l},"u">typeof c||"u">typeof h?{...d,fetch:{cache:c,next:h}}:d);return a.enabled?p.pipe(function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return eo.apply(void 0,N([],z(t)))}(t_(r.e(195).then(r.bind(r,4195)).then(function(t){return t.stegaEncodeSourceMap$1}).then(({stegaEncodeSourceMap:t})=>t))),tz(([t,e])=>{let r=e(t.result,t.resultSourceMap,a);return u({...t,result:r})})):p.pipe(tz(u))}function rE(t,e,r,n={}){let i={uri:rW(t,"doc",(()=>{if(!n.releaseId)return r;let t=ey(r);if(!t){if(ef(r))throw Error(`The document ID (\`${r}\`) is a draft, but \`options.releaseId\` is set as \`${n.releaseId}\``);return eg(r,n.releaseId)}if(t!==n.releaseId)throw Error(`The document ID (\`${r}\`) is already a version of \`${t}\` release, but this does not match the provided \`options.releaseId\` (\`${n.releaseId}\`)`);return r})()),json:!0,tag:n.tag,signal:n.signal};return rz(t,e,i).pipe(es(rv),tz(t=>t.body.documents&&t.body.documents[0]))}function rT(t,e,r,n={}){let i={uri:rW(t,"doc",r.join(",")),json:!0,tag:n.tag,signal:n.signal};return rz(t,e,i).pipe(es(rv),tz(t=>{let e=rw(t.body.documents||[],t=>t._id);return r.map(t=>e[t]||null)}))}function rC(t,e,r,n={}){return rD(t,e,"query",{query:"*[sanity::partOfRelease($releaseId)]",params:{releaseId:r}},n)}function rA(t,e,r,n){return e_("createIfNotExists",r),rV(t,e,r,"createIfNotExists",n)}function rS(t,e,r,n){return e_("createOrReplace",r),rV(t,e,r,"createOrReplace",n)}function rk(t,e,r,n,i){return e_("createVersion",r),ez("createVersion",r),rI(t,e,{actionType:"sanity.action.document.version.create",publishedId:n,document:r},i)}function rP(t,e,r,n){return rD(t,e,"mutate",{mutations:[{delete:ra(r)}]},n)}function rR(t,e,r,n=!1,i){return rI(t,e,{actionType:"sanity.action.document.version.discard",versionId:r,purge:n},i)}function rM(t,e,r,n){return e_("replaceVersion",r),ez("replaceVersion",r),rI(t,e,{actionType:"sanity.action.document.version.replace",document:r},n)}function rj(t,e,r,n,i){return rI(t,e,{actionType:"sanity.action.document.version.unpublish",versionId:r,publishedId:n},i)}function rO(t,e,r,n){let i;return rD(t,e,"mutate",{mutations:Array.isArray(i=r instanceof rc||r instanceof ru?{patch:r.serialize()}:r instanceof rp||r instanceof rf?r.serialize():r)?i:[i],transactionId:n&&n.transactionId||void 0},n)}function rI(t,e,r,n){let i=Array.isArray(r)?r:[r],o=n&&n.transactionId||void 0;return rD(t,e,"actions",{actions:i,transactionId:o,skipCrossDatasetReferenceValidation:n&&n.skipCrossDatasetReferenceValidation||void 0,dryRun:n&&n.dryRun||void 0},n)}function rD(t,e,r,n,i={}){let o="mutate"===r,s="actions"===r,a=o||s?"":rm(n),l=!o&&!s&&a.length<11264,u=l?a:"",c=i.returnFirst,{timeout:h,token:d,tag:p,headers:f,returnQuery:m,lastLiveEventId:g,cacheMode:y}=i,v={method:l?"GET":"POST",uri:rW(t,r,u),json:!0,body:l?void 0:n,query:o&&ry(i),timeout:h,headers:f,token:d,tag:p,returnQuery:m,perspective:i.perspective,resultSourceMap:i.resultSourceMap,lastLiveEventId:Array.isArray(g)?g[0]:g,cacheMode:y,canUseCdn:"query"===r,signal:i.signal,fetch:i.fetch,useAbortSignal:i.useAbortSignal,useCdn:i.useCdn};return rz(t,e,v).pipe(es(rv),tz(rb),tz(t=>{if(!o)return t;let e=t.results||[];if(i.returnDocuments)return c?e[0]&&e[0].document:e.map(t=>t.document);let r=c?e[0]&&e[0].id:e.map(t=>t.id);return{transactionId:t.transactionId,results:e,[c?"documentId":"documentIds"]:r}}))}function rV(t,e,r,n,i={}){return rD(t,e,"mutate",{mutations:[{[n]:r}]},Object.assign({returnFirst:!0,returnDocuments:!0},i))}let rq=t=>void 0!==t.config().dataset&&void 0!==t.config().projectId||void 0!==t.config()["~experimental_resource"],rF=(t,e)=>rq(t)&&e.startsWith(rW(t,"query")),r$=(t,e)=>rq(t)&&e.startsWith(rW(t,"mutate")),rL=(t,e)=>rq(t)&&e.startsWith(rW(t,"doc","")),rB=(t,e)=>rq(t)&&e.startsWith(rW(t,"listen")),r_=(t,e)=>rq(t)&&e.startsWith(rW(t,"history","")),rU=(t,e)=>e.startsWith("/data/")||rF(t,e)||r$(t,e)||rL(t,e)||rB(t,e)||r_(t,e);function rz(t,e,r){var n;let i=r.url||r.uri,o=t.config(),s=typeof r.canUseCdn>"u"?["GET","HEAD"].indexOf(r.method||"GET")>=0&&rU(t,i):r.canUseCdn,a=(r.useCdn??o.useCdn)&&s,l=r.tag&&o.requestTagPrefix?[o.requestTagPrefix,r.tag].join("."):r.tag||o.requestTagPrefix;if(l&&null!==r.tag&&(r.query={tag:eY(l),...r.query}),["GET","HEAD","POST"].indexOf(r.method||"GET")>=0&&rF(t,i)){let t=r.resultSourceMap??o.resultSourceMap;void 0!==t&&!1!==t&&(r.query={resultSourceMap:t,...r.query});let e=r.perspective||o.perspective;"u">typeof e&&("previewDrafts"===e&&e0(),e4(e),r.query={perspective:Array.isArray(e)?e.join(","):e,...r.query},(Array.isArray(e)&&e.length>0||"previewDrafts"===e||"drafts"===e)&&a&&(a=!1,eQ())),r.lastLiveEventId&&(r.query={...r.query,lastLiveEventId:r.lastLiveEventId}),!1===r.returnQuery&&(r.query={returnQuery:"false",...r.query}),a&&"noStale"==r.cacheMode&&(r.query={cacheMode:"noStale",...r.query})}let u=function(t,e={}){let r={};t.headers&&Object.assign(r,t.headers);let n=e.token||t.token;n&&(r.Authorization=`Bearer ${n}`),e.useGlobalApi||t.useProjectHostname||!t.projectId||(r["X-Sanity-Project-ID"]=t.projectId);let i=!!(typeof e.withCredentials>"u"?t.withCredentials:e.withCredentials),o=typeof e.timeout>"u"?t.timeout:e.timeout;return Object.assign({},e,{headers:Object.assign({},r,e.headers||{}),timeout:typeof o>"u"?3e5:o,proxy:e.proxy||t.proxy,json:!0,withCredentials:i,fetch:"object"==typeof e.fetch&&"object"==typeof t.fetch?{...t.fetch,...e.fetch}:e.fetch||t.fetch})}(o,Object.assign({},r,{url:rH(t,i,a)})),c=new tb(t=>e(u,o.requester).subscribe(t));return r.signal?c.pipe((n=r.signal,t=>new tb(e=>{let r=()=>e.error(function(t){if(rY)return new DOMException(t?.reason??"The operation was aborted.","AbortError");let e=Error(t?.reason??"The operation was aborted.");return e.name="AbortError",e}(n));if(n&&n.aborted)return void r();let i=t.subscribe(e);return n.addEventListener("abort",r),()=>{n.removeEventListener("abort",r),i.unsubscribe()}}))):c}function rN(t,e,r){return rz(t,e,r).pipe(es(t=>"response"===t.type),tz(t=>t.body))}function rW(t,e,r){let n=t.config();if(n["~experimental_resource"]){eG(n);let t=rG(n),i=void 0!==r?`${e}/${r}`:e;return`${t}/${i}`.replace(/\/($|\?)/,"$1")}let i=eH(n),o=`/${e}/${i}`;return`/data${void 0!==r?`${o}/${r}`:o}`.replace(/\/($|\?)/,"$1")}function rH(t,e,r=!1){let{url:n,cdnUrl:i}=t.config();return`${r?i:n}/${e.replace(/^\//,"")}`}let rY=!!globalThis.DOMException,rG=t=>{if(!t["~experimental_resource"])throw Error("`resource` must be provided to perform resource queries");let{type:e,id:r}=t["~experimental_resource"];switch(e){case"dataset":{let t=r.split(".");if(2!==t.length)throw Error('Dataset ID must be in the format "project.dataset"');return`/projects/${t[0]}/datasets/${t[1]}`}case"canvas":return`/canvases/${r}`;case"media-library":return`/media-libraries/${r}`;case"dashboard":return`/dashboards/${r}`;default:throw Error(`Unsupported resource type: ${e.toString()}`)}};function rX(t,e,r){let n=eH(t.config());return rN(t,e,{method:"POST",uri:`/agent/action/generate/${n}`,body:r})}function rK(t,e,r){let n=eH(t.config());return rN(t,e,{method:"POST",uri:`/agent/action/transform/${n}`,body:r})}function rZ(t,e,r){let n=eH(t.config());return rN(t,e,{method:"POST",uri:`/agent/action/translate/${n}`,body:r})}class rJ{#a;#l;constructor(t,e){this.#a=t,this.#l=e}generate(t){return rX(this.#a,this.#l,t)}transform(t){return rK(this.#a,this.#l,t)}translate(t){return rZ(this.#a,this.#l,t)}}class rQ{#a;#l;constructor(t,e){this.#a=t,this.#l=e}generate(t){return tH(rX(this.#a,this.#l,t))}transform(t){return tH(rK(this.#a,this.#l,t))}translate(t){return tH(rZ(this.#a,this.#l,t))}prompt(t){return tH(function(t,e,r){let n=eH(t.config());return rN(t,e,{method:"POST",uri:`/agent/action/prompt/${n}`,body:r})}(this.#a,this.#l,t))}patch(t){return tH(function(t,e,r){let n=eH(t.config());return rN(t,e,{method:"POST",uri:`/agent/action/patch/${n}`,body:r})}(this.#a,this.#l,t))}}class r0{#a;#l;constructor(t,e){this.#a=t,this.#l=e}upload(t,e,r){return r2(this.#a,this.#l,t,e,r)}}class r1{#a;#l;constructor(t,e){this.#a=t,this.#l=e}upload(t,e,r){return tH(r2(this.#a,this.#l,t,e,r).pipe(es(t=>"response"===t.type),tz(t=>t.body.document)))}}function r2(t,e,r,n,i={}){var o,s;e$(r);let a=i.extract||void 0;a&&!a.length&&(a=["none"]);let l=t.config(),u=(o=i,s=n,!(typeof File>"u")&&s instanceof File?Object.assign({filename:!1===o.preserveFilename?void 0:s.name,contentType:s.type},o):o),{tag:c,label:h,title:d,description:p,creditLine:f,filename:m,source:g}=u,y={label:h,title:d,description:p,filename:m,meta:a,creditLine:f};return g&&(y.sourceId=g.id,y.sourceName=g.name,y.sourceUrl=g.url),rz(t,e,{tag:c,method:"POST",timeout:u.timeout||0,uri:function(t,e){let r="image"===e?"images":"files";if(t["~experimental_resource"]){let{type:e,id:n}=t["~experimental_resource"];switch(e){case"dataset":throw Error("Assets are not supported for dataset resources, yet. Configure the client with `{projectId: <projectId>, dataset: <datasetId>}` instead.");case"canvas":return`/canvases/${n}/assets/${r}`;case"media-library":return`/media-libraries/${n}/upload`;case"dashboard":return`/dashboards/${n}/assets/${r}`;default:throw Error(`Unsupported resource type: ${e.toString()}`)}}let n=eH(t);return`assets/${r}/${n}`}(l,r),headers:u.contentType?{"Content-Type":u.contentType}:{},query:y,body:n})}var r3=(t,e)=>Object.keys(e).concat(Object.keys(t)).reduce((r,n)=>(r[n]=typeof t[n]>"u"?e[n]:t[n],r),{});let r5=(t,e)=>e.reduce((e,r)=>(typeof t[r]>"u"||(e[r]=t[r]),e),{}),r6=tj(()=>r.e(406).then(r.t.bind(r,4406,19))).pipe(tz(({default:t})=>t),function(t,e,r){var n,i,o,s,a=!1;return s=null!=t?t:1/0,tJ({connector:function(){return new tZ(s,e,r)},resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:a})}(1));function r8(){return function(t){return t.pipe(t0((t,e)=>{var r;return t instanceof e7?t2(tU({type:"reconnect"}),(void 0===r&&(r=t4),new tb(function(t){var e=1e3;e<0&&(e=0);var n=0;return r.schedule(function(){t.closed||(t.next(n++),t.complete())},e)})).pipe(tN(()=>e))):t9(()=>t)}))}}let r4=["includePreviousRevision","includeResult","includeMutations","includeAllVersions","visibility","effectFormat","tag"],r9={includeResult:!0};function r7(t,e,r={}){let{url:n,token:i,withCredentials:o,requestTagPrefix:s,headers:a}=this.config(),l=r.tag&&s?[s,r.tag].join("."):r.tag,u={...r3(r,r9),tag:l},c=rm({query:t,params:e,options:{tag:l,...r5(u,r4)}}),h=`${n}${rW(this,"listen",c)}`;if(h.length>14800)return t9(()=>Error("Query too large for listener"));let d=u.events?u.events:["mutation"],p={};return o&&(p.withCredentials=!0),(i||a)&&(p.headers={},i&&(p.headers.Authorization=`Bearer ${i}`),a&&Object.assign(p.headers,a)),ro(()=>(typeof EventSource>"u"||p.headers?r6:tU(EventSource)).pipe(tz(t=>new t(h,p))),d).pipe(r8(),es(t=>d.includes(t.type)),tz(t=>({type:t.type,..."data"in t?t.data:{}})))}let nt="2021-03-25";class ne{#a;constructor(t){this.#a=t}events({includeDrafts:t=!1,tag:e}={}){var r,n,i,o;eX("live",this.#a.config());let{projectId:s,apiVersion:a,token:l,withCredentials:u,requestTagPrefix:c,headers:h}=this.#a.config(),d=a.replace(/^v/,"");if("X"!==d&&d<nt)throw Error(`The live events API requires API version ${nt} or later. The current API version is ${d}. Please update your API version to use this feature.`);if(t&&!l&&!u)throw Error("The live events API requires a token or withCredentials when 'includeDrafts: true'. Please update your client configuration. The token should have the lowest possible access role.");let p=rW(this.#a,"live/events"),f=new URL(this.#a.getUrl(p,!1)),m=e&&c?[c,e].join("."):e;m&&f.searchParams.set("tag",m),t&&f.searchParams.set("includeDrafts","true");let g={};t&&u&&(g.withCredentials=!0),(t&&l||h)&&(g.headers={},t&&l&&(g.headers.Authorization=`Bearer ${l}`),h&&Object.assign(g.headers,h));let y=`${f.href}::${JSON.stringify(g)}`,v=nr.get(y);if(v)return v;let b=ro(()=>(typeof EventSource>"u"||g.headers?r6:tU(EventSource)).pipe(tz(t=>new t(f.href,g))),["message","restart","welcome","reconnect","goaway"]).pipe(r8(),tz(t=>{if("message"===t.type){let{data:e,...r}=t;return{...r,tags:e.tags}}return t})),w=t2((n=f,i={method:"OPTIONS",mode:"cors",credentials:g.withCredentials?"include":"omit",headers:g.headers},new tb(t=>{let e=new AbortController,r=e.signal;return fetch(n,{...i,signal:e.signal}).then(e=>{t.next(e),t.complete()},e=>{r.aborted||t.error(e)}),()=>e.abort()})).pipe(tN(()=>et),t0(()=>{throw new eR({projectId:s})})),b).pipe(t7(()=>nr.delete(y)),(o="function"==typeof(r={predicate:t=>"welcome"===t.type})?{predicate:r,...void 0}:r,t=>{var e,r,n,i;let s,a=!1,{predicate:l,...u}=o;return function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=tI(e),i=(t=1/0,"number"==typeof tO(e)?e.pop():t);return e.length?1===e.length?tR(e[0]):t1(i)(t_(e,n)):et}(t.pipe((i=H(e=t=>{o.predicate(t)&&(a=!0,s=t)})?{next:e,error:r,complete:n}:e)?tV(function(t,e){null==(r=i.subscribe)||r.call(i);var r,n=!0;t.subscribe(tq(e,function(t){var r;null==(r=i.next)||r.call(i,t),e.next(t)},function(){var t;n=!1,null==(t=i.complete)||t.call(i),e.complete()},function(t){var r;n=!1,null==(r=i.error)||r.call(i,t),e.error(t)},function(){var t,e;n&&(null==(t=i.unsubscribe)||t.call(i)),null==(e=i.finalize)||e.call(i)}))}):ty,t7(()=>{a=!1,s=void 0}),tJ(u)),new tb(t=>{a&&t.next(s),t.complete()}))}));return nr.set(y,w),w}}let nr=new Map;class nn{#a;#l;constructor(t,e){this.#a=t,this.#l=e}create(t,e){return no(this.#a,this.#l,"PUT",t,e)}edit(t,e){return no(this.#a,this.#l,"PATCH",t,e)}delete(t){return no(this.#a,this.#l,"DELETE",t)}list(){return rN(this.#a,this.#l,{uri:"/datasets",tag:null})}}class ni{#a;#l;constructor(t,e){this.#a=t,this.#l=e}create(t,e){return eX("dataset",this.#a.config()),tH(no(this.#a,this.#l,"PUT",t,e))}edit(t,e){return eX("dataset",this.#a.config()),tH(no(this.#a,this.#l,"PATCH",t,e))}delete(t){return eX("dataset",this.#a.config()),tH(no(this.#a,this.#l,"DELETE",t))}list(){return eX("dataset",this.#a.config()),tH(rN(this.#a,this.#l,{uri:"/datasets",tag:null}))}}function no(t,e,r,n,i){return eX("dataset",t.config()),eq(n),rN(t,e,{method:r,uri:`/datasets/${n}`,body:i,tag:null})}class ns{#a;#l;constructor(t,e){this.#a=t,this.#l=e}list(t){eX("projects",this.#a.config());let e=t?.includeMembers===!1?"/projects?includeMembers=false":"/projects";return rN(this.#a,this.#l,{uri:e})}getById(t){return eX("projects",this.#a.config()),rN(this.#a,this.#l,{uri:`/projects/${t}`})}}class na{#a;#l;constructor(t,e){this.#a=t,this.#l=e}list(t){eX("projects",this.#a.config());let e=t?.includeMembers===!1?"/projects?includeMembers=false":"/projects";return tH(rN(this.#a,this.#l,{uri:e}))}getById(t){return eX("projects",this.#a.config()),tH(rN(this.#a,this.#l,{uri:`/projects/${t}`}))}}let nl=((t,e=21)=>ew(t,e,eb))("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",8),nu=(t,e)=>e?eg(t,e):function(t){return em(t)?ed+ev(t):ef(t)?t:ed+t}(t);function nc(t,{releaseId:e,publishedId:r,document:n}){if(r&&n._id){let t=nu(r,e);return eN(t,n),t}if(n._id){let r=ef(n._id),i=em(n._id);if(!r&&!i)throw Error(`\`${t}()\` requires a document with an \`_id\` that is a version or draft ID`);if(e){if(r)throw Error(`\`${t}()\` was called with a document ID (\`${n._id}\`) that is a draft ID, but a release ID (\`${e}\`) was also provided.`);let i=ey(n._id);if(i!==e)throw Error(`\`${t}()\` was called with a document ID (\`${n._id}\`) that is a version ID, but the release ID (\`${e}\`) does not match the document's version ID (\`${i}\`).`)}return n._id}if(r)return nu(r,e);throw Error(`\`${t}()\` requires either a publishedId or a document with an \`_id\``)}let nh=(t,e)=>{if("object"==typeof t&&null!==t&&("releaseId"in t||"metadata"in t)){let{releaseId:r=nl(),metadata:n={}}=t;return[r,n,e]}return[nl(),{},t]},nd=(t,e)=>{let[r,n,i]=nh(t,e);return{action:{actionType:"sanity.action.release.create",releaseId:r,metadata:{...n,releaseType:n.releaseType||"undecided"}},options:i}};class np{#a;#l;constructor(t,e){this.#a=t,this.#l=e}get({releaseId:t},e){return rE(this.#a,this.#l,`_.releases.${t}`,e)}create(t,e){let{action:r,options:n}=nd(t,e),{releaseId:i,metadata:o}=r;return rI(this.#a,this.#l,r,n).pipe(tz(t=>({...t,releaseId:i,metadata:o})))}edit({releaseId:t,patch:e},r){return rI(this.#a,this.#l,{actionType:"sanity.action.release.edit",releaseId:t,patch:e},r)}publish({releaseId:t},e){return rI(this.#a,this.#l,{actionType:"sanity.action.release.publish",releaseId:t},e)}archive({releaseId:t},e){return rI(this.#a,this.#l,{actionType:"sanity.action.release.archive",releaseId:t},e)}unarchive({releaseId:t},e){return rI(this.#a,this.#l,{actionType:"sanity.action.release.unarchive",releaseId:t},e)}schedule({releaseId:t,publishAt:e},r){return rI(this.#a,this.#l,{actionType:"sanity.action.release.schedule",releaseId:t,publishAt:e},r)}unschedule({releaseId:t},e){return rI(this.#a,this.#l,{actionType:"sanity.action.release.unschedule",releaseId:t},e)}delete({releaseId:t},e){return rI(this.#a,this.#l,{actionType:"sanity.action.release.delete",releaseId:t},e)}fetchDocuments({releaseId:t},e){return rC(this.#a,this.#l,t,e)}}class nf{#a;#l;constructor(t,e){this.#a=t,this.#l=e}get({releaseId:t},e){return tH(rE(this.#a,this.#l,`_.releases.${t}`,e))}async create(t,e){let{action:r,options:n}=nd(t,e),{releaseId:i,metadata:o}=r;return{...await tH(rI(this.#a,this.#l,r,n)),releaseId:i,metadata:o}}edit({releaseId:t,patch:e},r){return tH(rI(this.#a,this.#l,{actionType:"sanity.action.release.edit",releaseId:t,patch:e},r))}publish({releaseId:t},e){return tH(rI(this.#a,this.#l,{actionType:"sanity.action.release.publish",releaseId:t},e))}archive({releaseId:t},e){return tH(rI(this.#a,this.#l,{actionType:"sanity.action.release.archive",releaseId:t},e))}unarchive({releaseId:t},e){return tH(rI(this.#a,this.#l,{actionType:"sanity.action.release.unarchive",releaseId:t},e))}schedule({releaseId:t,publishAt:e},r){return tH(rI(this.#a,this.#l,{actionType:"sanity.action.release.schedule",releaseId:t,publishAt:e},r))}unschedule({releaseId:t},e){return tH(rI(this.#a,this.#l,{actionType:"sanity.action.release.unschedule",releaseId:t},e))}delete({releaseId:t},e){return tH(rI(this.#a,this.#l,{actionType:"sanity.action.release.delete",releaseId:t},e))}fetchDocuments({releaseId:t},e){return tH(rC(this.#a,this.#l,t,e))}}class nm{#a;#l;constructor(t,e){this.#a=t,this.#l=e}getById(t){return rN(this.#a,this.#l,{uri:`/users/${t}`})}}class ng{#a;#l;constructor(t,e){this.#a=t,this.#l=e}getById(t){return tH(rN(this.#a,this.#l,{uri:`/users/${t}`}))}}class ny{assets;datasets;live;projects;users;agent;releases;#u;#l;listen=r7;constructor(t,e=e5){this.config(e),this.#l=t,this.assets=new r0(this,this.#l),this.datasets=new nn(this,this.#l),this.live=new ne(this),this.projects=new ns(this,this.#l),this.users=new nm(this,this.#l),this.agent={action:new rJ(this,this.#l)},this.releases=new np(this,this.#l)}clone(){return new ny(this.#l,this.config())}config(t){if(void 0===t)return{...this.#u};if(this.#u&&!1===this.#u.allowReconfigure)throw Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.#u=e9(t,this.#u||{}),this}withConfig(t){let e=this.config();return new ny(this.#l,{...e,...t,stega:{...e.stega||{},..."boolean"==typeof t?.stega?{enabled:t.stega}:t?.stega||{}}})}fetch(t,e,r){return rx(this,this.#l,this.#u.stega,t,e,r)}getDocument(t,e){return rE(this,this.#l,t,e)}getDocuments(t,e){return rT(this,this.#l,t,e)}create(t,e){return rV(this,this.#l,t,"create",e)}createIfNotExists(t,e){return rA(this,this.#l,t,e)}createOrReplace(t,e){return rS(this,this.#l,t,e)}createVersion({document:t,publishedId:e,releaseId:r},n){let i=nc("createVersion",{document:t,publishedId:e,releaseId:r}),o={...t,_id:i},s=e||ev(t._id);return rk(this,this.#l,o,s,n)}delete(t,e){return rP(this,this.#l,t,e)}discardVersion({releaseId:t,publishedId:e},r,n){let i=nu(e,t);return rR(this,this.#l,i,r,n)}replaceVersion({document:t,publishedId:e,releaseId:r},n){let i=nc("replaceVersion",{document:t,publishedId:e,releaseId:r}),o={...t,_id:i};return rM(this,this.#l,o,n)}unpublishVersion({releaseId:t,publishedId:e},r){let n=eg(e,t);return rj(this,this.#l,n,e,r)}mutate(t,e){return rO(this,this.#l,t,e)}patch(t,e){return new ru(t,e,this)}transaction(t){return new rf(t,this)}action(t,e){return rI(this,this.#l,t,e)}request(t){return rN(this,this.#l,t)}getUrl(t,e){return rH(this,t,e)}getDataUrl(t,e){return rW(this,t,e)}}class nv{assets;datasets;live;projects;users;agent;releases;observable;#u;#l;listen=r7;constructor(t,e=e5){this.config(e),this.#l=t,this.assets=new r1(this,this.#l),this.datasets=new ni(this,this.#l),this.live=new ne(this),this.projects=new na(this,this.#l),this.users=new ng(this,this.#l),this.agent={action:new rQ(this,this.#l)},this.releases=new nf(this,this.#l),this.observable=new ny(t,e)}clone(){return new nv(this.#l,this.config())}config(t){if(void 0===t)return{...this.#u};if(this.#u&&!1===this.#u.allowReconfigure)throw Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.observable&&this.observable.config(t),this.#u=e9(t,this.#u||{}),this}withConfig(t){let e=this.config();return new nv(this.#l,{...e,...t,stega:{...e.stega||{},..."boolean"==typeof t?.stega?{enabled:t.stega}:t?.stega||{}}})}fetch(t,e,r){return tH(rx(this,this.#l,this.#u.stega,t,e,r))}getDocument(t,e){return tH(rE(this,this.#l,t,e))}getDocuments(t,e){return tH(rT(this,this.#l,t,e))}create(t,e){return tH(rV(this,this.#l,t,"create",e))}createIfNotExists(t,e){return tH(rA(this,this.#l,t,e))}createOrReplace(t,e){return tH(rS(this,this.#l,t,e))}createVersion({document:t,publishedId:e,releaseId:r},n){let i=nc("createVersion",{document:t,publishedId:e,releaseId:r}),o={...t,_id:i},s=e||ev(t._id);return ee(rk(this,this.#l,o,s,n))}delete(t,e){return tH(rP(this,this.#l,t,e))}discardVersion({releaseId:t,publishedId:e},r,n){let i=nu(e,t);return tH(rR(this,this.#l,i,r,n))}replaceVersion({document:t,publishedId:e,releaseId:r},n){let i=nc("replaceVersion",{document:t,publishedId:e,releaseId:r}),o={...t,_id:i};return ee(rM(this,this.#l,o,n))}unpublishVersion({releaseId:t,publishedId:e},r){let n=eg(e,t);return tH(rj(this,this.#l,n,e,r))}mutate(t,e){return tH(rO(this,this.#l,t,e))}patch(t,e){return new rc(t,e,this)}transaction(t){return new rp(t,this)}action(t,e){return tH(rI(this,this.#l,t,e))}request(t){return tH(rN(this,this.#l,t))}dataRequest(t,e,r){return tH(rD(this,this.#l,t,e,r))}getUrl(t,e){return rH(this,t,e)}getDataUrl(t,e){return rW(this,t,e)}}let nb=function(t,e){return{requester:ej(t),createClient:r=>{let n=ej(t);return new e((t,e)=>(e||n)({maxRedirects:0,maxRetries:r.maxRetries,retryDelay:r.retryDelay,...t}),r)}}}([],nv),nw=(nb.requester,nb.createClient)},3509:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},4416:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4612:function(t){t.exports=function(){function t(){return(t=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function e(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var r="image-Tb9Ew8CXIwaY6R1kjMvI0uRR-2000x3000-jpg";function n(t){return("image-"+t.split("/").slice(-1)[0]).replace(/\.([a-z]+)$/,"-$1")}var i=[["width","w"],["height","h"],["format","fm"],["download","dl"],["blur","blur"],["sharpen","sharp"],["invert","invert"],["orientation","or"],["minHeight","min-h"],["maxHeight","max-h"],["minWidth","min-w"],["maxWidth","max-w"],["quality","q"],["fit","fit"],["crop","crop"],["saturation","sat"],["auto","auto"],["dpr","dpr"],["pad","pad"],["frame","frame"]],o=["clip","crop","fill","fillmax","max","scale","min"],s=["top","bottom","left","right","center","focalpoint","entropy"],a=["format"],l=function(){function l(e,r){this.options=void 0,this.options=e?t({},e.options||{},r||{}):t({},r||{})}var u=l.prototype;return u.withOptions=function(r){var n=r.baseUrl||this.options.baseUrl,o={baseUrl:n};for(var s in r)r.hasOwnProperty(s)&&(o[function(t){for(var r,n=function(t,r){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(n)return(n=n.call(t)).next.bind(n);if(Array.isArray(t)||(n=function(t,r){if(t){if("string"==typeof t)return e(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return e(t,void 0)}}(t))){n&&(t=n);var i=0;return function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(i);!(r=n()).done;){var o=r.value,s=o[0],a=o[1];if(t===s||t===a)return s}return t}(s)]=r[s]);return new l(this,t({baseUrl:n},o))},u.image=function(t){return this.withOptions({source:t})},u.dataset=function(t){return this.withOptions({dataset:t})},u.projectId=function(t){return this.withOptions({projectId:t})},u.bg=function(t){return this.withOptions({bg:t})},u.dpr=function(t){return this.withOptions(t&&1!==t?{dpr:t}:{})},u.width=function(t){return this.withOptions({width:t})},u.height=function(t){return this.withOptions({height:t})},u.focalPoint=function(t,e){return this.withOptions({focalPoint:{x:t,y:e}})},u.maxWidth=function(t){return this.withOptions({maxWidth:t})},u.minWidth=function(t){return this.withOptions({minWidth:t})},u.maxHeight=function(t){return this.withOptions({maxHeight:t})},u.minHeight=function(t){return this.withOptions({minHeight:t})},u.size=function(t,e){return this.withOptions({width:t,height:e})},u.blur=function(t){return this.withOptions({blur:t})},u.sharpen=function(t){return this.withOptions({sharpen:t})},u.rect=function(t,e,r,n){return this.withOptions({rect:{left:t,top:e,width:r,height:n}})},u.format=function(t){return this.withOptions({format:t})},u.invert=function(t){return this.withOptions({invert:t})},u.orientation=function(t){return this.withOptions({orientation:t})},u.quality=function(t){return this.withOptions({quality:t})},u.forceDownload=function(t){return this.withOptions({download:t})},u.flipHorizontal=function(){return this.withOptions({flipHorizontal:!0})},u.flipVertical=function(){return this.withOptions({flipVertical:!0})},u.ignoreImageParams=function(){return this.withOptions({ignoreImageParams:!0})},u.fit=function(t){if(-1===o.indexOf(t))throw Error('Invalid fit mode "'+t+'"');return this.withOptions({fit:t})},u.crop=function(t){if(-1===s.indexOf(t))throw Error('Invalid crop mode "'+t+'"');return this.withOptions({crop:t})},u.saturation=function(t){return this.withOptions({saturation:t})},u.auto=function(t){if(-1===a.indexOf(t))throw Error('Invalid auto mode "'+t+'"');return this.withOptions({auto:t})},u.pad=function(t){return this.withOptions({pad:t})},u.vanityName=function(t){return this.withOptions({vanityName:t})},u.frame=function(t){if(1!==t)throw Error('Invalid frame value "'+t+'"');return this.withOptions({frame:t})},u.url=function(){return function(e){var o=t({},e||{}),s=o.source;delete o.source;var a=function(e){var r,i;if(!e)return null;if("string"==typeof e&&(i=e,/^https?:\/\//.test(""+i)))r={asset:{_ref:n(e)}};else if("string"==typeof e)r={asset:{_ref:e}};else if(e&&"string"==typeof e._ref)r={asset:e};else if(e&&"string"==typeof e._id)r={asset:{_ref:e._id||""}};else if(e&&e.asset&&"string"==typeof e.asset.url)r={asset:{_ref:n(e.asset.url)}};else{if("object"!=typeof e.asset)return null;r=t({},e)}return e.crop&&(r.crop=e.crop),e.hotspot&&(r.hotspot=e.hotspot),function(e){if(e.crop&&e.hotspot)return e;var r=t({},e);return r.crop||(r.crop={left:0,top:0,bottom:0,right:0}),r.hotspot||(r.hotspot={x:.5,y:.5,height:1,width:1}),r}(r)}(s);if(!a)throw Error("Unable to resolve image URL from source ("+JSON.stringify(s)+")");var l=function(t){var e=t.split("-"),n=e[1],i=e[2],o=e[3];if(!n||!i||!o)throw Error("Malformed asset _ref '"+t+"'. Expected an id like \""+r+'".');var s=i.split("x"),a=s[0],l=s[1],u=+a,c=+l;if(!(isFinite(u)&&isFinite(c)))throw Error("Malformed asset _ref '"+t+"'. Expected an id like \""+r+'".');return{id:n,width:u,height:c,format:o}}(a.asset._ref||a.asset._id||""),u=Math.round(a.crop.left*l.width),c=Math.round(a.crop.top*l.height),h={left:u,top:c,width:Math.round(l.width-a.crop.right*l.width-u),height:Math.round(l.height-a.crop.bottom*l.height-c)},d=a.hotspot.height*l.height/2,p=a.hotspot.width*l.width/2,f=a.hotspot.x*l.width,m=a.hotspot.y*l.height;return o.rect||o.focalPoint||o.ignoreImageParams||o.crop||(o=t({},o,function(t,e){var r,n=e.width,i=e.height;if(!(n&&i))return{width:n,height:i,rect:t.crop};var o=t.crop,s=t.hotspot,a=n/i;if(o.width/o.height>a){var l=Math.round(o.height),u=Math.round(l*a),c=Math.max(0,Math.round(o.top)),h=Math.max(0,Math.round(Math.round((s.right-s.left)/2+s.left)-u/2));h<o.left?h=o.left:h+u>o.left+o.width&&(h=o.left+o.width-u),r={left:h,top:c,width:u,height:l}}else{var d=o.width,p=Math.round(d/a),f=Math.max(0,Math.round(o.left)),m=Math.max(0,Math.round(Math.round((s.bottom-s.top)/2+s.top)-p/2));m<o.top?m=o.top:m+p>o.top+o.height&&(m=o.top+o.height-p),r={left:f,top:m,width:d,height:p}}return{width:n,height:i,rect:r}}({crop:h,hotspot:{left:f-p,top:m-d,right:f+p,bottom:m+d}},o))),function(t){var e=(t.baseUrl||"https://cdn.sanity.io").replace(/\/+$/,""),r=t.vanityName?"/"+t.vanityName:"",n=t.asset.id+"-"+t.asset.width+"x"+t.asset.height+"."+t.asset.format+r,o=e+"/images/"+t.projectId+"/"+t.dataset+"/"+n,s=[];if(t.rect){var a=t.rect,l=a.left,u=a.top,c=a.width,h=a.height;(0!==l||0!==u||h!==t.asset.height||c!==t.asset.width)&&s.push("rect="+l+","+u+","+c+","+h)}t.bg&&s.push("bg="+t.bg),t.focalPoint&&(s.push("fp-x="+t.focalPoint.x),s.push("fp-y="+t.focalPoint.y));var d=[t.flipHorizontal&&"h",t.flipVertical&&"v"].filter(Boolean).join("");return(d&&s.push("flip="+d),i.forEach(function(e){var r=e[0],n=e[1];void 0!==t[r]?s.push(n+"="+encodeURIComponent(t[r])):void 0!==t[n]&&s.push(n+"="+encodeURIComponent(t[n]))}),0===s.length)?o:o+"?"+s.join("&")}(t({},o,{asset:l}))}(this.options)},u.toString=function(){return this.url()},l}();return function(t){if(t&&"config"in t&&"function"==typeof t.config){var e=t.config(),r=e.apiHost,n=e.projectId,i=e.dataset;return new l(null,{baseUrl:(r||"https://api.sanity.io").replace(/^https:\/\/api\./,"https://cdn."),projectId:n,dataset:i})}if(t&&"clientConfig"in t&&"object"==typeof t.clientConfig){var o=t.clientConfig,s=o.apiHost,a=o.projectId,u=o.dataset;return new l(null,{baseUrl:(s||"https://api.sanity.io").replace(/^https:\/\/api\./,"https://cdn."),projectId:a,dataset:u})}return new l(null,t||{})}}()},4783:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},6408:(t,e,r)=>{"use strict";let n;function i(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function o(t){let e=[{},{}];return null==t||t.values.forEach((t,r)=>{e[0][r]=t.get(),e[1][r]=t.getVelocity()}),e}function s(t,e,r,n){if("function"==typeof e){let[i,s]=o(n);e=e(void 0!==r?r:t.custom,i,s)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[i,s]=o(n);e=e(void 0!==r?r:t.custom,i,s)}return e}function a(t,e,r){let n=t.getProps();return s(n,e,void 0!==r?r:n.custom,t)}function l(t,e){return t?.[e]??t?.default??t}r.d(e,{P:()=>ok});let u=t=>t,c={},h=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],d={value:null,addProjectionMetrics:null};function p(t,e){let r=!1,n=!0,i={delta:0,timestamp:0,isProcessing:!1},o=()=>r=!0,s=h.reduce((t,r)=>(t[r]=function(t,e){let r=new Set,n=new Set,i=!1,o=!1,s=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){s.has(e)&&(c.schedule(e),t()),l++,e(a)}let c={schedule:(t,e=!1,o=!1)=>{let a=o&&i?r:n;return e&&s.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),s.delete(t)},process:t=>{if(a=t,i){o=!0;return}i=!0,[r,n]=[n,r],r.forEach(u),e&&d.value&&d.value.frameloop[e].push(l),l=0,r.clear(),i=!1,o&&(o=!1,c.process(t))}};return c}(o,e?r:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:p,update:f,preRender:m,render:g,postRender:y}=s,v=()=>{let o=c.useManualTiming?i.timestamp:performance.now();r=!1,c.useManualTiming||(i.delta=n?1e3/60:Math.max(Math.min(o-i.timestamp,40),1)),i.timestamp=o,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),p.process(i),f.process(i),m.process(i),g.process(i),y.process(i),i.isProcessing=!1,r&&e&&(n=!1,t(v))},b=()=>{r=!0,n=!0,i.isProcessing||t(v)};return{schedule:h.reduce((t,e)=>{let n=s[e];return t[e]=(t,e=!1,i=!1)=>(r||b(),n.schedule(t,e,i)),t},{}),cancel:t=>{for(let e=0;e<h.length;e++)s[h[e]].cancel(t)},state:i,steps:s}}let{schedule:f,cancel:m,state:g,steps:y}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(v),w=new Set(["width","height","top","left","right","bottom",...v]);function x(t,e){-1===t.indexOf(e)&&t.push(e)}function E(t,e){let r=t.indexOf(e);r>-1&&t.splice(r,1)}class T{constructor(){this.subscriptions=[]}add(t){return x(this.subscriptions,t),()=>E(this.subscriptions,t)}notify(t,e,r){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,r);else for(let i=0;i<n;i++){let n=this.subscriptions[i];n&&n(t,e,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function C(){n=void 0}let A={now:()=>(void 0===n&&A.set(g.isProcessing||c.useManualTiming?g.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(C)}},S=t=>!isNaN(parseFloat(t)),k={current:void 0};class P{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let r=A.now();if(this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=A.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=S(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new T);let r=this.events[t].add(e);return"change"===t?()=>{r(),f.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,r){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return k.current&&k.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=A.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let r=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),r?1e3/r*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function R(t,e){return new P(t,e)}let M=t=>Array.isArray(t),j=t=>!!(t&&t.getVelocity);function O(t,e){let r=t.getValue("willChange");if(j(r)&&r.add)return r.add(e);if(!r&&c.WillChange){let r=new c.WillChange("auto");t.addValue("willChange",r),r.add(e)}}let I=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),D="data-"+I("framerAppearId"),V=(t,e)=>r=>e(t(r)),q=(...t)=>t.reduce(V),F=(t,e,r)=>r>e?e:r<t?t:r,$=t=>1e3*t,L=t=>t/1e3,B={layout:0,mainThread:0,waapi:0},_=()=>{},U=()=>{},z=t=>e=>"string"==typeof e&&e.startsWith(t),N=z("--"),W=z("var(--"),H=t=>!!W(t)&&Y.test(t.split("/*")[0].trim()),Y=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,G={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},X={...G,transform:t=>F(0,1,t)},K={...G,default:1},Z=t=>Math.round(1e5*t)/1e5,J=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,Q=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>r=>!!("string"==typeof r&&Q.test(r)&&r.startsWith(t)||e&&null!=r&&Object.prototype.hasOwnProperty.call(r,e)),te=(t,e,r)=>n=>{if("string"!=typeof n)return n;let[i,o,s,a]=n.match(J);return{[t]:parseFloat(i),[e]:parseFloat(o),[r]:parseFloat(s),alpha:void 0!==a?parseFloat(a):1}},tr=t=>F(0,255,t),tn={...G,transform:t=>Math.round(tr(t))},ti={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:r,alpha:n=1})=>"rgba("+tn.transform(t)+", "+tn.transform(e)+", "+tn.transform(r)+", "+Z(X.transform(n))+")"},to={test:tt("#"),parse:function(t){let e="",r="",n="",i="";return t.length>5?(e=t.substring(1,3),r=t.substring(3,5),n=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),r=t.substring(2,3),n=t.substring(3,4),i=t.substring(4,5),e+=e,r+=r,n+=n,i+=i),{red:parseInt(e,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}},transform:ti.transform},ts=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ta=ts("deg"),tl=ts("%"),tu=ts("px"),tc=ts("vh"),th=ts("vw"),td={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},tp={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:r,alpha:n=1})=>"hsla("+Math.round(t)+", "+tl.transform(Z(e))+", "+tl.transform(Z(r))+", "+Z(X.transform(n))+")"},tf={test:t=>ti.test(t)||to.test(t)||tp.test(t),parse:t=>ti.test(t)?ti.parse(t):tp.test(t)?tp.parse(t):to.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?ti.transform(t):tp.transform(t),getAnimatableNone:t=>{let e=tf.parse(t);return e.alpha=0,tf.transform(e)}},tm=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tg="number",ty="color",tv=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tb(t){let e=t.toString(),r=[],n={color:[],number:[],var:[]},i=[],o=0,s=e.replace(tv,t=>(tf.test(t)?(n.color.push(o),i.push(ty),r.push(tf.parse(t))):t.startsWith("var(")?(n.var.push(o),i.push("var"),r.push(t)):(n.number.push(o),i.push(tg),r.push(parseFloat(t))),++o,"${}")).split("${}");return{values:r,split:s,indexes:n,types:i}}function tw(t){return tb(t).values}function tx(t){let{split:e,types:r}=tb(t),n=e.length;return t=>{let i="";for(let o=0;o<n;o++)if(i+=e[o],void 0!==t[o]){let e=r[o];e===tg?i+=Z(t[o]):e===ty?i+=tf.transform(t[o]):i+=t[o]}return i}}let tE=t=>"number"==typeof t?0:tf.test(t)?tf.getAnimatableNone(t):t,tT={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(J)?.length||0)+(t.match(tm)?.length||0)>0},parse:tw,createTransformer:tx,getAnimatableNone:function(t){let e=tw(t);return tx(t)(e.map(tE))}};function tC(t,e,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?t+(e-t)*6*r:r<.5?e:r<2/3?t+(e-t)*(2/3-r)*6:t}function tA(t,e){return r=>r>0?e:t}let tS=(t,e,r)=>t+(e-t)*r,tk=(t,e,r)=>{let n=t*t,i=r*(e*e-n)+n;return i<0?0:Math.sqrt(i)},tP=[to,ti,tp],tR=t=>tP.find(e=>e.test(t));function tM(t){let e=tR(t);if(_(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let r=e.parse(t);return e===tp&&(r=function({hue:t,saturation:e,lightness:r,alpha:n}){t/=360,r/=100;let i=0,o=0,s=0;if(e/=100){let n=r<.5?r*(1+e):r+e-r*e,a=2*r-n;i=tC(a,n,t+1/3),o=tC(a,n,t),s=tC(a,n,t-1/3)}else i=o=s=r;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*s),alpha:n}}(r)),r}let tj=(t,e)=>{let r=tM(t),n=tM(e);if(!r||!n)return tA(t,e);let i={...r};return t=>(i.red=tk(r.red,n.red,t),i.green=tk(r.green,n.green,t),i.blue=tk(r.blue,n.blue,t),i.alpha=tS(r.alpha,n.alpha,t),ti.transform(i))},tO=new Set(["none","hidden"]);function tI(t,e){return r=>tS(t,e,r)}function tD(t){return"number"==typeof t?tI:"string"==typeof t?H(t)?tA:tf.test(t)?tj:tF:Array.isArray(t)?tV:"object"==typeof t?tf.test(t)?tj:tq:tA}function tV(t,e){let r=[...t],n=r.length,i=t.map((t,r)=>tD(t)(t,e[r]));return t=>{for(let e=0;e<n;e++)r[e]=i[e](t);return r}}function tq(t,e){let r={...t,...e},n={};for(let i in r)void 0!==t[i]&&void 0!==e[i]&&(n[i]=tD(t[i])(t[i],e[i]));return t=>{for(let e in n)r[e]=n[e](t);return r}}let tF=(t,e)=>{let r=tT.createTransformer(e),n=tb(t),i=tb(e);return n.indexes.var.length===i.indexes.var.length&&n.indexes.color.length===i.indexes.color.length&&n.indexes.number.length>=i.indexes.number.length?tO.has(t)&&!i.values.length||tO.has(e)&&!n.values.length?function(t,e){return tO.has(t)?r=>r<=0?t:e:r=>r>=1?e:t}(t,e):q(tV(function(t,e){let r=[],n={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){let o=e.types[i],s=t.indexes[o][n[o]],a=t.values[s]??0;r[i]=a,n[o]++}return r}(n,i),i.values),r):(_(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tA(t,e))};function t$(t,e,r){return"number"==typeof t&&"number"==typeof e&&"number"==typeof r?tS(t,e,r):tD(t)(t,e)}let tL=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>f.update(e,t),stop:()=>m(e),now:()=>g.isProcessing?g.timestamp:A.now()}},tB=(t,e,r=10)=>{let n="",i=Math.max(Math.round(e/r),2);for(let e=0;e<i;e++)n+=Math.round(1e4*t(e/(i-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function t_(t){let e=0,r=t.next(e);for(;!r.done&&e<2e4;)e+=50,r=t.next(e);return e>=2e4?1/0:e}function tU(t,e,r){var n,i;let o=Math.max(e-5,0);return n=r-t(o),(i=e-o)?1e3/i*n:0}let tz={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tN(t,e){return t*Math.sqrt(1-e*e)}let tW=["duration","bounce"],tH=["stiffness","damping","mass"];function tY(t,e){return e.some(e=>void 0!==t[e])}function tG(t=tz.visualDuration,e=tz.bounce){let r,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:i,restDelta:o}=n,s=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:c,mass:h,duration:d,velocity:p,isResolvedFromDuration:f}=function(t){let e={velocity:tz.velocity,stiffness:tz.stiffness,damping:tz.damping,mass:tz.mass,isResolvedFromDuration:!1,...t};if(!tY(t,tH)&&tY(t,tW))if(t.visualDuration){let r=2*Math.PI/(1.2*t.visualDuration),n=r*r,i=2*F(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:tz.mass,stiffness:n,damping:i}}else{let r=function({duration:t=tz.duration,bounce:e=tz.bounce,velocity:r=tz.velocity,mass:n=tz.mass}){let i,o;_(t<=$(tz.maxDuration),"Spring duration must be 10 seconds or less");let s=1-e;s=F(tz.minDamping,tz.maxDamping,s),t=F(tz.minDuration,tz.maxDuration,L(t)),s<1?(i=e=>{let n=e*s,i=n*t;return .001-(n-r)/tN(e,s)*Math.exp(-i)},o=e=>{let n=e*s*t,o=Math.pow(s,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=tN(Math.pow(e,2),s);return(n*r+r-o)*a*(-i(e)+.001>0?-1:1)/l}):(i=e=>-.001+Math.exp(-e*t)*((e-r)*t+1),o=e=>t*t*(r-e)*Math.exp(-e*t));let a=function(t,e,r){let n=r;for(let r=1;r<12;r++)n-=t(n)/e(n);return n}(i,o,5/t);if(t=$(t),isNaN(a))return{stiffness:tz.stiffness,damping:tz.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*s*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...r,mass:tz.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-L(n.velocity||0)}),m=p||0,g=c/(2*Math.sqrt(u*h)),y=a-s,v=L(Math.sqrt(u/h)),b=5>Math.abs(y);if(i||(i=b?tz.restSpeed.granular:tz.restSpeed.default),o||(o=b?tz.restDelta.granular:tz.restDelta.default),g<1){let t=tN(v,g);r=e=>a-Math.exp(-g*v*e)*((m+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)r=t=>a-Math.exp(-v*t)*(y+(m+v*y)*t);else{let t=v*Math.sqrt(g*g-1);r=e=>{let r=Math.exp(-g*v*e),n=Math.min(t*e,300);return a-r*((m+g*v*y)*Math.sinh(n)+t*y*Math.cosh(n))/t}}let w={calculatedDuration:f&&d||null,next:t=>{let e=r(t);if(f)l.done=t>=d;else{let n=0===t?m:0;g<1&&(n=0===t?$(m):tU(r,t,e));let s=Math.abs(a-e)<=o;l.done=Math.abs(n)<=i&&s}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(t_(w),2e4),e=tB(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function tX({keyframes:t,velocity:e=0,power:r=.8,timeConstant:n=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){let h,d,p=t[0],f={done:!1,value:p},m=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,y=r*e,v=p+y,b=void 0===s?v:s(v);b!==v&&(y=b-p);let w=t=>-y*Math.exp(-t/n),x=t=>b+w(t),E=t=>{let e=w(t),r=x(t);f.done=Math.abs(e)<=u,f.value=f.done?b:r},T=t=>{m(f.value)&&(h=t,d=tG({keyframes:[f.value,g(f.value)],velocity:tU(x,t,f.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==h||(e=!0,E(t),T(t)),void 0!==h&&t>=h)?d.next(t-h):(e||E(t),f)}}}tG.applyToOptions=t=>{let e=function(t,e=100,r){let n=r({...t,keyframes:[0,e]}),i=Math.min(t_(n),2e4);return{type:"keyframes",ease:t=>n.next(i*t).value/e,duration:L(i)}}(t,100,tG);return t.ease=e.ease,t.duration=$(e.duration),t.type="keyframes",t};let tK=(t,e,r)=>(((1-3*r+3*e)*t+(3*r-6*e))*t+3*e)*t;function tZ(t,e,r,n){if(t===e&&r===n)return u;let i=e=>(function(t,e,r,n,i){let o,s,a=0;do(o=tK(s=e+(r-e)/2,n,i)-t)>0?r=s:e=s;while(Math.abs(o)>1e-7&&++a<12);return s})(e,0,1,t,r);return t=>0===t||1===t?t:tK(i(t),e,n)}let tJ=tZ(.42,0,1,1),tQ=tZ(0,0,.58,1),t0=tZ(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t2=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t3=t=>e=>1-t(1-e),t5=tZ(.33,1.53,.69,.99),t6=t3(t5),t8=t2(t6),t4=t=>(t*=2)<1?.5*t6(t):.5*(2-Math.pow(2,-10*(t-1))),t9=t=>1-Math.sin(Math.acos(t)),t7=t3(t9),et=t2(t9),ee=t=>Array.isArray(t)&&"number"==typeof t[0],er={linear:u,easeIn:tJ,easeInOut:t0,easeOut:tQ,circIn:t9,circInOut:et,circOut:t7,backIn:t6,backInOut:t8,backOut:t5,anticipate:t4},en=t=>"string"==typeof t,ei=t=>{if(ee(t)){U(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,r,n,i]=t;return tZ(e,r,n,i)}return en(t)?(U(void 0!==er[t],`Invalid easing type '${t}'`),er[t]):t},eo=(t,e,r)=>{let n=e-t;return 0===n?1:(r-t)/n};function es({duration:t=300,keyframes:e,times:r,ease:n="easeInOut"}){var i;let o=t1(n)?n.map(ei):ei(n),s={done:!1,value:e[0]},a=function(t,e,{clamp:r=!0,ease:n,mixer:i}={}){let o=t.length;if(U(o===e.length,"Both input and output ranges must be the same length"),1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];let s=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,r){let n=[],i=r||c.mix||t$,o=t.length-1;for(let r=0;r<o;r++){let o=i(t[r],t[r+1]);e&&(o=q(Array.isArray(e)?e[r]||u:e,o)),n.push(o)}return n}(e,n,i),l=a.length,h=r=>{if(s&&r<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(r<t[n+1]);n++);let i=eo(t[n],t[n+1],r);return a[n](i)};return r?e=>h(F(t[0],t[o-1],e)):h}((i=r&&r.length===e.length?r:function(t){let e=[0];return!function(t,e){let r=t[t.length-1];for(let n=1;n<=e;n++){let i=eo(0,e,n);t.push(tS(r,1,i))}}(e,t.length-1),e}(e),i.map(e=>e*t)),e,{ease:Array.isArray(o)?o:e.map(()=>o||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=a(e),s.done=e>=t,s)}}let ea=t=>null!==t;function el(t,{repeat:e,repeatType:r="loop"},n,i=1){let o=t.filter(ea),s=i<0||e&&"loop"!==r&&e%2==1?0:o.length-1;return s&&void 0!==n?n:o[s]}let eu={decay:tX,inertia:tX,tween:es,keyframes:es,spring:tG};function ec(t){"string"==typeof t.type&&(t.type=eu[t.type])}class eh{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ed=t=>t/100;class ep extends eh{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==A.now()&&this.tick(A.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},B.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;ec(t);let{type:e=es,repeat:r=0,repeatDelay:n=0,repeatType:i,velocity:o=0}=t,{keyframes:s}=t,a=e||es;a!==es&&"number"!=typeof s[0]&&(this.mixKeyframes=q(ed,t$(s[0],s[1])),s=[0,100]);let l=a({...t,keyframes:s});"mirror"===i&&(this.mirroredGenerator=a({...t,keyframes:[...s].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=t_(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(r+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:r,totalDuration:n,mixKeyframes:i,mirroredGenerator:o,resolvedDuration:s,calculatedDuration:a}=this;if(null===this.startTime)return r.next(0);let{delay:l=0,keyframes:u,repeat:c,repeatType:h,repeatDelay:d,type:p,onUpdate:f,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,b=r;if(c){let t=Math.min(this.currentTime,n)/s,e=Math.floor(t),r=t%1;!r&&t>=1&&(r=1),1===r&&e--,(e=Math.min(e,c+1))%2&&("reverse"===h?(r=1-r,d&&(r-=d/s)):"mirror"===h&&(b=o)),v=F(0,1,r)*s}let w=y?{done:!1,value:u[0]}:b.next(v);i&&(w.value=i(w.value));let{done:x}=w;y||null===a||(x=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let E=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return E&&p!==tX&&(w.value=el(u,this.options,m,this.speed)),f&&f(w.value),E&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return L(this.calculatedDuration)}get time(){return L(this.currentTime)}set time(t){t=$(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(A.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=L(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tL,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let r=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=r):null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime||(this.startTime=e??r),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(A.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,B.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ef=t=>180*t/Math.PI,em=t=>ey(ef(Math.atan2(t[1],t[0]))),eg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:em,rotateZ:em,skewX:t=>ef(Math.atan(t[1])),skewY:t=>ef(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ey=t=>((t%=360)<0&&(t+=360),t),ev=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),eb=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ew={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ev,scaleY:eb,scale:t=>(ev(t)+eb(t))/2,rotateX:t=>ey(ef(Math.atan2(t[6],t[5]))),rotateY:t=>ey(ef(Math.atan2(-t[2],t[0]))),rotateZ:em,rotate:em,skewX:t=>ef(Math.atan(t[4])),skewY:t=>ef(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ex(t){return+!!t.includes("scale")}function eE(t,e){let r,n;if(!t||"none"===t)return ex(e);let i=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)r=ew,n=i;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=eg,n=e}if(!n)return ex(e);let o=r[e],s=n[1].split(",").map(eC);return"function"==typeof o?o(s):s[o]}let eT=(t,e)=>{let{transform:r="none"}=getComputedStyle(t);return eE(r,e)};function eC(t){return parseFloat(t.trim())}let eA=t=>t===G||t===tu,eS=new Set(["x","y","z"]),ek=v.filter(t=>!eS.has(t)),eP={width:({x:t},{paddingLeft:e="0",paddingRight:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),height:({y:t},{paddingTop:e="0",paddingBottom:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eE(e,"x"),y:(t,{transform:e})=>eE(e,"y")};eP.translateX=eP.x,eP.translateY=eP.y;let eR=new Set,eM=!1,ej=!1,eO=!1;function eI(){if(ej){let t=Array.from(eR).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),r=new Map;e.forEach(t=>{let e=function(t){let e=[];return ek.forEach(r=>{let n=t.getValue(r);void 0!==n&&(e.push([r,n.get()]),n.set(+!!r.startsWith("scale")))}),e}(t);e.length&&(r.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=r.get(t);e&&e.forEach(([e,r])=>{t.getValue(e)?.set(r)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}ej=!1,eM=!1,eR.forEach(t=>t.complete(eO)),eR.clear()}function eD(){eR.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(ej=!0)})}class eV{constructor(t,e,r,n,i,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=r,this.motionValue=n,this.element=i,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(eR.add(this),eM||(eM=!0,f.read(eD),f.resolveKeyframes(eI))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:r,motionValue:n}=this;if(null===t[0]){let i=n?.get(),o=t[t.length-1];if(void 0!==i)t[0]=i;else if(r&&e){let n=r.readValue(e,o);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=o),n&&void 0===i&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eR.delete(this)}cancel(){"scheduled"===this.state&&(eR.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eq=t=>t.startsWith("--");function eF(t){let e;return()=>(void 0===e&&(e=t()),e)}let e$=eF(()=>void 0!==window.ScrollTimeline),eL={},eB=function(t,e){let r=eF(t);return()=>eL[e]??r()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),e_=([t,e,r,n])=>`cubic-bezier(${t}, ${e}, ${r}, ${n})`,eU={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:e_([0,.65,.55,1]),circOut:e_([.55,0,1,.45]),backIn:e_([.31,.01,.66,-.59]),backOut:e_([.33,1.53,.69,.99])};function ez(t){return"function"==typeof t&&"applyToOptions"in t}class eN extends eh{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:r,keyframes:n,pseudoElement:i,allowFlatten:o=!1,finalKeyframe:s,onComplete:a}=t;this.isPseudoElement=!!i,this.allowFlatten=o,this.options=t,U("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return ez(t)&&eB()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,r,{delay:n=0,duration:i=300,repeat:o=0,repeatType:s="loop",ease:a="easeOut",times:l}={},u){let c={[e]:r};l&&(c.offset=l);let h=function t(e,r){if(e)return"function"==typeof e?eB()?tB(e,r):"ease-out":ee(e)?e_(e):Array.isArray(e)?e.map(e=>t(e,r)||eU.easeOut):eU[e]}(a,i);Array.isArray(h)&&(c.easing=h),d.value&&B.waapi++;let p={delay:n,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"};u&&(p.pseudoElement=u);let f=t.animate(c,p);return d.value&&f.finished.finally(()=>{B.waapi--}),f}(e,r,n,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let t=el(n,this.options,s,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,r){eq(e)?t.style.setProperty(e,r):t.style[e]=r}(e,r,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return L(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return L(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=$(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&e$())?(this.animation.timeline=t,u):e(this)}}let eW={anticipate:t4,backInOut:t8,circInOut:et};class eH extends eN{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eW&&(t.ease=eW[t.ease])}(t),ec(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:r,onComplete:n,element:i,...o}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let s=new ep({...o,autoplay:!1}),a=$(this.finishedTime??this.time);e.setWithVelocity(s.sample(a-10).value,s.sample(a).value,10),s.stop()}}let eY=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tT.test(t)||"0"===t)&&!t.startsWith("url("));var eG,eX,eK=r(7351);let eZ=new Set(["opacity","clipPath","filter","transform"]),eJ=eF(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eQ extends eh{constructor({autoplay:t=!0,delay:e=0,type:r="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:o="loop",keyframes:s,name:a,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=A.now();let h={autoplay:t,delay:e,type:r,repeat:n,repeatDelay:i,repeatType:o,name:a,motionValue:l,element:u,...c},d=u?.KeyframeResolver||eV;this.keyframeResolver=new d(s,(t,e,r)=>this.onKeyframesResolved(t,e,h,!r),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,r,n){this.keyframeResolver=void 0;let{name:i,type:o,velocity:s,delay:a,isHandoff:l,onUpdate:h}=r;this.resolvedAt=A.now(),!function(t,e,r,n){let i=t[0];if(null===i)return!1;if("display"===e||"visibility"===e)return!0;let o=t[t.length-1],s=eY(i,e),a=eY(o,e);return _(s===a,`You are trying to animate ${e} from "${i}" to "${o}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${o} via the \`style\` property.`),!!s&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let r=0;r<t.length;r++)if(t[r]!==e)return!0}(t)||("spring"===r||ez(r))&&n)}(t,i,o,s)&&((c.instantAnimations||!a)&&h?.(el(t,r,e)),t[0]=t[t.length-1],r.duration=0,r.repeat=0);let d={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...r,keyframes:t},p=!l&&function(t){let{motionValue:e,name:r,repeatDelay:n,repeatType:i,damping:o,type:s}=t;if(!(0,eK.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eJ()&&r&&eZ.has(r)&&("transform"!==r||!l)&&!a&&!n&&"mirror"!==i&&0!==o&&"inertia"!==s}(d)?new eH({...d,element:d.motionValue.owner.current}):new ep(d);p.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eO=!0,eD(),eI(),eO=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e0=t=>null!==t,e1={type:"spring",stiffness:500,damping:25,restSpeed:10},e2=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e3={type:"keyframes",duration:.8},e5={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e6=(t,e)=>{let{keyframes:r}=e;return r.length>2?e3:b.has(t)?t.startsWith("scale")?e2(r[1]):e1:e5},e8=function(t,e,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4?arguments[4]:void 0,o=arguments.length>5?arguments[5]:void 0;return s=>{let a=l(n,t)||{},u=a.delay||n.delay||0,{elapsed:h=0}=n;h-=$(u);let d={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-h,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{s(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:i};!function(t){let{when:e,delay:r,delayChildren:n,staggerChildren:i,staggerDirection:o,repeat:s,repeatType:a,repeatDelay:l,from:u,elapsed:c,...h}=t;return!!Object.keys(h).length}(a)&&Object.assign(d,e6(t,d)),d.duration&&(d.duration=$(d.duration)),d.repeatDelay&&(d.repeatDelay=$(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let p=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(p=!0)),(c.instantAnimations||c.skipAnimations)&&(p=!0,d.duration=0,d.delay=0),d.allowFlatten=!a.type&&!a.ease,p&&!o&&void 0!==e.get()){let t=function(t,e,r){let{repeat:n,repeatType:i="loop"}=e,o=t.filter(e0),s=n&&"loop"!==i&&n%2==1?0:o.length-1;return o[s]}(d.keyframes,a);if(void 0!==t)return void f.update(()=>{d.onUpdate(t),d.onComplete()})}return a.isSync?new ep(d):new eQ(d)}};function e4(t,e){let{delay:r=0,transitionOverride:n,type:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{transition:o=t.getDefaultTransition(),transitionEnd:s,...u}=e;n&&(o=n);let c=[],h=i&&t.animationState&&t.animationState.getState()[i];for(let e in u){var d;let n=t.getValue(e,null!=(d=t.latestValues[e])?d:null),i=u[e];if(void 0===i||h&&function(t,e){let{protectedKeys:r,needsAnimating:n}=t,i=r.hasOwnProperty(e)&&!0!==n[e];return n[e]=!1,i}(h,e))continue;let s={delay:r,...l(o||{},e)},a=n.get();if(void 0!==a&&!n.isAnimating&&!Array.isArray(i)&&i===a&&!s.velocity)continue;let p=!1;if(window.MotionHandoffAnimation){let r=t.props[D];if(r){let t=window.MotionHandoffAnimation(r,e,f);null!==t&&(s.startTime=t,p=!0)}}O(t,e),n.start(e8(e,n,i,t.shouldReduceMotion&&w.has(e)?{type:!1}:s,t,p));let m=n.animation;m&&c.push(m)}return s&&Promise.all(c).then(()=>{f.update(()=>{s&&function(t,e){let{transitionEnd:r={},transition:n={},...i}=a(t,e)||{};for(let e in i={...i,...r}){var o;let r=M(o=i[e])?o[o.length-1]||0:o;t.hasValue(e)?t.getValue(e).set(r):t.addValue(e,R(r))}}(t,s)})}),c}function e9(t,e){var r;let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=a(t,e,"exit"===n.type?null==(r=t.presenceContext)?void 0:r.custom:void 0),{transition:o=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(o=n.transitionOverride);let s=i?()=>Promise.all(e4(t,i,n)):()=>Promise.resolve(),l=t.variantChildren&&t.variantChildren.size?function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,{delayChildren:i=0,staggerChildren:s,staggerDirection:a}=o;return function(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,s=arguments.length>6?arguments[6]:void 0,a=[],l=t.variantChildren.size,u=(l-1)*i,c="function"==typeof n,h=c?t=>n(t,l):1===o?function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return t*i}:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return u-t*i};return Array.from(t.variantChildren).sort(e7).forEach((t,i)=>{t.notify("AnimationStart",e),a.push(e9(t,e,{...s,delay:r+(c?0:n)+h(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,r,i,s,a,n)}:()=>Promise.resolve(),{when:u}=o;if(!u)return Promise.all([s(),l(n.delay)]);{let[t,e]="beforeChildren"===u?[s,l]:[l,s];return t().then(()=>e())}}function e7(t,e){return t.sortNodePosition(e)}function rt(t,e){if(!Array.isArray(e))return!1;let r=e.length;if(r!==t.length)return!1;for(let n=0;n<r;n++)if(e[n]!==t[n])return!1;return!0}function re(t){return"string"==typeof t||Array.isArray(t)}let rr=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],rn=["initial",...rr],ri=rn.length,ro=[...rr].reverse(),rs=rr.length;function ra(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rl(){return{animate:ra(!0),whileInView:ra(),whileHover:ra(),whileTap:ra(),whileDrag:ra(),whileFocus:ra(),exit:ra()}}class ru{update(){}constructor(t){this.isMounted=!1,this.node=t}}class rc extends ru{updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();i(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null==(t=this.unmountControls)||t.call(this)}constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(e=>{let{animation:r,options:n}=e;return function(t,e){let r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>e9(t,e,n)));else if("string"==typeof e)r=e9(t,e,n);else{let i="function"==typeof e?a(t,e,n.custom):e;r=Promise.all(e4(t,i,n))}return r.then(()=>{t.notify("AnimationComplete",e)})}(t,r,n)})),r=rl(),n=!0,o=e=>(r,n)=>{var i;let o=a(t,n,"exit"===e?null==(i=t.presenceContext)?void 0:i.custom:void 0);if(o){let{transition:t,transitionEnd:e,...n}=o;r={...r,...n,...e}}return r};function s(s){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let r=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(r.initial=e.props.initial),r}let r={};for(let t=0;t<ri;t++){let n=rn[t],i=e.props[n];(re(i)||!1===i)&&(r[n]=i)}return r}(t.parent)||{},c=[],h=new Set,d={},p=1/0;for(let e=0;e<rs;e++){var f,m;let a=ro[e],g=r[a],y=void 0!==l[a]?l[a]:u[a],v=re(y),b=a===s?g.isActive:null;!1===b&&(p=e);let w=y===u[a]&&y!==l[a]&&v;if(w&&n&&t.manuallyAnimateOnMount&&(w=!1),g.protectedKeys={...d},!g.isActive&&null===b||!y&&!g.prevProp||i(y)||"boolean"==typeof y)continue;let x=(f=g.prevProp,"string"==typeof(m=y)?m!==f:!!Array.isArray(m)&&!rt(m,f)),E=x||a===s&&g.isActive&&!w&&v||e>p&&v,T=!1,C=Array.isArray(y)?y:[y],A=C.reduce(o(a),{});!1===b&&(A={});let{prevResolvedValues:S={}}=g,k={...S,...A},P=e=>{E=!0,h.has(e)&&(T=!0,h.delete(e)),g.needsAnimating[e]=!0;let r=t.getValue(e);r&&(r.liveStyle=!1)};for(let t in k){let e=A[t],r=S[t];if(d.hasOwnProperty(t))continue;let n=!1;(M(e)&&M(r)?rt(e,r):e===r)?void 0!==e&&h.has(t)?P(t):g.protectedKeys[t]=!0:null!=e?P(t):h.add(t)}g.prevProp=y,g.prevResolvedValues=A,g.isActive&&(d={...d,...A}),n&&t.blockInitialAnimation&&(E=!1);let R=!(w&&x)||T;E&&R&&c.push(...C.map(t=>({animation:t,options:{type:a}})))}if(h.size){let e={};if("boolean"!=typeof l.initial){let r=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);r&&r.transition&&(e.transition=r.transition)}h.forEach(r=>{let n=t.getBaseTarget(r),i=t.getValue(r);i&&(i.liveStyle=!0),e[r]=null!=n?n:null}),c.push({animation:e})}let g=!!c.length;return n&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(g=!1),n=!1,g?e(c):Promise.resolve()}return{animateChanges:s,setActive:function(e,n){var i;if(r[e].isActive===n)return Promise.resolve();null==(i=t.variantChildren)||i.forEach(t=>{var r;return null==(r=t.animationState)?void 0:r.setActive(e,n)}),r[e].isActive=n;let o=s(e);for(let t in r)r[t].protectedKeys={};return o},setAnimateFunction:function(r){e=r(t)},getState:()=>r,reset:()=>{r=rl(),n=!0}}}(t))}}let rh=0;class rd extends ru{update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}constructor(){super(...arguments),this.id=rh++}}let rp={x:!1,y:!1};function rf(t,e,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return t.addEventListener(e,r,n),()=>t.removeEventListener(e,r)}let rm=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function rg(t){return{point:{x:t.pageX,y:t.pageY}}}let ry=t=>e=>rm(e)&&t(e,rg(e));function rv(t,e,r,n){return rf(t,e,ry(r),n)}function rb(t){let{top:e,left:r,right:n,bottom:i}=t;return{x:{min:r,max:n},y:{min:e,max:i}}}function rw(t){return t.max-t.min}function rx(t,e,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;t.origin=n,t.originPoint=tS(e.min,e.max,t.origin),t.scale=rw(r)/rw(e),t.translate=tS(r.min,r.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function rE(t,e,r,n){rx(t.x,e.x,r.x,n?n.originX:void 0),rx(t.y,e.y,r.y,n?n.originY:void 0)}function rT(t,e,r){t.min=r.min+e.min,t.max=t.min+rw(e)}function rC(t,e,r){t.min=e.min-r.min,t.max=t.min+rw(e)}function rA(t,e,r){rC(t.x,e.x,r.x),rC(t.y,e.y,r.y)}let rS=()=>({translate:0,scale:1,origin:0,originPoint:0}),rk=()=>({x:rS(),y:rS()}),rP=()=>({min:0,max:0}),rR=()=>({x:rP(),y:rP()});function rM(t){return[t("x"),t("y")]}function rj(t){return void 0===t||1===t}function rO(t){let{scale:e,scaleX:r,scaleY:n}=t;return!rj(e)||!rj(r)||!rj(n)}function rI(t){return rO(t)||rD(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function rD(t){var e,r;return(e=t.x)&&"0%"!==e||(r=t.y)&&"0%"!==r}function rV(t,e,r,n,i){return void 0!==i&&(t=n+i*(t-n)),n+r*(t-n)+e}function rq(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0;t.min=rV(t.min,e,r,n,i),t.max=rV(t.max,e,r,n,i)}function rF(t,e){let{x:r,y:n}=e;rq(t.x,r.translate,r.scale,r.originPoint),rq(t.y,n.translate,n.scale,n.originPoint)}function r$(t,e){t.min=t.min+e,t.max=t.max+e}function rL(t,e,r,n){let i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:.5,o=tS(t.min,t.max,i);rq(t,e,r,o,n)}function rB(t,e){rL(t.x,e.x,e.scaleX,e.scale,e.originX),rL(t.y,e.y,e.scaleY,e.scale,e.originY)}function r_(t,e){return rb(function(t,e){if(!e)return t;let r=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let rU=t=>{let{current:e}=t;return e?e.ownerDocument.defaultView:null};function rz(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let rN=(t,e)=>Math.abs(t-e);class rW{updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}constructor(t,e,{transformPagePoint:r,contextWindow:n=window,dragSnapToOrigin:i=!1,distanceThreshold:o=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=rG(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,r=function(t,e){return Math.sqrt(rN(t.x,e.x)**2+rN(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!r)return;let{point:n}=t,{timestamp:i}=g;this.history.push({...n,timestamp:i});let{onStart:o,onMove:s}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=rH(e,this.transformPagePoint),f.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:r,onSessionEnd:n,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=rG("pointercancel"===t.type?this.lastMoveEventInfo:rH(e,this.transformPagePoint),this.history);this.startEvent&&r&&r(t,o),n&&n(t,o)},!rm(t))return;this.dragSnapToOrigin=i,this.handlers=e,this.transformPagePoint=r,this.distanceThreshold=o,this.contextWindow=n||window;let s=rH(rg(t),this.transformPagePoint),{point:a}=s,{timestamp:l}=g;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=e;u&&u(t,rG(s,this.history)),this.removeListeners=q(rv(this.contextWindow,"pointermove",this.handlePointerMove),rv(this.contextWindow,"pointerup",this.handlePointerUp),rv(this.contextWindow,"pointercancel",this.handlePointerUp))}}function rH(t,e){return e?{point:e(t.point)}:t}function rY(t,e){return{x:t.x-e.x,y:t.y-e.y}}function rG(t,e){let{point:r}=t;return{point:r,delta:rY(r,rX(e)),offset:rY(r,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let r=t.length-1,n=null,i=rX(t);for(;r>=0&&(n=t[r],!(i.timestamp-n.timestamp>$(.1)));)r--;if(!n)return{x:0,y:0};let o=L(i.timestamp-n.timestamp);if(0===o)return{x:0,y:0};let s={x:(i.x-n.x)/o,y:(i.y-n.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(e,.1)}}function rX(t){return t[t.length-1]}function rK(t,e,r){return{min:void 0!==e?t.min+e:void 0,max:void 0!==r?t.max+r-(t.max-t.min):void 0}}function rZ(t,e){let r=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([r,n]=[n,r]),{min:r,max:n}}function rJ(t,e,r){return{min:rQ(t,e),max:rQ(t,r)}}function rQ(t,e){return"number"==typeof t?t:t[e]||0}let r0=new WeakMap;class r1{start(t){let{snapToCursor:e=!1,distanceThreshold:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new rW(t,{onSessionStart:t=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(rg(t).point)},onStart:(t,e)=>{let{drag:r,dragPropagation:n,onDragStart:i}=this.getProps();if(r&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(rp[t])return null;else return rp[t]=!0,()=>{rp[t]=!1};return rp.x||rp.y?null:(rp.x=rp.y=!0,()=>{rp.x=rp.y=!1})}(r),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rM(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:r}=this.visualElement;if(r&&r.layout){let n=r.layout.layoutBox[t];n&&(e=rw(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),i&&f.postRender(()=>i(t,e)),O(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:r,dragDirectionLock:n,onDirectionLock:i,onDrag:o}=this.getProps();if(!r&&!this.openDragLock)return;let{offset:s}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,r=null;return Math.abs(t.y)>e?r="y":Math.abs(t.x)>e&&(r="x"),r}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",e.point,s),this.updateAxis("y",e.point,s),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>rM(t=>{var e;return"paused"===this.getAnimationState(t)&&(null==(e=this.getAxisMotionValue(t).animation)?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,distanceThreshold:r,contextWindow:rU(this.visualElement)})}stop(t,e){let r=t||this.latestPointerEvent,n=e||this.latestPanInfo,i=this.isDragging;if(this.cancel(),!i||!n||!r)return;let{velocity:o}=n;this.startAnimation(o);let{onDragEnd:s}=this.getProps();s&&f.postRender(()=>s(r,n))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,r){let{drag:n}=this.getProps();if(!r||!r2(t,n,this.currentDirection))return;let i=this.getAxisMotionValue(t),o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=function(t,e,r){let{min:n,max:i}=e;return void 0!==n&&t<n?t=r?tS(n,t,r.min):Math.max(t,n):void 0!==i&&t>i&&(t=r?tS(i,t,r.max):Math.min(t,i)),t}(o,this.constraints[t],this.elastic[t])),i.set(o)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:r}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(t=this.visualElement.projection)?void 0:t.layout,i=this.constraints;e&&rz(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(t,e){let{top:r,left:n,bottom:i,right:o}=e;return{x:rK(t.x,n,o),y:rK(t.y,r,i)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.35;return!1===t?t=0:!0===t&&(t=.35),{x:rJ(t,"left","right"),y:rJ(t,"top","bottom")}}(r),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&rM(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let r={};return void 0!==e.min&&(r.min=e.min-t.min),void 0!==e.max&&(r.max=e.max-t.min),r}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:r}=this.getProps();if(!e||!rz(e))return!1;let n=e.current;U(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(t,e,r){let n=r_(t,r),{scroll:i}=e;return i&&(r$(n.x,i.offset.x),r$(n.y,i.offset.y)),n}(n,i.root,this.visualElement.getTransformPagePoint()),s=(t=i.layout.layoutBox,{x:rZ(t.x,o.x),y:rZ(t.y,o.y)});if(r){let t=r(function(t){let{x:e,y:r}=t;return{top:r.min,right:e.max,bottom:r.max,left:e.min}}(s));this.hasMutatedConstraints=!!t,t&&(s=rb(t))}return s}startAnimation(t){let{drag:e,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all(rM(s=>{if(!r2(s,e,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:r?t[s]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(t,e){let r=this.getAxisMotionValue(t);return O(this.visualElement,t),r.start(e8(t,r,0,e,this.visualElement,!1))}stopAnimation(){rM(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){rM(t=>{var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.pause()})}getAnimationState(t){var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.state}getAxisMotionValue(t){let e="_drag".concat(t.toUpperCase()),r=this.visualElement.getProps();return r[e]||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){rM(e=>{let{drag:r}=this.getProps();if(!r2(e,r,this.currentDirection))return;let{projection:n}=this.visualElement,i=this.getAxisMotionValue(e);if(n&&n.layout){let{min:r,max:o}=n.layout.layoutBox[e];i.set(t[e]-tS(r,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:r}=this.visualElement;if(!rz(e)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};rM(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let r=e.get();n[t]=function(t,e){let r=.5,n=rw(t),i=rw(e);return i>n?r=eo(e.min,e.max-n,t.min):n>i&&(r=eo(t.min,t.max-i,e.min)),F(0,1,r)}({min:r,max:r},this.constraints[t])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),rM(e=>{if(!r2(e,t,null))return;let r=this.getAxisMotionValue(e),{min:i,max:o}=this.constraints[e];r.set(tS(i,o,n[e]))})}addListeners(){if(!this.visualElement.current)return;r0.set(this.visualElement,this);let t=rv(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:r=!0}=this.getProps();e&&r&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();rz(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,n=r.addEventListener("measure",e);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),f.read(e);let i=rf(window,"resize",()=>this.scalePositionWithinConstraints()),o=r.addEventListener("didUpdate",t=>{let{delta:e,hasLayoutChanged:r}=t;this.isDragging&&r&&(rM(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),t(),n(),o&&o()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:s=!0}=t;return{...t,drag:e,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:o,dragMomentum:s}}constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=rR(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}}function r2(t,e,r){return(!0===e||e===t)&&(null===r||r===t)}class r3 extends ru{mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}constructor(t){super(t),this.removeGroupControls=u,this.removeListeners=u,this.controls=new r1(t)}}let r5=t=>(e,r)=>{t&&f.postRender(()=>t(e,r))};class r6 extends ru{onPointerDown(t){this.session=new rW(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rU(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:r5(t),onStart:r5(e),onMove:r,onEnd:(t,e)=>{delete this.session,n&&f.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=rv(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}constructor(){super(...arguments),this.removePointerDownListener=u}}var r8=r(5155);let{schedule:r4}=p(queueMicrotask,!1);var r9=r(2115),r7=r(2082),nt=r(869);let ne=(0,r9.createContext)({}),nr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nn(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let ni={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tu.test(t))return t;else t=parseFloat(t);let r=nn(t,e.target.x),n=nn(t,e.target.y);return"".concat(r,"% ").concat(n,"%")}},no={},ns=!1;class na extends r9.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=t;for(let t in nu)no[t]=nu[t],N(t)&&(no[t].isCSSVariable=!0);i&&(e.group&&e.group.add(i),r&&r.register&&n&&r.register(i),ns&&i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),nr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:r,drag:n,isPresent:i}=this.props,{projection:o}=r;return o&&(o.isPresent=i,ns=!0,n||t.layoutDependency!==e||void 0===e||t.isPresent!==i?o.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?o.promote():o.relegate()||f.postRender(()=>{let t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),r4.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nl(t){let[e,r]=(0,r7.xQ)(),n=(0,r9.useContext)(nt.L);return(0,r8.jsx)(na,{...t,layoutGroup:n,switchLayoutGroup:(0,r9.useContext)(ne),isPresent:e,safeToRemove:r})}let nu={borderRadius:{...ni,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ni,borderTopRightRadius:ni,borderBottomLeftRadius:ni,borderBottomRightRadius:ni,boxShadow:{correct:(t,e)=>{let{treeScale:r,projectionDelta:n}=e,i=tT.parse(t);if(i.length>5)return t;let o=tT.createTransformer(t),s=+("number"!=typeof i[0]),a=n.x.scale*r.x,l=n.y.scale*r.y;i[0+s]/=a,i[1+s]/=l;let u=tS(a,l,.5);return"number"==typeof i[2+s]&&(i[2+s]/=u),"number"==typeof i[3+s]&&(i[3+s]/=u),o(i)}}};var nc=r(6983);function nh(t){return(0,nc.G)(t)&&"ownerSVGElement"in t}let nd=(t,e)=>t.depth-e.depth;class np{add(t){x(this.children,t),this.isDirty=!0}remove(t){E(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nd),this.isDirty=!1,this.children.forEach(t)}constructor(){this.children=[],this.isDirty=!1}}function nf(t){return j(t)?t.get():t}let nm=["TopLeft","TopRight","BottomLeft","BottomRight"],ng=nm.length,ny=t=>"string"==typeof t?parseFloat(t):t,nv=t=>"number"==typeof t||tu.test(t);function nb(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nw=nE(0,.5,t7),nx=nE(.5,.95,u);function nE(t,e,r){return n=>n<t?0:n>e?1:r(eo(t,e,n))}function nT(t,e){t.min=e.min,t.max=e.max}function nC(t,e){nT(t.x,e.x),nT(t.y,e.y)}function nA(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nS(t,e,r,n,i){return t-=e,t=n+1/r*(t-n),void 0!==i&&(t=n+1/i*(t-n)),t}function nk(t,e,r,n,i){let[o,s,a]=r;!function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,i=arguments.length>4?arguments[4]:void 0,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:t,s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:t;if(tl.test(e)&&(e=parseFloat(e),e=tS(s.min,s.max,e/100)-s.min),"number"!=typeof e)return;let a=tS(o.min,o.max,n);t===o&&(a-=e),t.min=nS(t.min,e,r,a,i),t.max=nS(t.max,e,r,a,i)}(t,e[o],e[s],e[a],e.scale,n,i)}let nP=["x","scaleX","originX"],nR=["y","scaleY","originY"];function nM(t,e,r,n){nk(t.x,e,nP,r?r.x:void 0,n?n.x:void 0),nk(t.y,e,nR,r?r.y:void 0,n?n.y:void 0)}function nj(t){return 0===t.translate&&1===t.scale}function nO(t){return nj(t.x)&&nj(t.y)}function nI(t,e){return t.min===e.min&&t.max===e.max}function nD(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nV(t,e){return nD(t.x,e.x)&&nD(t.y,e.y)}function nq(t){return rw(t.x)/rw(t.y)}function nF(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class n${add(t){x(this.members,t),t.scheduleRender()}remove(t){if(E(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,r=this.members.findIndex(e=>t===e);if(0===r)return!1;for(let t=r;t>=0;t--){let r=this.members[t];if(!1!==r.isPresent){e=r;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,e&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:r}=t;e.onExitComplete&&e.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}constructor(){this.members=[]}}let nL={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nB=["","X","Y","Z"],n_=0;function nU(t,e,r,n){let{latestValues:i}=e;i[t]&&(r[t]=i[t],e.setStaticValue(t,0),n&&(n[t]=0))}function nz(t){let{attachResizeListener:e,defaultParent:r,measureScroll:n,checkIsScrollRoot:i,resetTransform:o}=t;return class{addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new T),this.eventHandlers.get(t).add(e)}notifyListeners(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];let i=this.eventHandlers.get(t);i&&i.notify(...r)}hasListeners(t){return this.eventHandlers.has(t)}mount(t){if(this.instance)return;this.isSVG=nh(t)&&!(nh(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:r,layout:n,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||r)&&(this.isLayoutDirty=!0),e){let r,n=0,i=()=>this.root.updateBlockedByResize=!1;f.read(()=>{n=window.innerWidth}),e(t,()=>{let t=window.innerWidth;t!==n&&(n=t,this.root.updateBlockedByResize=!0,r&&r(),r=function(t,e){let r=A.now(),n=i=>{let{timestamp:o}=i,s=o-r;s>=250&&(m(n),t(s-e))};return f.setup(n,!0),()=>m(n)}(i,250),nr.hasAnimatedSinceResize&&(nr.hasAnimatedSinceResize=!1,this.nodes.forEach(nJ)))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&i&&(r||n)&&this.addEventListener("didUpdate",t=>{let{delta:e,hasLayoutChanged:r,hasRelativeLayoutChanged:n,layout:o}=t;if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||i.getDefaultTransition()||n8,{onLayoutAnimationStart:a,onLayoutAnimationComplete:u}=i.getProps(),c=!this.targetLayout||!nV(this.targetLayout,o),h=!r&&n;if(this.options.layoutRoot||this.resumeFrom||h||r&&(c||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(s,"layout"),onPlay:a,onComplete:u};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,h)}else r||nJ(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=o})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n1),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:r}=e.options;if(!r)return;let n=r.props[D];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:r}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",f,!(t||r))}let{parent:i}=e;i&&!i.hasCheckedOptimisedAppear&&t(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:r}=this.options;if(void 0===e&&!r)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nX);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(nK);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(nZ),this.nodes.forEach(nN),this.nodes.forEach(nW)):this.nodes.forEach(nK),this.clearAllSnapshots();let t=A.now();g.delta=F(0,1e3/60,t-g.timestamp),g.timestamp=t,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,r4.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nG),this.sharedNodes.forEach(n2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,f.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){f.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||rw(this.snapshot.measuredBox.x)||rw(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=rR(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!o)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nO(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,i=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||rI(this.latestValues)||i)&&(o(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(){var t;let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],r=this.measurePageBox(),n=this.removeElementScroll(r);return e&&(n=this.removeTransform(n)),n7((t=n).x),n7(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return rR();let r=e.measureViewportBox();if(!((null==(t=this.scroll)?void 0:t.wasRoot)||this.path.some(ie))){let{scroll:t}=this.root;t&&(r$(r.x,t.offset.x),r$(r.y,t.offset.y))}return r}removeElementScroll(t){var e;let r=rR();if(nC(r,t),null==(e=this.scroll)?void 0:e.wasRoot)return r;for(let e=0;e<this.path.length;e++){let n=this.path[e],{scroll:i,options:o}=n;n!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&nC(r,t),r$(r.x,i.offset.x),r$(r.y,i.offset.y))}return r}applyTransform(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=rR();nC(r,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&rB(r,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),rI(n.latestValues)&&rB(r,n.latestValues)}return rI(this.latestValues)&&rB(r,this.latestValues),r}removeTransform(t){let e=rR();nC(e,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];if(!r.instance||!rI(r.latestValues))continue;rO(r.latestValues)&&r.updateSnapshot();let n=rR();nC(n,r.measurePageBox()),nM(e,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,n)}return rI(this.latestValues)&&nM(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(){var t,e,r,n;let i=arguments.length>0&&void 0!==arguments[0]&&arguments[0],o=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=o.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=o.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=o.isSharedProjectionDirty);let s=!!this.resumingFrom||this!==o;if(!(i||s&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rR(),this.relativeTargetOrigin=rR(),rA(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nC(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=rR(),this.targetWithTransforms=rR()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),e=this.target,r=this.relativeTarget,n=this.relativeParent.target,rT(e.x,r.x,n.x),rT(e.y,r.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nC(this.target,this.layout.layoutBox),rF(this.target,this.targetDelta)):nC(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rR(),this.relativeTargetOrigin=rR(),rA(this.relativeTargetOrigin,this.target,t.target),nC(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}d.value&&nL.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||rO(this.parent.latestValues)||rD(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),r=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty))&&(n=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===g.timestamp&&(n=!1),n)return;let{layout:i,layoutId:o}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||o))return;nC(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;!function(t,e,r){let n,i,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3],s=r.length;if(s){e.x=e.y=1;for(let a=0;a<s;a++){i=(n=r[a]).projectionDelta;let{visualElement:s}=n.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(o&&n.options.layoutScroll&&n.scroll&&n!==n.root&&rB(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),i&&(e.x*=i.x.scale,e.y*=i.y.scale,rF(t,i)),o&&rI(n.latestValues)&&rB(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,r),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=rR());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nA(this.prevProjectionDelta.x,this.projectionDelta.x),nA(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rE(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&nF(this.projectionDelta.x,this.prevProjectionDelta.x)&&nF(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),d.value&&nL.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){var t;let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(null==(t=this.options.visualElement)||t.scheduleRender(),e){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=rk(),this.projectionDelta=rk(),this.projectionDeltaWithTransform=rk()}setAnimationOrigin(t){let e,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.snapshot,i=n?n.latestValues:{},o={...this.latestValues},s=rk();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!r;let a=rR(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,h=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(n6));this.animationProgress=0,this.mixTargetDelta=r=>{let n=r/1e3;if(n3(s.x,t.x,n),n3(s.y,t.y,n),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,f,m,g;rA(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,f=this.relativeTargetOrigin,m=a,g=n,n5(p.x,f.x,m.x,g),n5(p.y,f.y,m.y,g),e&&(u=this.relativeTarget,d=e,nI(u.x,d.x)&&nI(u.y,d.y))&&(this.isProjectionDirty=!1),e||(e=rR()),nC(e,this.relativeTarget)}l&&(this.animationValues=o,function(t,e,r,n,i,o){var s,a,l,u;i?(t.opacity=tS(0,null!=(s=r.opacity)?s:1,nw(n)),t.opacityExit=tS(null!=(a=e.opacity)?a:1,0,nx(n))):o&&(t.opacity=tS(null!=(l=e.opacity)?l:1,null!=(u=r.opacity)?u:1,n));for(let i=0;i<ng;i++){let o="border".concat(nm[i],"Radius"),s=nb(e,o),a=nb(r,o);(void 0!==s||void 0!==a)&&(s||(s=0),a||(a=0),0===s||0===a||nv(s)===nv(a)?(t[o]=Math.max(tS(ny(s),ny(a),n),0),(tl.test(a)||tl.test(s))&&(t[o]+="%")):t[o]=a)}(e.rotate||r.rotate)&&(t.rotate=tS(e.rotate||0,r.rotate||0,n))}(o,i,this.latestValues,n,h,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){var e,r,n;this.notifyListeners("animationStart"),null==(e=this.currentAnimation)||e.stop(),null==(n=this.resumingFrom)||null==(r=n.currentAnimation)||r.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=f.update(()=>{nr.hasAnimatedSinceResize=!0,B.layout++,this.motionValue||(this.motionValue=R(0)),this.currentAnimation=function(t,e,r){let n=j(t)?t:R(t);return n.start(e8("",n,e,r)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{B.layout--},onComplete:()=>{B.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:r,layout:n,latestValues:i}=t;if(e&&r&&n){if(this!==t&&this.layout&&n&&it(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||rR();let e=rw(this.layout.layoutBox.x);r.x.min=t.target.x.min,r.x.max=r.x.min+e;let n=rw(this.layout.layoutBox.y);r.y.min=t.target.y.min,r.y.max=r.y.min+n}nC(e,r),rB(e,i),rE(this.projectionDeltaWithTransform,this.layoutCorrected,e,i)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new n$),this.sharedNodes.get(t).add(e);let r=e.options.initialPromotionConfig;e.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null==(t=this.getStack())?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null==(t=this.getStack())?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote(){let{needsReset:t,transition:e,preserveFollowOpacity:r}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=this.getStack();n&&n.promote(this,r),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:r}=t;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(e=!0),!e)return;let n={};r.z&&nU("z",t,n,this.animationValues);for(let e=0;e<nB.length;e++)nU("rotate".concat(nB[e]),t,n,this.animationValues),nU("skew".concat(nB[e]),t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let r=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=nf(null==e?void 0:e.pointerEvents)||"",t.transform=r?r(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=nf(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!rI(this.latestValues)&&(t.transform=r?r({},""):"none",this.hasProjected=!1);return}t.visibility="";let i=n.animationValues||n.latestValues;this.applyTransformsToTarget();let o=function(t,e,r){let n="",i=t.x.translate/e.x,o=t.y.translate/e.y,s=(null==r?void 0:r.z)||0;if((i||o||s)&&(n="translate3d(".concat(i,"px, ").concat(o,"px, ").concat(s,"px) ")),(1!==e.x||1!==e.y)&&(n+="scale(".concat(1/e.x,", ").concat(1/e.y,") ")),r){let{transformPerspective:t,rotate:e,rotateX:i,rotateY:o,skewX:s,skewY:a}=r;t&&(n="perspective(".concat(t,"px) ").concat(n)),e&&(n+="rotate(".concat(e,"deg) ")),i&&(n+="rotateX(".concat(i,"deg) ")),o&&(n+="rotateY(".concat(o,"deg) ")),s&&(n+="skewX(".concat(s,"deg) ")),a&&(n+="skewY(".concat(a,"deg) "))}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+="scale(".concat(a,", ").concat(l,")")),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,i);r&&(o=r(i,o)),t.transform=o;let{x:s,y:a}=this.projectionDelta;if(t.transformOrigin="".concat(100*s.origin,"% ").concat(100*a.origin,"% 0"),n.animationValues){var l,u;t.opacity=n===this?null!=(u=null!=(l=i.opacity)?l:this.latestValues.opacity)?u:1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit}else t.opacity=n===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0;for(let e in no){if(void 0===i[e])continue;let{correct:r,applyTo:s,isCSSVariable:a}=no[e],l="none"===o?i[e]:r(i[e],n);if(s){let e=s.length;for(let r=0;r<e;r++)t[s[r]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=n===this?nf(null==e?void 0:e.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null==(e=t.currentAnimation)?void 0:e.stop()}),this.root.nodes.forEach(nX),this.root.sharedNodes.clear()}constructor(t={},e=null==r?void 0:r()){this.id=n_++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,d.value&&(nL.nodes=nL.calculatedTargetDeltas=nL.calculatedProjections=0),this.nodes.forEach(nH),this.nodes.forEach(nQ),this.nodes.forEach(n0),this.nodes.forEach(nY),d.addProjectionMetrics&&d.addProjectionMetrics(nL)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=e?e.root||e:this,this.path=e?[...e.path,e]:[],this.parent=e,this.depth=e?e.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new np)}}}function nN(t){t.updateLayout()}function nW(t){var e;let r=(null==(e=t.resumeFrom)?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&r&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:n}=t.layout,{animationType:i}=t.options,o=r.source!==t.layout.source;"size"===i?rM(t=>{let n=o?r.measuredBox[t]:r.layoutBox[t],i=rw(n);n.min=e[t].min,n.max=n.min+i}):it(i,r.layoutBox,e)&&rM(n=>{let i=o?r.measuredBox[n]:r.layoutBox[n],s=rw(e[n]);i.max=i.min+s,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+s)});let s=rk();rE(s,e,r.layoutBox);let a=rk();o?rE(a,t.applyTransform(n,!0),r.measuredBox):rE(a,e,r.layoutBox);let l=!nO(s),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:i,layout:o}=n;if(i&&o){let s=rR();rA(s,r.layoutBox,i.layoutBox);let a=rR();rA(a,e,o.layoutBox),nV(s,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=s,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:r,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nH(t){d.value&&nL.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nY(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function nG(t){t.clearSnapshot()}function nX(t){t.clearMeasurements()}function nK(t){t.isLayoutDirty=!1}function nZ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function nJ(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function nQ(t){t.resolveTargetDelta()}function n0(t){t.calcProjection()}function n1(t){t.resetSkewAndRotation()}function n2(t){t.removeLeadSnapshot()}function n3(t,e,r){t.translate=tS(e.translate,0,r),t.scale=tS(e.scale,1,r),t.origin=e.origin,t.originPoint=e.originPoint}function n5(t,e,r,n){t.min=tS(e.min,r.min,n),t.max=tS(e.max,r.max,n)}function n6(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let n8={duration:.45,ease:[.4,0,.1,1]},n4=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),n9=n4("applewebkit/")&&!n4("chrome/")?Math.round:u;function n7(t){t.min=n9(t.min),t.max=n9(t.max)}function it(t,e,r){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nq(e)-nq(r)))}function ie(t){var e;return t!==t.root&&(null==(e=t.scroll)?void 0:e.wasRoot)}let ir=nz({attachResizeListener:(t,e)=>rf(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ii={current:void 0},io=nz({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ii.current){let t=new ir({});t.mount(window),t.setOptions({layoutScroll:!0}),ii.current=t}return ii.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function is(t,e){let r=function(t,e,r){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,r=(void 0)??e.querySelectorAll(t);return r?Array.from(r):[]}return Array.from(t)}(t),n=new AbortController;return[r,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function ia(t){return!("touch"===t.pointerType||rp.x||rp.y)}function il(t,e,r){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===r);let i=n["onHover"+r];i&&f.postRender(()=>i(e,rg(e)))}class iu extends ru{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,r={}){let[n,i,o]=is(t,r),s=t=>{if(!ia(t))return;let{target:r}=t,n=e(r,t);if("function"!=typeof n||!r)return;let o=t=>{ia(t)&&(n(t),r.removeEventListener("pointerleave",o))};r.addEventListener("pointerleave",o,i)};return n.forEach(t=>{t.addEventListener("pointerenter",s,i)}),o}(t,(t,e)=>(il(this.node,e,"Start"),t=>il(this.node,t,"End"))))}unmount(){}}class ic extends ru{onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=q(rf(this.node.current,"focus",()=>this.onFocus()),rf(this.node.current,"blur",()=>this.onBlur()))}unmount(){}constructor(){super(...arguments),this.isActive=!1}}let ih=(t,e)=>!!e&&(t===e||ih(t,e.parentElement)),id=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ip=new WeakSet;function im(t){return e=>{"Enter"===e.key&&t(e)}}function ig(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let iy=(t,e)=>{let r=t.currentTarget;if(!r)return;let n=im(()=>{if(ip.has(r))return;ig(r,"down");let t=im(()=>{ig(r,"up")});r.addEventListener("keyup",t,e),r.addEventListener("blur",()=>ig(r,"cancel"),e)});r.addEventListener("keydown",n,e),r.addEventListener("blur",()=>r.removeEventListener("keydown",n),e)};function iv(t){return rm(t)&&!(rp.x||rp.y)}function ib(t,e,r){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===r);let i=n["onTap"+("End"===r?"":r)];i&&f.postRender(()=>i(e,rg(e)))}class iw extends ru{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,r={}){let[n,i,o]=is(t,r),s=t=>{let n=t.currentTarget;if(!iv(t))return;ip.add(n);let o=e(n,t),s=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),ip.has(n)&&ip.delete(n),iv(t)&&"function"==typeof o&&o(t,{success:e})},a=t=>{s(t,n===window||n===document||r.useGlobalTarget||ih(n,t.target))},l=t=>{s(t,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",l,i)};return n.forEach(t=>{((r.useGlobalTarget?window:t).addEventListener("pointerdown",s,i),(0,eK.s)(t))&&(t.addEventListener("focus",t=>iy(t,i)),id.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),o}(t,(t,e)=>(ib(this.node,e,"Start"),(t,e)=>{let{success:r}=e;return ib(this.node,t,r?"End":"Cancel")}),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let ix=new WeakMap,iE=new WeakMap,iT=t=>{let e=ix.get(t.target);e&&e(t)},iC=t=>{t.forEach(iT)},iA={some:0,all:1};class iS extends ru{startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:r,amount:n="some",once:i}=t,o={root:e?e.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:iA[n]};return function(t,e,r){let n=function(t){let{root:e,...r}=t,n=e||document;iE.has(n)||iE.set(n,{});let i=iE.get(n),o=JSON.stringify(r);return i[o]||(i[o]=new IntersectionObserver(iC,{root:e,...r})),i[o]}(e);return ix.set(t,r),n.observe(t),()=>{ix.delete(t),n.unobserve(t)}}(this.node.current,o,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,i&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),o=e?r:n;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function(t){let{viewport:e={}}=t,{viewport:r={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=>e[t]!==r[t]}(t,e))&&this.startObserver()}unmount(){}constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}}let ik=(0,r9.createContext)({strict:!1});var iP=r(1508);let iR=(0,r9.createContext)({});function iM(t){return i(t.animate)||rn.some(e=>re(t[e]))}function ij(t){return!!(iM(t)||t.variants)}function iO(t){return Array.isArray(t)?t.join(" "):t}var iI=r(8972);let iD={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iV={};for(let t in iD)iV[t]={isEnabled:e=>iD[t].some(t=>!!e[t])};let iq=Symbol.for("motionComponentSymbol");var iF=r(845),i$=r(7494);function iL(t,e){let{layout:r,layoutId:n}=e;return b.has(t)||t.startsWith("origin")||(r||void 0!==n)&&(!!no[t]||"opacity"===t)}let iB=(t,e)=>e&&"number"==typeof t?e.transform(t):t,i_={...G,transform:Math.round},iU={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:ta,rotateX:ta,rotateY:ta,rotateZ:ta,scale:K,scaleX:K,scaleY:K,scaleZ:K,skew:ta,skewX:ta,skewY:ta,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:X,originX:td,originY:td,originZ:tu,zIndex:i_,fillOpacity:X,strokeOpacity:X,numOctaves:i_},iz={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iN=v.length;function iW(t,e,r){let{style:n,vars:i,transformOrigin:o}=t,s=!1,a=!1;for(let t in e){let r=e[t];if(b.has(t)){s=!0;continue}if(N(t)){i[t]=r;continue}{let e=iB(r,iU[t]);t.startsWith("origin")?(a=!0,o[t]=e):n[t]=e}}if(!e.transform&&(s||r?n.transform=function(t,e,r){let n="",i=!0;for(let o=0;o<iN;o++){let s=v[o],a=t[s];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!s.startsWith("scale"):0===parseFloat(a))||r){let t=iB(a,iU[s]);if(!l){i=!1;let e=iz[s]||s;n+="".concat(e,"(").concat(t,") ")}r&&(e[s]=t)}}return n=n.trim(),r?n=r(e,i?"":n):i&&(n="none"),n}(e,t.transform,r):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:r=0}=o;n.transformOrigin="".concat(t," ").concat(e," ").concat(r)}}let iH=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iY(t,e,r){for(let n in e)j(e[n])||iL(n,r)||(t[n]=e[n])}let iG={offset:"stroke-dashoffset",array:"stroke-dasharray"},iX={offset:"strokeDashoffset",array:"strokeDasharray"};function iK(t,e,r,n,i){var o,s;let{attrX:a,attrY:l,attrScale:u,pathLength:c,pathSpacing:h=1,pathOffset:d=0,...p}=e;if(iW(t,p,n),r){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:f,style:m}=t;f.transform&&(m.transform=f.transform,delete f.transform),(m.transform||f.transformOrigin)&&(m.transformOrigin=null!=(o=f.transformOrigin)?o:"50% 50%",delete f.transformOrigin),m.transform&&(m.transformBox=null!=(s=null==i?void 0:i.transformBox)?s:"fill-box",delete f.transformBox),void 0!==a&&(f.x=a),void 0!==l&&(f.y=l),void 0!==u&&(f.scale=u),void 0!==c&&function(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=!(arguments.length>4)||void 0===arguments[4]||arguments[4];t.pathLength=1;let o=i?iG:iX;t[o.offset]=tu.transform(-n);let s=tu.transform(e),a=tu.transform(r);t[o.array]="".concat(s," ").concat(a)}(f,c,h,d,!1)}let iZ=()=>({...iH(),attrs:{}}),iJ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),iQ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function i0(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||iQ.has(t)}let i1=t=>!i0(t);try{!function(t){"function"==typeof t&&(i1=e=>e.startsWith("on")?!i0(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}let i2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i3(t){if("string"!=typeof t||t.includes("-"));else if(i2.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var i5=r(2885);let i6=t=>(e,r)=>{let n=(0,r9.useContext)(iR),o=(0,r9.useContext)(iF.t),a=()=>(function(t,e,r,n){let{scrapeMotionValuesFromProps:o,createRenderState:a}=t;return{latestValues:function(t,e,r,n){let o={},a=n(t,{});for(let t in a)o[t]=nf(a[t]);let{initial:l,animate:u}=t,c=iM(t),h=ij(t);e&&h&&!c&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let d=!!r&&!1===r.initial,p=(d=d||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!i(p)){let e=Array.isArray(p)?p:[p];for(let r=0;r<e.length;r++){let n=s(t,e[r]);if(n){let{transitionEnd:t,transition:e,...r}=n;for(let t in r){let e=r[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(o[t]=e)}for(let e in t)o[e]=t[e]}}}return o}(e,r,n,o),renderState:a()}})(t,e,n,o);return r?a():(0,i5.M)(a)};function i8(t,e,r){let{style:n}=t,i={};for(let s in n){var o;(j(n[s])||e.style&&j(e.style[s])||iL(s,t)||(null==r||null==(o=r.getValue(s))?void 0:o.liveStyle)!==void 0)&&(i[s]=n[s])}return i}let i4={useVisualState:i6({scrapeMotionValuesFromProps:i8,createRenderState:iH})};function i9(t,e,r){let n=i8(t,e,r);for(let r in t)(j(t[r])||j(e[r]))&&(n[-1!==v.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=t[r]);return n}let i7={useVisualState:i6({scrapeMotionValuesFromProps:i9,createRenderState:iZ})},ot=t=>e=>e.test(t),oe=[G,tu,tl,ta,th,tc,{test:t=>"auto"===t,parse:t=>t}],or=t=>oe.find(ot(t)),on=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),oi=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,oo=t=>/^0[^.\s]+$/u.test(t),os=new Set(["brightness","contrast","saturate","opacity"]);function oa(t){let[e,r]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=r.match(J)||[];if(!n)return t;let i=r.replace(n,""),o=+!!os.has(e);return n!==r&&(o*=100),e+"("+o+i+")"}let ol=/\b([a-z-]*)\(.*?\)/gu,ou={...tT,getAnimatableNone:t=>{let e=t.match(ol);return e?e.map(oa).join(" "):t}},oc={...iU,color:tf,backgroundColor:tf,outlineColor:tf,fill:tf,stroke:tf,borderColor:tf,borderTopColor:tf,borderRightColor:tf,borderBottomColor:tf,borderLeftColor:tf,filter:ou,WebkitFilter:ou},oh=t=>oc[t];function od(t,e){let r=oh(t);return r!==ou&&(r=tT),r.getAnimatableNone?r.getAnimatableNone(e):void 0}let op=new Set(["auto","none","0"]);class of extends eV{constructor(t,e,r,n,i){super(t,e,r,n,i,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:r}=this;if(!e||!e.current)return;super.readKeyframes();for(let r=0;r<t.length;r++){let n=t[r];if("string"==typeof n&&H(n=n.trim())){let i=function t(e,r,n=1){U(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[i,o]=function(t){let e=oi.exec(t);if(!e)return[,];let[,r,n,i]=e;return[`--${r??n}`,i]}(e);if(!i)return;let s=window.getComputedStyle(r).getPropertyValue(i);if(s){let t=s.trim();return on(t)?parseFloat(t):t}return H(o)?t(o,r,n+1):o}(n,e.current);void 0!==i&&(t[r]=i),r===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!w.has(r)||2!==t.length)return;let[n,i]=t,o=or(n),s=or(i);if(o!==s)if(eA(o)&&eA(s))for(let e=0;e<t.length;e++){let r=t[e];"string"==typeof r&&(t[e]=parseFloat(r))}else eP[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,r=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||oo(n)))&&r.push(e)}r.length&&function(t,e,r){let n,i=0;for(;i<t.length&&!n;){let e=t[i];"string"==typeof e&&!op.has(e)&&tb(e).values.length&&(n=t[i]),i++}if(n&&r)for(let i of e)t[i]=od(r,n)}(t,r,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:r}=this;if(!t||!t.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eP[r](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(r,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:r}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let i=r.length-1,o=r[i];r[i]=eP[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,r])=>{t.getValue(e).set(r)}),this.resolveNoneKeyframes()}}let om=[...oe,tf,tT],og=t=>om.find(ot(t)),oy={current:null},ov={current:!1},ob=new WeakMap,ow=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ox{scrapeMotionValuesFromProps(t,e,r){return{}}mount(t){this.current=t,ob.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),ov.current||function(){if(ov.current=!0,iI.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>oy.current=t.matches;t.addEventListener("change",e),e()}else oy.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||oy.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let r;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=b.has(t);n&&this.onBindTransform&&this.onBindTransform();let i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&f.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),o=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{i(),o(),r&&r(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in iV){let e=iV[t];if(!e)continue;let{isEnabled:r,Feature:n}=e;if(!this.features[t]&&n&&r(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):rR()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<ow.length;e++){let r=ow[e];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let n=t["on"+r];n&&(this.propEventSubscriptions[r]=this.on(r,n))}this.prevMotionValues=function(t,e,r){for(let n in e){let i=e[n],o=r[n];if(j(i))t.addValue(n,i);else if(j(o))t.addValue(n,R(i,{owner:t}));else if(o!==i)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(i):e.hasAnimated||e.set(i)}else{let e=t.getStaticValue(n);t.addValue(n,R(void 0!==e?e:i,{owner:t}))}}for(let n in r)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let r=this.values.get(t);e!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return void 0===r&&void 0!==e&&(r=R(null===e?void 0:e,{owner:this}),this.addValue(t,r)),r}readValue(t,e){var r;let n=void 0===this.latestValues[t]&&this.current?null!=(r=this.getBaseTargetFromProps(this.props,t))?r:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&(on(n)||oo(n))?n=parseFloat(n):!og(n)&&tT.test(e)&&(n=od(t,e)),this.setBaseTarget(t,j(n)?n.get():n)),j(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){var n;let i=s(this.props,r,null==(n=this.presenceContext)?void 0:n.custom);i&&(e=i[t])}if(r&&void 0!==e)return e;let i=this.getBaseTargetFromProps(this.props,t);return void 0===i||j(i)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:i}on(t,e){return this.events[t]||(this.events[t]=new T),this.events[t].add(e)}notify(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];this.events[t]&&this.events[t].notify(...r)}constructor({parent:t,props:e,presenceContext:r,reducedMotionConfig:n,blockInitialAnimation:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eV,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=A.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,f.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=s,this.blockInitialAnimation=!!i,this.isControllingVariants=iM(e),this.isVariantNode=ij(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in c){let e=c[t];void 0!==a[t]&&j(e)&&e.set(a[t],!1)}}}class oE extends ox{sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,e){let{vars:r,style:n}=e;delete r[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;j(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent="".concat(t))}))}constructor(){super(...arguments),this.KeyframeResolver=of}}function oT(t,e,r,n){let i,{style:o,vars:s}=e,a=t.style;for(i in o)a[i]=o[i];for(i in null==n||n.applyProjectionStyles(a,r),s)a.setProperty(i,s[i])}class oC extends oE{readValueFromInstance(t,e){var r;if(b.has(e))return(null==(r=this.projection)?void 0:r.isProjecting)?ex(e):eT(t,e);{let r=window.getComputedStyle(t),n=(N(e)?r.getPropertyValue(e):r[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,e){let{transformPagePoint:r}=e;return r_(t,r)}build(t,e,r){iW(t,e,r.transformTemplate)}scrapeMotionValuesFromProps(t,e,r){return i8(t,e,r)}constructor(){super(...arguments),this.type="html",this.renderInstance=oT}}let oA=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class oS extends oE{getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(b.has(e)){let t=oh(e);return t&&t.default||0}return e=oA.has(e)?e:I(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,r){return i9(t,e,r)}build(t,e,r){iK(t,e,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(t,e,r,n){for(let r in oT(t,e,void 0,n),e.attrs)t.setAttribute(oA.has(r)?r:I(r),e.attrs[r])}mount(t){this.isSVGTag=iJ(t.tagName),super.mount(t)}constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=rR}}let ok=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy(function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return t(...r)},{get:(r,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((eG={animation:{Feature:rc},exit:{Feature:rd},inView:{Feature:iS},tap:{Feature:iw},focus:{Feature:ic},hover:{Feature:iu},pan:{Feature:r6},drag:{Feature:r3,ProjectionNode:io,MeasureLayout:nl},layout:{ProjectionNode:io,MeasureLayout:nl}},eX=(t,e)=>i3(t)?new oS(e):new oC(e,{allowProjection:t!==r9.Fragment}),function(t){let{forwardMotionProps:e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{forwardMotionProps:!1};return function(t){var e,r;let{preloadedFeatures:n,createVisualElement:i,useRender:o,useVisualState:s,Component:a}=t;function l(t,e){var r,n,l;let u,c={...(0,r9.useContext)(iP.Q),...t,layoutId:function(t){let{layoutId:e}=t,r=(0,r9.useContext)(nt.L).id;return r&&void 0!==e?r+"-"+e:e}(t)},{isStatic:h}=c,d=function(t){let{initial:e,animate:r}=function(t,e){if(iM(t)){let{initial:e,animate:r}=t;return{initial:!1===e||re(e)?e:void 0,animate:re(r)?r:void 0}}return!1!==t.inherit?e:{}}(t,(0,r9.useContext)(iR));return(0,r9.useMemo)(()=>({initial:e,animate:r}),[iO(e),iO(r)])}(t),p=s(t,h);if(!h&&iI.B){n=0,l=0,(0,r9.useContext)(ik).strict;let t=function(t){let{drag:e,layout:r}=iV;if(!e&&!r)return{};let n={...e,...r};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==r?void 0:r.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(c);u=t.MeasureLayout,d.visualElement=function(t,e,r,n,i){var o,s,a,l;let{visualElement:u}=(0,r9.useContext)(iR),c=(0,r9.useContext)(ik),h=(0,r9.useContext)(iF.t),d=(0,r9.useContext)(iP.Q).reducedMotion,p=(0,r9.useRef)(null);n=n||c.renderer,!p.current&&n&&(p.current=n(t,{visualState:e,parent:u,props:r,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:d}));let f=p.current,m=(0,r9.useContext)(ne);f&&!f.projection&&i&&("html"===f.type||"svg"===f.type)&&function(t,e,r,n){let{layoutId:i,layout:o,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=e;t.projection=new r(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!s||a&&rz(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:n,crossfade:c,layoutScroll:l,layoutRoot:u})}(p.current,r,i,m);let g=(0,r9.useRef)(!1);(0,r9.useInsertionEffect)(()=>{f&&g.current&&f.update(r,h)});let y=r[D],v=(0,r9.useRef)(!!y&&!(null==(o=(s=window).MotionHandoffIsComplete)?void 0:o.call(s,y))&&(null==(a=(l=window).MotionHasOptimisedAnimation)?void 0:a.call(l,y)));return(0,i$.E)(()=>{f&&(g.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),r4.render(f.render),v.current&&f.animationState&&f.animationState.animateChanges())}),(0,r9.useEffect)(()=>{f&&(!v.current&&f.animationState&&f.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var t,e;null==(t=(e=window).MotionHandoffMarkAsComplete)||t.call(e,y)}),v.current=!1))}),f}(a,p,c,i,t.ProjectionNode)}return(0,r8.jsxs)(iR.Provider,{value:d,children:[u&&d.visualElement?(0,r8.jsx)(u,{visualElement:d.visualElement,...c}):null,o(a,t,(r=d.visualElement,(0,r9.useCallback)(t=>{t&&p.onMount&&p.onMount(t),r&&(t?r.mount(t):r.unmount()),e&&("function"==typeof e?e(t):rz(e)&&(e.current=t))},[r])),p,h,d.visualElement)]})}n&&function(t){for(let e in t)iV[e]={...iV[e],...t[e]}}(n),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(r=null!=(e=a.displayName)?e:a.name)?r:"",")"));let u=(0,r9.forwardRef)(l);return u[iq]=a,u}({...i3(t)?i7:i4,preloadedFeatures:eG,useRender:function(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(e,r,n,i,o)=>{let{latestValues:s}=i,a=(i3(e)?function(t,e,r,n){let i=(0,r9.useMemo)(()=>{let r=iZ();return iK(r,e,iJ(n),t.transformTemplate,t.style),{...r.attrs,style:{...r.style}}},[e]);if(t.style){let e={};iY(e,t.style,t),i.style={...e,...i.style}}return i}:function(t,e){let r={},n=function(t,e){let r=t.style||{},n={};return iY(n,r,t),Object.assign(n,function(t,e){let{transformTemplate:r}=t;return(0,r9.useMemo)(()=>{let t=iH();return iW(t,e,r),Object.assign({},t.vars,t.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":"pan-".concat("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(r.tabIndex=0),r.style=n,r})(r,s,o,e),l=function(t,e,r){let n={};for(let i in t)("values"!==i||"object"!=typeof t.values)&&(i1(i)||!0===r&&i0(i)||!e&&!i0(i)||t.draggable&&i.startsWith("onDrag"))&&(n[i]=t[i]);return n}(r,"string"==typeof e,t),u=e!==r9.Fragment?{...l,...a,ref:n}:{},{children:c}=r,h=(0,r9.useMemo)(()=>j(c)?c.get():c,[c]);return(0,r9.createElement)(e,{...u,children:h})}}(e),createVisualElement:eX,Component:t})}))},6983:(t,e,r)=>{"use strict";function n(t){return"object"==typeof t&&null!==t}r.d(e,{G:()=>n})},7351:(t,e,r)=>{"use strict";r.d(e,{s:()=>i});var n=r(6983);function i(t){return(0,n.G)(t)&&"offsetHeight"in t}},7494:(t,e,r)=>{"use strict";r.d(e,{E:()=>i});var n=r(2115);let i=r(8972).B?n.useLayoutEffect:n.useEffect},8972:(t,e,r)=>{"use strict";r.d(e,{B:()=>n});let n="undefined"!=typeof window},9641:t=>{!function(){var e={675:function(t,e){"use strict";e.byteLength=function(t){var e=l(t),r=e[0],n=e[1];return(r+n)*3/4-n},e.toByteArray=function(t){var e,r,o=l(t),s=o[0],a=o[1],u=new i((s+a)*3/4-a),c=0,h=a>0?s-4:s;for(r=0;r<h;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],u[c++]=e>>16&255,u[c++]=e>>8&255,u[c++]=255&e;return 2===a&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,u[c++]=255&e),1===a&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,u[c++]=e>>8&255,u[c++]=255&e),u},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,o=[],s=0,a=n-i;s<a;s+=16383)o.push(function(t,e,n){for(var i,o=[],s=e;s<n;s+=3)i=(t[s]<<16&0xff0000)+(t[s+1]<<8&65280)+(255&t[s+2]),o.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return o.join("")}(t,s,s+16383>a?a:s+16383));return 1===i?o.push(r[(e=t[n-1])>>2]+r[e<<4&63]+"=="):2===i&&o.push(r[(e=(t[n-2]<<8)+t[n-1])>>10]+r[e>>4&63]+r[e<<2&63]+"="),o.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,a=o.length;s<a;++s)r[s]=o[s],n[o.charCodeAt(s)]=s;function l(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var n=r===e?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},72:function(t,e,r){"use strict";var n=r(675),i=r(783),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function s(t){if(t>0x7fffffff)throw RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,a.prototype),e}function a(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return c(t)}return l(t,e,r)}function l(t,e,r){if("string"==typeof t){var n=t,i=e;if(("string"!=typeof i||""===i)&&(i="utf8"),!a.isEncoding(i))throw TypeError("Unknown encoding: "+i);var o=0|p(n,i),l=s(o),u=l.write(n,i);return u!==o&&(l=l.slice(0,u)),l}if(ArrayBuffer.isView(t))return h(t);if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(R(t,ArrayBuffer)||t&&R(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(R(t,SharedArrayBuffer)||t&&R(t.buffer,SharedArrayBuffer)))return function(t,e,r){var n;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),a.prototype),n}(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');var c=t.valueOf&&t.valueOf();if(null!=c&&c!==t)return a.from(c,e,r);var f=function(t){if(a.isBuffer(t)){var e=0|d(t.length),r=s(e);return 0===r.length||t.copy(r,0,0,e),r}return void 0!==t.length?"number"!=typeof t.length||function(t){return t!=t}(t.length)?s(0):h(t):"Buffer"===t.type&&Array.isArray(t.data)?h(t.data):void 0}(t);if(f)return f;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return a.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function u(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function c(t){return u(t),s(t<0?0:0|d(t))}function h(t){for(var e=t.length<0?0:0|d(t.length),r=s(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}e.Buffer=a,e.SlowBuffer=function(t){return+t!=t&&(t=0),a.alloc(+t)},e.INSPECT_MAX_BYTES=50,e.kMaxLength=0x7fffffff,a.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(t,e,r){return l(t,e,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(t,e,r){return(u(t),t<=0)?s(t):void 0!==e?"string"==typeof r?s(t).fill(e,r):s(t).fill(e):s(t)},a.allocUnsafe=function(t){return c(t)},a.allocUnsafeSlow=function(t){return c(t)};function d(t){if(t>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function p(t,e){if(a.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||R(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return A(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return k(t).length;default:if(i)return n?-1:A(t).length;e=(""+e).toLowerCase(),i=!0}}function f(t,e,r){var i,o,s,a=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=e;o<r;++o)i+=M[t[o]];return i}(this,e,r);case"utf8":case"utf-8":return v(this,e,r);case"ascii":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}(this,e,r);case"latin1":case"binary":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}(this,e,r);case"base64":return i=this,o=e,s=r,0===o&&s===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(o,s));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}(this,e,r);default:if(a)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),a=!0}}function m(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function g(t,e,r,n,i){var o;if(0===t.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(o=r*=1)!=o&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length)if(i)return -1;else r=t.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof e&&(e=a.from(e,n)),a.isBuffer(e))return 0===e.length?-1:y(t,e,r,n,i);if("number"==typeof e){if(e&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(t,e,r);else return Uint8Array.prototype.lastIndexOf.call(t,e,r);return y(t,[e],r,n,i)}throw TypeError("val must be string, number or Buffer")}function y(t,e,r,n,i){var o,s=1,a=t.length,l=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return -1;s=2,a/=2,l/=2,r/=2}function u(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){var c=-1;for(o=r;o<a;o++)if(u(t,o)===u(e,-1===c?0:o-c)){if(-1===c&&(c=o),o-c+1===l)return c*s}else -1!==c&&(o-=o-c),c=-1}else for(r+l>a&&(r=a-l),o=r;o>=0;o--){for(var h=!0,d=0;d<l;d++)if(u(t,o+d)!==u(e,d)){h=!1;break}if(h)return o}return -1}a.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==a.prototype},a.compare=function(t,e){if(R(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),R(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(t)||!a.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:+(n<r)},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;var r,n=a.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var o=t[r];if(R(o,Uint8Array)&&(o=a.from(o)),!a.isBuffer(o))throw TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},a.byteLength=p,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)m(this,e,e+1);return this},a.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)m(this,e,e+3),m(this,e+1,e+2);return this},a.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)m(this,e,e+7),m(this,e+1,e+6),m(this,e+2,e+5),m(this,e+3,e+4);return this},a.prototype.toString=function(){var t=this.length;return 0===t?"":0==arguments.length?v(this,0,t):f.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(t){if(!a.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},o&&(a.prototype[o]=a.prototype.inspect),a.prototype.compare=function(t,e,r,n,i){if(R(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,n>>>=0,i>>>=0,this===t)return 0;for(var o=i-n,s=r-e,l=Math.min(o,s),u=this.slice(n,i),c=t.slice(e,r),h=0;h<l;++h)if(u[h]!==c[h]){o=u[h],s=c[h];break}return o<s?-1:+(s<o)},a.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},a.prototype.indexOf=function(t,e,r){return g(this,t,e,r,!0)},a.prototype.lastIndexOf=function(t,e,r){return g(this,t,e,r,!1)};function v(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o,s,a,l,u=t[i],c=null,h=u>239?4:u>223?3:u>191?2:1;if(i+h<=r)switch(h){case 1:u<128&&(c=u);break;case 2:(192&(o=t[i+1]))==128&&(l=(31&u)<<6|63&o)>127&&(c=l);break;case 3:o=t[i+1],s=t[i+2],(192&o)==128&&(192&s)==128&&(l=(15&u)<<12|(63&o)<<6|63&s)>2047&&(l<55296||l>57343)&&(c=l);break;case 4:o=t[i+1],s=t[i+2],a=t[i+3],(192&o)==128&&(192&s)==128&&(192&a)==128&&(l=(15&u)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&l<1114112&&(c=l)}null===c?(c=65533,h=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=h}var d=n,p=d.length;if(p<=4096)return String.fromCharCode.apply(String,d);for(var f="",m=0;m<p;)f+=String.fromCharCode.apply(String,d.slice(m,m+=4096));return f}function b(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function w(t,e,r,n,i,o){if(!a.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw RangeError('"value" argument is out of bounds');if(r+n>t.length)throw RangeError("Index out of range")}function x(t,e,r,n,i,o){if(r+n>t.length||r<0)throw RangeError("Index out of range")}function E(t,e,r,n,o){return e*=1,r>>>=0,o||x(t,e,r,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,r,n,23,4),r+4}function T(t,e,r,n,o){return e*=1,r>>>=0,o||x(t,e,r,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,r,n,52,8),r+8}a.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,o,s,a,l,u,c,h,d=this.length-e;if((void 0===r||r>d)&&(r=d),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var p=!1;;)switch(n){case"hex":return function(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=e.length;n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a,l=parseInt(e.substr(2*s,2),16);if((a=l)!=a)break;t[r+s]=l}return s}(this,t,e,r);case"utf8":case"utf-8":return i=e,o=r,P(A(t,this.length-i),this,i,o);case"ascii":return s=e,a=r,P(S(t),this,s,a);case"latin1":case"binary":return function(t,e,r,n){return P(S(e),t,r,n)}(this,t,e,r);case"base64":return l=e,u=r,P(k(t),this,l,u);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return c=e,h=r,P(function(t,e){for(var r,n,i=[],o=0;o<t.length&&!((e-=2)<0);++o)n=(r=t.charCodeAt(o))>>8,i.push(r%256),i.push(n);return i}(t,this.length-c),this,c,h);default:if(p)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),p=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(t,e){var r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return Object.setPrototypeOf(n,a.prototype),n},a.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n},a.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},a.prototype.readUInt8=function(t,e){return t>>>=0,e||b(t,1,this.length),this[t]},a.prototype.readUInt16LE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUInt16BE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+0x1000000*this[t+3]},a.prototype.readUInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),0x1000000*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},a.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=e,i=1,o=this[t+--n];n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},a.prototype.readInt8=function(t,e){return(t>>>=0,e||b(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},a.prototype.readInt16LE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt16BE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readFloatLE=function(t,e){return t>>>=0,e||b(t,4,this.length),i.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,e){return t>>>=0,e||b(t,4,this.length),i.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,e){return t>>>=0,e||b(t,8,this.length),i.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,e){return t>>>=0,e||b(t,8,this.length),i.read(this,t,!1,52,8)},a.prototype.writeUIntLE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;w(this,t,e,r,i,0)}var o=1,s=0;for(this[e]=255&t;++s<r&&(o*=256);)this[e+s]=t/o&255;return e+r},a.prototype.writeUIntBE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;w(this,t,e,r,i,0)}var o=r-1,s=1;for(this[e+o]=255&t;--o>=0&&(s*=256);)this[e+o]=t/s&255;return e+r},a.prototype.writeUInt8=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,1,255,0),this[e]=255&t,e+1},a.prototype.writeUInt16LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeUInt16BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeUInt32LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0xffffffff,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},a.prototype.writeUInt32BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0xffffffff,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeIntLE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);w(this,t,e,r,i-1,-i)}var o=0,s=1,a=0;for(this[e]=255&t;++o<r&&(s*=256);)t<0&&0===a&&0!==this[e+o-1]&&(a=1),this[e+o]=(t/s|0)-a&255;return e+r},a.prototype.writeIntBE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);w(this,t,e,r,i-1,-i)}var o=r-1,s=1,a=0;for(this[e+o]=255&t;--o>=0&&(s*=256);)t<0&&0===a&&0!==this[e+o+1]&&(a=1),this[e+o]=(t/s|0)-a&255;return e+r},a.prototype.writeInt8=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},a.prototype.writeInt16LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeInt16BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeInt32LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0x7fffffff,-0x80000000),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},a.prototype.writeInt32BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0x7fffffff,-0x80000000),t<0&&(t=0xffffffff+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeFloatLE=function(t,e,r){return E(this,t,e,!0,r)},a.prototype.writeFloatBE=function(t,e,r){return E(this,t,e,!1,r)},a.prototype.writeDoubleLE=function(t,e,r){return T(this,t,e,!0,r)},a.prototype.writeDoubleBE=function(t,e,r){return T(this,t,e,!1,r)},a.prototype.copy=function(t,e,r,n){if(!a.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i=n-r;if(this===t&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(e,r,n);else if(this===t&&r<e&&e<n)for(var o=i-1;o>=0;--o)t[o+e]=this[o+r];else Uint8Array.prototype.set.call(t,this.subarray(r,n),e);return i},a.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===t.length){var i,o=t.charCodeAt(0);("utf8"===n&&o<128||"latin1"===n)&&(t=o)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(i=e;i<r;++i)this[i]=t;else{var s=a.isBuffer(t)?t:a.from(t,n),l=s.length;if(0===l)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<r-e;++i)this[i+e]=s[i%l]}return this};var C=/[^+/0-9A-Za-z-_]/g;function A(t,e){e=e||1/0;for(var r,n=t.length,i=null,o=[],s=0;s<n;++s){if((r=t.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319||s+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return o}function S(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}function k(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(C,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function P(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length)&&!(i>=t.length);++i)e[i+r]=t[i];return i}function R(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}var M=function(){for(var t="0123456789abcdef",e=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)e[n+i]=t[r]+t[i];return e}()},783:function(t,e){e.read=function(t,e,r,n,i){var o,s,a=8*i-n-1,l=(1<<a)-1,u=l>>1,c=-7,h=r?i-1:0,d=r?-1:1,p=t[e+h];for(h+=d,o=p&(1<<-c)-1,p>>=-c,c+=a;c>0;o=256*o+t[e+h],h+=d,c-=8);for(s=o&(1<<-c)-1,o>>=-c,c+=n;c>0;s=256*s+t[e+h],h+=d,c-=8);if(0===o)o=1-u;else{if(o===l)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,n),o-=u}return(p?-1:1)*s*Math.pow(2,o-n)},e.write=function(t,e,r,n,i,o){var s,a,l,u=8*o-i-1,c=(1<<u)-1,h=c>>1,d=5960464477539062e-23*(23===i),p=n?0:o-1,f=n?1:-1,m=+(e<0||0===e&&1/e<0);for(isNaN(e=Math.abs(e))||e===1/0?(a=+!!isNaN(e),s=c):(s=Math.floor(Math.log(e)/Math.LN2),e*(l=Math.pow(2,-s))<1&&(s--,l*=2),s+h>=1?e+=d/l:e+=d*Math.pow(2,1-h),e*l>=2&&(s++,l/=2),s+h>=c?(a=0,s=c):s+h>=1?(a=(e*l-1)*Math.pow(2,i),s+=h):(a=e*Math.pow(2,h-1)*Math.pow(2,i),s=0));i>=8;t[r+p]=255&a,p+=f,a/=256,i-=8);for(s=s<<i|a,u+=i;u>0;t[r+p]=255&s,p+=f,s/=256,u-=8);t[r+p-f]|=128*m}}},r={};function n(t){var i=r[t];if(void 0!==i)return i.exports;var o=r[t]={exports:{}},s=!0;try{e[t](o,o.exports,n),s=!1}finally{s&&delete r[t]}return o.exports}n.ab="//",t.exports=n(72)}()},9688:(t,e,r)=>{"use strict";r.d(e,{QP:()=>tu});let n=t=>{let e=a(t),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=t;return{getClassGroupId:t=>{let r=t.split("-");return""===r[0]&&1!==r.length&&r.shift(),i(r,e)||s(t)},getConflictingClassGroupIds:(t,e)=>{let i=r[t]||[];return e&&n[t]?[...i,...n[t]]:i}}},i=(t,e)=>{if(0===t.length)return e.classGroupId;let r=t[0],n=e.nextPart.get(r),o=n?i(t.slice(1),n):void 0;if(o)return o;if(0===e.validators.length)return;let s=t.join("-");return e.validators.find(({validator:t})=>t(s))?.classGroupId},o=/^\[(.+)\]$/,s=t=>{if(o.test(t)){let e=o.exec(t)[1],r=e?.substring(0,e.indexOf(":"));if(r)return"arbitrary.."+r}},a=t=>{let{theme:e,classGroups:r}=t,n={nextPart:new Map,validators:[]};for(let t in r)l(r[t],n,t,e);return n},l=(t,e,r,n)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:u(e,t)).classGroupId=r;return}if("function"==typeof t)return c(t)?void l(t(n),e,r,n):void e.validators.push({validator:t,classGroupId:r});Object.entries(t).forEach(([t,i])=>{l(i,u(e,t),r,n)})})},u=(t,e)=>{let r=t;return e.split("-").forEach(t=>{r.nextPart.has(t)||r.nextPart.set(t,{nextPart:new Map,validators:[]}),r=r.nextPart.get(t)}),r},c=t=>t.isThemeGetter,h=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,r=new Map,n=new Map,i=(i,o)=>{r.set(i,o),++e>t&&(e=0,n=r,r=new Map)};return{get(t){let e=r.get(t);return void 0!==e?e:void 0!==(e=n.get(t))?(i(t,e),e):void 0},set(t,e){r.has(t)?r.set(t,e):i(t,e)}}},d=t=>{let{prefix:e,experimentalParseClassName:r}=t,n=t=>{let e,r=[],n=0,i=0,o=0;for(let s=0;s<t.length;s++){let a=t[s];if(0===n&&0===i){if(":"===a){r.push(t.slice(o,s)),o=s+1;continue}if("/"===a){e=s;continue}}"["===a?n++:"]"===a?n--:"("===a?i++:")"===a&&i--}let s=0===r.length?t:t.substring(o),a=p(s);return{modifiers:r,hasImportantModifier:a!==s,baseClassName:a,maybePostfixModifierPosition:e&&e>o?e-o:void 0}};if(e){let t=e+":",r=n;n=e=>e.startsWith(t)?r(e.substring(t.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:e,maybePostfixModifierPosition:void 0}}if(r){let t=n;n=e=>r({className:e,parseClassName:t})}return n},p=t=>t.endsWith("!")?t.substring(0,t.length-1):t.startsWith("!")?t.substring(1):t,f=t=>{let e=Object.fromEntries(t.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;let r=[],n=[];return t.forEach(t=>{"["===t[0]||e[t]?(r.push(...n.sort(),t),n=[]):n.push(t)}),r.push(...n.sort()),r}},m=t=>({cache:h(t.cacheSize),parseClassName:d(t),sortModifiers:f(t),...n(t)}),g=/\s+/,y=(t,e)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:i,sortModifiers:o}=e,s=[],a=t.trim().split(g),l="";for(let t=a.length-1;t>=0;t-=1){let e=a[t],{isExternal:u,modifiers:c,hasImportantModifier:h,baseClassName:d,maybePostfixModifierPosition:p}=r(e);if(u){l=e+(l.length>0?" "+l:l);continue}let f=!!p,m=n(f?d.substring(0,p):d);if(!m){if(!f||!(m=n(d))){l=e+(l.length>0?" "+l:l);continue}f=!1}let g=o(c).join(":"),y=h?g+"!":g,v=y+m;if(s.includes(v))continue;s.push(v);let b=i(m,f);for(let t=0;t<b.length;++t){let e=b[t];s.push(y+e)}l=e+(l.length>0?" "+l:l)}return l};function v(){let t,e,r=0,n="";for(;r<arguments.length;)(t=arguments[r++])&&(e=b(t))&&(n&&(n+=" "),n+=e);return n}let b=t=>{let e;if("string"==typeof t)return t;let r="";for(let n=0;n<t.length;n++)t[n]&&(e=b(t[n]))&&(r&&(r+=" "),r+=e);return r},w=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,E=/^\((?:(\w[\w-]*):)?(.+)\)$/i,T=/^\d+\/\d+$/,C=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,A=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,k=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,P=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,R=t=>T.test(t),M=t=>!!t&&!Number.isNaN(Number(t)),j=t=>!!t&&Number.isInteger(Number(t)),O=t=>t.endsWith("%")&&M(t.slice(0,-1)),I=t=>C.test(t),D=()=>!0,V=t=>A.test(t)&&!S.test(t),q=()=>!1,F=t=>k.test(t),$=t=>P.test(t),L=t=>!_(t)&&!Y(t),B=t=>tt(t,ti,q),_=t=>x.test(t),U=t=>tt(t,to,V),z=t=>tt(t,ts,M),N=t=>tt(t,tr,q),W=t=>tt(t,tn,$),H=t=>tt(t,tl,F),Y=t=>E.test(t),G=t=>te(t,to),X=t=>te(t,ta),K=t=>te(t,tr),Z=t=>te(t,ti),J=t=>te(t,tn),Q=t=>te(t,tl,!0),tt=(t,e,r)=>{let n=x.exec(t);return!!n&&(n[1]?e(n[1]):r(n[2]))},te=(t,e,r=!1)=>{let n=E.exec(t);return!!n&&(n[1]?e(n[1]):r)},tr=t=>"position"===t||"percentage"===t,tn=t=>"image"===t||"url"===t,ti=t=>"length"===t||"size"===t||"bg-size"===t,to=t=>"length"===t,ts=t=>"number"===t,ta=t=>"family-name"===t,tl=t=>"shadow"===t;Symbol.toStringTag;let tu=function(t,...e){let r,n,i,o=function(a){return n=(r=m(e.reduce((t,e)=>e(t),t()))).cache.get,i=r.cache.set,o=s,s(a)};function s(t){let e=n(t);if(e)return e;let o=y(t,r);return i(t,o),o}return function(){return o(v.apply(null,arguments))}}(()=>{let t=w("color"),e=w("font"),r=w("text"),n=w("font-weight"),i=w("tracking"),o=w("leading"),s=w("breakpoint"),a=w("container"),l=w("spacing"),u=w("radius"),c=w("shadow"),h=w("inset-shadow"),d=w("text-shadow"),p=w("drop-shadow"),f=w("blur"),m=w("perspective"),g=w("aspect"),y=w("ease"),v=w("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],E=()=>[...x(),Y,_],T=()=>["auto","hidden","clip","visible","scroll"],C=()=>["auto","contain","none"],A=()=>[Y,_,l],S=()=>[R,"full","auto",...A()],k=()=>[j,"none","subgrid",Y,_],P=()=>["auto",{span:["full",j,Y,_]},j,Y,_],V=()=>[j,"auto",Y,_],q=()=>["auto","min","max","fr",Y,_],F=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],$=()=>["start","end","center","stretch","center-safe","end-safe"],tt=()=>["auto",...A()],te=()=>[R,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...A()],tr=()=>[t,Y,_],tn=()=>[...x(),K,N,{position:[Y,_]}],ti=()=>["no-repeat",{repeat:["","x","y","space","round"]}],to=()=>["auto","cover","contain",Z,B,{size:[Y,_]}],ts=()=>[O,G,U],ta=()=>["","none","full",u,Y,_],tl=()=>["",M,G,U],tu=()=>["solid","dashed","dotted","double"],tc=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],th=()=>[M,O,K,N],td=()=>["","none",f,Y,_],tp=()=>["none",M,Y,_],tf=()=>["none",M,Y,_],tm=()=>[M,Y,_],tg=()=>[R,"full",...A()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[I],breakpoint:[I],color:[D],container:[I],"drop-shadow":[I],ease:["in","out","in-out"],font:[L],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[I],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[I],shadow:[I],spacing:["px",M],text:[I],"text-shadow":[I],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",R,_,Y,g]}],container:["container"],columns:[{columns:[M,_,Y,a]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:E()}],overflow:[{overflow:T()}],"overflow-x":[{"overflow-x":T()}],"overflow-y":[{"overflow-y":T()}],overscroll:[{overscroll:C()}],"overscroll-x":[{"overscroll-x":C()}],"overscroll-y":[{"overscroll-y":C()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[j,"auto",Y,_]}],basis:[{basis:[R,"full","auto",a,...A()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[M,R,"auto","initial","none",_]}],grow:[{grow:["",M,Y,_]}],shrink:[{shrink:["",M,Y,_]}],order:[{order:[j,"first","last","none",Y,_]}],"grid-cols":[{"grid-cols":k()}],"col-start-end":[{col:P()}],"col-start":[{"col-start":V()}],"col-end":[{"col-end":V()}],"grid-rows":[{"grid-rows":k()}],"row-start-end":[{row:P()}],"row-start":[{"row-start":V()}],"row-end":[{"row-end":V()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":q()}],"auto-rows":[{"auto-rows":q()}],gap:[{gap:A()}],"gap-x":[{"gap-x":A()}],"gap-y":[{"gap-y":A()}],"justify-content":[{justify:[...F(),"normal"]}],"justify-items":[{"justify-items":[...$(),"normal"]}],"justify-self":[{"justify-self":["auto",...$()]}],"align-content":[{content:["normal",...F()]}],"align-items":[{items:[...$(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...$(),{baseline:["","last"]}]}],"place-content":[{"place-content":F()}],"place-items":[{"place-items":[...$(),"baseline"]}],"place-self":[{"place-self":["auto",...$()]}],p:[{p:A()}],px:[{px:A()}],py:[{py:A()}],ps:[{ps:A()}],pe:[{pe:A()}],pt:[{pt:A()}],pr:[{pr:A()}],pb:[{pb:A()}],pl:[{pl:A()}],m:[{m:tt()}],mx:[{mx:tt()}],my:[{my:tt()}],ms:[{ms:tt()}],me:[{me:tt()}],mt:[{mt:tt()}],mr:[{mr:tt()}],mb:[{mb:tt()}],ml:[{ml:tt()}],"space-x":[{"space-x":A()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":A()}],"space-y-reverse":["space-y-reverse"],size:[{size:te()}],w:[{w:[a,"screen",...te()]}],"min-w":[{"min-w":[a,"screen","none",...te()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[s]},...te()]}],h:[{h:["screen","lh",...te()]}],"min-h":[{"min-h":["screen","lh","none",...te()]}],"max-h":[{"max-h":["screen","lh",...te()]}],"font-size":[{text:["base",r,G,U]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,Y,z]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",O,_]}],"font-family":[{font:[X,_,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,Y,_]}],"line-clamp":[{"line-clamp":[M,"none",Y,z]}],leading:[{leading:[o,...A()]}],"list-image":[{"list-image":["none",Y,_]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Y,_]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:tr()}],"text-color":[{text:tr()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...tu(),"wavy"]}],"text-decoration-thickness":[{decoration:[M,"from-font","auto",Y,U]}],"text-decoration-color":[{decoration:tr()}],"underline-offset":[{"underline-offset":[M,"auto",Y,_]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:A()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Y,_]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Y,_]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:tn()}],"bg-repeat":[{bg:ti()}],"bg-size":[{bg:to()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},j,Y,_],radial:["",Y,_],conic:[j,Y,_]},J,W]}],"bg-color":[{bg:tr()}],"gradient-from-pos":[{from:ts()}],"gradient-via-pos":[{via:ts()}],"gradient-to-pos":[{to:ts()}],"gradient-from":[{from:tr()}],"gradient-via":[{via:tr()}],"gradient-to":[{to:tr()}],rounded:[{rounded:ta()}],"rounded-s":[{"rounded-s":ta()}],"rounded-e":[{"rounded-e":ta()}],"rounded-t":[{"rounded-t":ta()}],"rounded-r":[{"rounded-r":ta()}],"rounded-b":[{"rounded-b":ta()}],"rounded-l":[{"rounded-l":ta()}],"rounded-ss":[{"rounded-ss":ta()}],"rounded-se":[{"rounded-se":ta()}],"rounded-ee":[{"rounded-ee":ta()}],"rounded-es":[{"rounded-es":ta()}],"rounded-tl":[{"rounded-tl":ta()}],"rounded-tr":[{"rounded-tr":ta()}],"rounded-br":[{"rounded-br":ta()}],"rounded-bl":[{"rounded-bl":ta()}],"border-w":[{border:tl()}],"border-w-x":[{"border-x":tl()}],"border-w-y":[{"border-y":tl()}],"border-w-s":[{"border-s":tl()}],"border-w-e":[{"border-e":tl()}],"border-w-t":[{"border-t":tl()}],"border-w-r":[{"border-r":tl()}],"border-w-b":[{"border-b":tl()}],"border-w-l":[{"border-l":tl()}],"divide-x":[{"divide-x":tl()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":tl()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...tu(),"hidden","none"]}],"divide-style":[{divide:[...tu(),"hidden","none"]}],"border-color":[{border:tr()}],"border-color-x":[{"border-x":tr()}],"border-color-y":[{"border-y":tr()}],"border-color-s":[{"border-s":tr()}],"border-color-e":[{"border-e":tr()}],"border-color-t":[{"border-t":tr()}],"border-color-r":[{"border-r":tr()}],"border-color-b":[{"border-b":tr()}],"border-color-l":[{"border-l":tr()}],"divide-color":[{divide:tr()}],"outline-style":[{outline:[...tu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[M,Y,_]}],"outline-w":[{outline:["",M,G,U]}],"outline-color":[{outline:tr()}],shadow:[{shadow:["","none",c,Q,H]}],"shadow-color":[{shadow:tr()}],"inset-shadow":[{"inset-shadow":["none",h,Q,H]}],"inset-shadow-color":[{"inset-shadow":tr()}],"ring-w":[{ring:tl()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:tr()}],"ring-offset-w":[{"ring-offset":[M,U]}],"ring-offset-color":[{"ring-offset":tr()}],"inset-ring-w":[{"inset-ring":tl()}],"inset-ring-color":[{"inset-ring":tr()}],"text-shadow":[{"text-shadow":["none",d,Q,H]}],"text-shadow-color":[{"text-shadow":tr()}],opacity:[{opacity:[M,Y,_]}],"mix-blend":[{"mix-blend":[...tc(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":tc()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[M]}],"mask-image-linear-from-pos":[{"mask-linear-from":th()}],"mask-image-linear-to-pos":[{"mask-linear-to":th()}],"mask-image-linear-from-color":[{"mask-linear-from":tr()}],"mask-image-linear-to-color":[{"mask-linear-to":tr()}],"mask-image-t-from-pos":[{"mask-t-from":th()}],"mask-image-t-to-pos":[{"mask-t-to":th()}],"mask-image-t-from-color":[{"mask-t-from":tr()}],"mask-image-t-to-color":[{"mask-t-to":tr()}],"mask-image-r-from-pos":[{"mask-r-from":th()}],"mask-image-r-to-pos":[{"mask-r-to":th()}],"mask-image-r-from-color":[{"mask-r-from":tr()}],"mask-image-r-to-color":[{"mask-r-to":tr()}],"mask-image-b-from-pos":[{"mask-b-from":th()}],"mask-image-b-to-pos":[{"mask-b-to":th()}],"mask-image-b-from-color":[{"mask-b-from":tr()}],"mask-image-b-to-color":[{"mask-b-to":tr()}],"mask-image-l-from-pos":[{"mask-l-from":th()}],"mask-image-l-to-pos":[{"mask-l-to":th()}],"mask-image-l-from-color":[{"mask-l-from":tr()}],"mask-image-l-to-color":[{"mask-l-to":tr()}],"mask-image-x-from-pos":[{"mask-x-from":th()}],"mask-image-x-to-pos":[{"mask-x-to":th()}],"mask-image-x-from-color":[{"mask-x-from":tr()}],"mask-image-x-to-color":[{"mask-x-to":tr()}],"mask-image-y-from-pos":[{"mask-y-from":th()}],"mask-image-y-to-pos":[{"mask-y-to":th()}],"mask-image-y-from-color":[{"mask-y-from":tr()}],"mask-image-y-to-color":[{"mask-y-to":tr()}],"mask-image-radial":[{"mask-radial":[Y,_]}],"mask-image-radial-from-pos":[{"mask-radial-from":th()}],"mask-image-radial-to-pos":[{"mask-radial-to":th()}],"mask-image-radial-from-color":[{"mask-radial-from":tr()}],"mask-image-radial-to-color":[{"mask-radial-to":tr()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[M]}],"mask-image-conic-from-pos":[{"mask-conic-from":th()}],"mask-image-conic-to-pos":[{"mask-conic-to":th()}],"mask-image-conic-from-color":[{"mask-conic-from":tr()}],"mask-image-conic-to-color":[{"mask-conic-to":tr()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:tn()}],"mask-repeat":[{mask:ti()}],"mask-size":[{mask:to()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Y,_]}],filter:[{filter:["","none",Y,_]}],blur:[{blur:td()}],brightness:[{brightness:[M,Y,_]}],contrast:[{contrast:[M,Y,_]}],"drop-shadow":[{"drop-shadow":["","none",p,Q,H]}],"drop-shadow-color":[{"drop-shadow":tr()}],grayscale:[{grayscale:["",M,Y,_]}],"hue-rotate":[{"hue-rotate":[M,Y,_]}],invert:[{invert:["",M,Y,_]}],saturate:[{saturate:[M,Y,_]}],sepia:[{sepia:["",M,Y,_]}],"backdrop-filter":[{"backdrop-filter":["","none",Y,_]}],"backdrop-blur":[{"backdrop-blur":td()}],"backdrop-brightness":[{"backdrop-brightness":[M,Y,_]}],"backdrop-contrast":[{"backdrop-contrast":[M,Y,_]}],"backdrop-grayscale":[{"backdrop-grayscale":["",M,Y,_]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[M,Y,_]}],"backdrop-invert":[{"backdrop-invert":["",M,Y,_]}],"backdrop-opacity":[{"backdrop-opacity":[M,Y,_]}],"backdrop-saturate":[{"backdrop-saturate":[M,Y,_]}],"backdrop-sepia":[{"backdrop-sepia":["",M,Y,_]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":A()}],"border-spacing-x":[{"border-spacing-x":A()}],"border-spacing-y":[{"border-spacing-y":A()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Y,_]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[M,"initial",Y,_]}],ease:[{ease:["linear","initial",y,Y,_]}],delay:[{delay:[M,Y,_]}],animate:[{animate:["none",v,Y,_]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[m,Y,_]}],"perspective-origin":[{"perspective-origin":E()}],rotate:[{rotate:tp()}],"rotate-x":[{"rotate-x":tp()}],"rotate-y":[{"rotate-y":tp()}],"rotate-z":[{"rotate-z":tp()}],scale:[{scale:tf()}],"scale-x":[{"scale-x":tf()}],"scale-y":[{"scale-y":tf()}],"scale-z":[{"scale-z":tf()}],"scale-3d":["scale-3d"],skew:[{skew:tm()}],"skew-x":[{"skew-x":tm()}],"skew-y":[{"skew-y":tm()}],transform:[{transform:[Y,_,"","none","gpu","cpu"]}],"transform-origin":[{origin:E()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:tg()}],"translate-x":[{"translate-x":tg()}],"translate-y":[{"translate-y":tg()}],"translate-z":[{"translate-z":tg()}],"translate-none":["translate-none"],accent:[{accent:tr()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:tr()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Y,_]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":A()}],"scroll-mx":[{"scroll-mx":A()}],"scroll-my":[{"scroll-my":A()}],"scroll-ms":[{"scroll-ms":A()}],"scroll-me":[{"scroll-me":A()}],"scroll-mt":[{"scroll-mt":A()}],"scroll-mr":[{"scroll-mr":A()}],"scroll-mb":[{"scroll-mb":A()}],"scroll-ml":[{"scroll-ml":A()}],"scroll-p":[{"scroll-p":A()}],"scroll-px":[{"scroll-px":A()}],"scroll-py":[{"scroll-py":A()}],"scroll-ps":[{"scroll-ps":A()}],"scroll-pe":[{"scroll-pe":A()}],"scroll-pt":[{"scroll-pt":A()}],"scroll-pr":[{"scroll-pr":A()}],"scroll-pb":[{"scroll-pb":A()}],"scroll-pl":[{"scroll-pl":A()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Y,_]}],fill:[{fill:["none",...tr()]}],"stroke-w":[{stroke:[M,G,U,z]}],stroke:[{stroke:["none",...tr()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9946:(t,e,r)=>{"use strict";r.d(e,{A:()=>h});var n=r(2115);let i=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,r)=>r?r.toUpperCase():e.toLowerCase()),s=t=>{let e=o(t);return e.charAt(0).toUpperCase()+e.slice(1)},a=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];return e.filter((t,e,r)=>!!t&&""!==t.trim()&&r.indexOf(t)===e).join(" ").trim()},l=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,n.forwardRef)((t,e)=>{let{color:r="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:s,className:c="",children:h,iconNode:d,...p}=t;return(0,n.createElement)("svg",{ref:e,...u,width:i,height:i,stroke:r,strokeWidth:s?24*Number(o)/Number(i):o,className:a("lucide",c),...!h&&!l(p)&&{"aria-hidden":"true"},...p},[...d.map(t=>{let[e,r]=t;return(0,n.createElement)(e,r)}),...Array.isArray(h)?h:[h]])}),h=(t,e)=>{let r=(0,n.forwardRef)((r,o)=>{let{className:l,...u}=r;return(0,n.createElement)(c,{ref:o,iconNode:e,className:a("lucide-".concat(i(s(t))),"lucide-".concat(t),l),...u})});return r.displayName=s(t),r}}}]);