# 🚀 Mobilify Website - Refactoring & Readability Plan

This document outlines a structured plan to refactor the Mobilify website codebase. The primary goals are to improve project structure, increase code readability, and externalize all assets (text, colors, images, configurations) to make future changes easier and safer.

**Migration Strategy:** Hybrid approach prioritizing Phase 1 (externalization) before Phase 2 (structure reorganization). This ensures components are cleaner before being moved, making structural refactoring easier to manage.

---

## Phase 1: Asset & Configuration Externalization

> **Goal:** Remove all hardcoded values from the codebase. Everything should be configurable from a single source of truth (environment variables, a constants file, or the CMS).

### 1.1. Centralized Constants (`src/config/site.ts`)
- [ ] Create a `src/config/site.ts` file for static, developer-controlled configuration.
- [ ] Move static, non-sensitive data that doesn't belong in the CMS into this file:
  - Site name (`Mobilify`)
  - Company contact email (`<EMAIL>`)
  - Social media links and URLs
  - Main navigation links structure
  - Default fallback values for CMS content
  - API endpoints and external service URLs
- [ ] Update all components to import these values from `src/config/site.ts` instead of using hardcoded strings.

### 1.2. Text & Content Audit (Sanity CMS)
- [ ] Review all high-level components (`Hero.tsx`, `AboutSnippet.tsx`, `ServicesOverview.tsx`, `Process.tsx`, `Contact.tsx`, `Header.tsx`, `Footer.tsx`).
- [ ] Identify all dynamic UI text (headlines, paragraphs, button labels, form success/error messages) that should be content-managed.
- [ ] **Create `siteSettings` Singleton Schema:** Add to existing Sanity Studio configuration:
  1. Create `schemas/siteSettings.js` file with singleton document structure
  2. Open existing `schemas/schema.js` file in Sanity Studio project
  3. Import: `import siteSettings from './siteSettings'`
  4. Add `siteSettings` to the types array within `createSchema`
  ```javascript
  // schemas/siteSettings.js
  export default {
    name: 'siteSettings',
    title: 'Site Settings',
    type: 'document',
    __experimental_actions: ['update', 'publish'], // Disable create/delete for singleton
    fields: [
      // Hero Section
      { name: 'heroHeadline', title: 'Hero Headline', type: 'string' },
      { name: 'heroSubtext', title: 'Hero Subtext', type: 'text' },
      { name: 'heroButtonText', title: 'Hero Button Text', type: 'string' },
      // Contact Section
      { name: 'contactHeadline', title: 'Contact Headline', type: 'string' },
      { name: 'contactSubtext', title: 'Contact Subtext', type: 'text' },
      { name: 'contactButtonText', title: 'Contact Button Text', type: 'string' },
      // Form Messages
      { name: 'formSuccessMessage', title: 'Form Success Message', type: 'string' },
      { name: 'formErrorMessage', title: 'Form Error Message', type: 'string' },
      // Add more fields as identified during audit...
    ]
  }
  ```
- [ ] **Content Migration:** After schema deployment, manually populate Sanity Studio:
  1. Deploy updated schema to Sanity Studio
  2. Open "Site Settings" document in Studio
  3. Copy/paste current hardcoded text from website into corresponding fields
  4. Publish changes to make CMS the source of truth
- [ ] Create utility function `getSiteSettings()` in `src/lib/sanity.ts` to fetch singleton data.
- [ ] Update components to fetch content from Sanity, with fallbacks to static strings from `site.ts`.

### 1.3. Color & Theme Audit
- [ ] Perform a global search for hardcoded hex color values (e.g., `#4f46e5`, `#111827`).
- [ ] Search for non-semantic Tailwind CSS color classes (e.g., `bg-indigo-600`, `text-gray-900`).
- [ ] Replace every instance with the correct semantic color tokens from `tailwind.config.js` (e.g., `bg-electric-blue`, `text-dark-charcoal`).
- [ ] Verify that all components render correctly in both light and dark modes after the changes.

### 1.4. Image & Media Asset Management
- [ ] Create a dedicated `public/images/` directory for static UI images (e.g., logos, favicons, static placeholders).
- [ ] **For Content Images (Team Photos, etc.):**
  - [ ] Ensure the Sanity schema (e.g., for team members) has an `image` field.
  - [ ] Upload all content-related images to the Sanity CMS.
  - [ ] Update components (like `/about` page) to source these images from the CMS via `next/image`.
- [ ] Remove any images currently stored inside the `src` directory.

### 1.5. Environment Variable Audit
- [ ] Scan the entire codebase for any hardcoded API keys, URLs, or other secrets.
- [ ] Ensure every external service configuration is powered by an environment variable.
- [ ] Verify that `.env.local.example` is complete and documents every required variable with a placeholder.

---

## Phase 2: Project Structure Refactoring

> **Goal:** Organize the file structure to be more intuitive and scalable. Components should be easy to locate based on their function.

### 2.1. Component Directory Reorganization
- [ ] **Create `src/components/layout/`:**
  - [ ] Move `Header.tsx`, `Footer.tsx`, and any new global wrapper components into this directory.
- [ ] **Create `src/components/sections/`:**
  - [ ] Move all major homepage sections into this directory. This clarifies that they are large, compositional components, not small reusable UI elements.
    - `Hero.tsx`
    - `InteractiveDemo.tsx`
    - `ServicesOverview.tsx`
    - `Process.tsx`
    - `AboutSnippet.tsx`
    - `Contact.tsx`
- [ ] **Create `src/components/icons/`:**
  - [ ] If any custom SVG icons are used, convert them to React components and place them here.
- [ ] **Create `src/analytics/`:**
  - [ ] Move analytics-related components (`GoogleAnalytics.tsx`, `CrispChat.tsx`) into this top-level directory to separate them from the main UI logic.

### 2.2. Component Granularity
- [ ] Review large, multi-responsibility components.
- [ ] **`InteractiveDemo.tsx`:** Break down into smaller sub-components:
  - `DemoTabs.tsx`
  - `DemoInput.tsx`
  - `PhoneMockup.tsx`
- [ ] **`Contact.tsx`:** Extract the form logic and JSX into its own component:
  - `ContactForm.tsx`
- [ ] **`Header.tsx`:** Break down into smaller, single-responsibility components:
  - `DesktopNav.tsx` (for top-level links)
  - `MobileMenu.tsx` (for the hamburger menu and its flyout/overlay)
- [ ] **`Footer.tsx`:** Ensure complex parts are separate components:
  - `FooterNav.tsx` (for the link columns)
  - `FooterNewsletter.tsx` (if the newsletter form is embedded and complex)
- [ ] **`/services` page:** The comparison table should be its own component:
  - `PricingTable.tsx`

### 2.3. Hooks & Logic Abstraction
- [ ] Create a `src/hooks/` directory.
- [ ] **`useContactForm.ts`:** Extract the form state management, validation, and submission logic from the `ContactForm` component into a custom hook.
- [ ] **`useAnalytics.ts`:** Create a unified analytics interface with `trackEvent(eventName, eventProperties)` function to handle:
  - Form submission tracking
  - Navigation and CTA click tracking
  - CMS content interaction tracking
  - Existing InteractiveDemo events
  - All future analytics needs across the site

### 2.4. Type Definitions
- [ ] Create a `src/types/` directory.
- [ ] **`sanity.ts`:** Define all interfaces for data fetched from Sanity (e.g., `Post`, `FaqItem`).
- [ ] **`forms.ts`:** Define types for form inputs and states.
- [ ] **`index.ts`:** For general, site-wide types.
- [ ] Remove all shared `type` and `interface` definitions from component files and import them from the central types directory.

---

## Phase 3: Code Quality and Documentation

> **Goal:** Ensure the code is self-documenting and easy for new developers to understand.

### 3.1. Code Commenting & Naming
- [ ] Add JSDoc comments to every component, explaining its purpose and props.
- [ ] Add inline comments for any complex algorithms or non-obvious business logic.
- [ ] Audit all file, variable, and function names to ensure they are descriptive and consistent.

### 3.2. Finalizing Documentation
- [ ] **Create `CONTRIBUTING.md`:**
  - [ ] Document the new project structure and explain the purpose of each directory.
  - [ ] Provide clear instructions on how to add a new page, section, or UI component.
  - [ ] Outline the process for adding new content via the Sanity CMS.
- [ ] **Update `README.md`:**
  - [ ] Simplify the main README to focus on project setup and deployment.
  - [ ] Add a "Project Structure" section that links to the more detailed `CONTRIBUTING.md`.

### 3.3. Testing Integration (Comprehensive Coverage)
- [ ] **Unit Tests:** As components are broken down (InteractiveDemo → DemoTabs, Header → MobileMenu), create individual unit tests for new smaller components.
- [ ] **Integration Tests:** Update parent component tests to:
  - Mock new data-fetching logic (Sanity CMS and hooks)
  - Test "success" path (CMS data present)
  - Test "fallback" path (CMS data missing, using `site.ts` defaults)
- [ ] **Test Updates:** As each component is refactored, immediately update its corresponding test file in `src/__tests__/` and `src/components/__tests__/`.
- [ ] **Regression Prevention:** Ensure all existing tests pass after each refactoring step. A component refactoring is only "done" when tests are updated and passing.
- [ ] **New Test Coverage:** Add comprehensive tests for:
  - New hooks (`useContactForm.ts`, `useAnalytics.ts`)
  - Extracted components (DemoTabs, MobileMenu, etc.)
  - CMS integration and fallback behavior
  - Analytics event tracking

### 3.4. Linting and Formatting
- [ ] Set up pre-commit hooks with Husky to automatically run ESLint and Prettier (as noted in `FEATURE_TODO.md`).
- [ ] Run `npm run lint -- --fix` and `npm run format` across the entire project to enforce a consistent style.

---

## Implementation Notes

### Development Workflow (Branch-Based Approach)
Create separate branches for each phase to maintain stability and enable focused reviews:
- `refactor/phase-1-externalization` - Asset & configuration externalization
- `refactor/phase-2-structure` - File structure reorganization
- `refactor/phase-3-quality` - Documentation and quality improvements

### Sanity CMS Integration
- **Existing Setup:** The project already has Sanity CMS configured with blog and FAQ schemas. The task is to add the new `siteSettings` singleton schema to the existing Sanity Studio.
- **Environment Variables:** All necessary Sanity environment variables are already defined in `.env.local.example`.
- **Content Migration:** Manual population of CMS content from existing hardcoded values (no migration script needed).

### Fallback Strategy
- **Primary:** Sanity CMS content (dynamic, content-managed)
- **Fallback:** Static strings in `src/config/site.ts` (prevents broken UI)
- **Implementation:** Components fetch from Sanity with graceful degradation to `site.ts` defaults

### Migration Priority
1. **Phase 1.1 & 1.2:** Complete externalization of constants and content before any structural changes
2. **Phase 2:** Reorganize file structure with already-cleaned components
3. **Phase 3:** Documentation and quality improvements with comprehensive test coverage

### Testing Strategy
- **Comprehensive Coverage:** Both unit and integration tests required
- **Refactoring Safety:** Existing tests are the safety net - external behavior must remain unchanged
- **Immediate Updates:** Update tests immediately after refactoring each component
- **CMS Integration:** Verify functionality works with both CMS content and fallback values