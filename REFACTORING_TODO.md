# 🚀 Mobilify Website - Refactoring & Readability Plan

This document outlines a structured plan to refactor the Mobilify website codebase. The primary goals are to improve project structure, increase code readability, and externalize all assets (text, colors, images, configurations) to make future changes easier and safer.

---

## Phase 1: Asset & Configuration Externalization

> **Goal:** Remove all hardcoded values from the codebase. Everything should be configurable from a single source of truth (environment variables, a constants file, or the CMS).

### 1.1. Centralized Constants
- [ ] Create a `src/config/site.ts` file.
- [ ] Move static, non-sensitive data that doesn't belong in the CMS into this file. Examples:
  - Site name (`Mobilify`)
  - Company contact email (`<EMAIL>`)
  - Social media links
  - Main navigation links (if not managed in the CMS)
- [ ] Update all components to import these values from `src/config/site.ts` instead of using hardcoded strings.

### 1.2. Text & Content Audit (Sanity CMS)
- [ ] Review all high-level components (`Hero.tsx`, `AboutSnippet.tsx`, `ServicesOverview.tsx`, `Process.tsx`, `Contact.tsx`).
- [ ] Identify all static UI text (headlines, paragraphs, button labels, form success/error messages) that is currently hardcoded in JSX.
- [ ] **Decision:** Create a "Singleton" document type in Sanity called `siteSettings` to hold this global content.
- [ ] Add fields to the `siteSettings` schema for each piece of identified text.
- [ ] Update the corresponding components to fetch this content from Sanity, falling back to a default string if the fetch fails.

### 1.3. Color & Theme Audit
- [ ] Perform a global search for hardcoded hex color values (e.g., `#4f46e5`, `#111827`).
- [ ] Search for non-semantic Tailwind CSS color classes (e.g., `bg-indigo-600`, `text-gray-900`).
- [ ] Replace every instance with the correct semantic color tokens from `tailwind.config.js` (e.g., `bg-electric-blue`, `text-dark-charcoal`).
- [ ] Verify that all components render correctly in both light and dark modes after the changes.

### 1.4. Image & Media Asset Management
- [ ] Create a dedicated `public/images/` directory for static UI images (e.g., logos, favicons, static placeholders).
- [ ] **For Content Images (Team Photos, etc.):**
  - [ ] Ensure the Sanity schema (e.g., for team members) has an `image` field.
  - [ ] Upload all content-related images to the Sanity CMS.
  - [ ] Update components (like `/about` page) to source these images from the CMS via `next/image`.
- [ ] Remove any images currently stored inside the `src` directory.

### 1.5. Environment Variable Audit
- [ ] Scan the entire codebase for any hardcoded API keys, URLs, or other secrets.
- [ ] Ensure every external service configuration is powered by an environment variable.
- [ ] Verify that `.env.local.example` is complete and documents every required variable with a placeholder.

---

## Phase 2: Project Structure Refactoring

> **Goal:** Organize the file structure to be more intuitive and scalable. Components should be easy to locate based on their function.

### 2.1. Component Directory Reorganization
- [ ] **Create `src/components/layout/`:**
  - [ ] Move `Header.tsx`, `Footer.tsx`, and any new global wrapper components into this directory.
- [ ] **Create `src/components/sections/`:**
  - [ ] Move all major homepage sections into this directory. This clarifies that they are large, compositional components, not small reusable UI elements.
    - `Hero.tsx`
    - `InteractiveDemo.tsx`
    - `ServicesOverview.tsx`
    - `Process.tsx`
    - `AboutSnippet.tsx`
    - `Contact.tsx`
- [ ] **Create `src/components/icons/`:**
  - [ ] If any custom SVG icons are used, convert them to React components and place them here.
- [ ] **Create `src/analytics/`:**
  - [ ] Move analytics-related components (`GoogleAnalytics.tsx`, `CrispChat.tsx`) into this top-level directory to separate them from the main UI logic.

### 2.2. Component Granularity
- [ ] Review large, multi-responsibility components.
- [ ] **`InteractiveDemo.tsx`:** Break down into smaller sub-components:
  - `DemoTabs.tsx`
  - `DemoInput.tsx`
  - `PhoneMockup.tsx`
- [ ] **`Contact.tsx`:** Extract the form logic and JSX into its own component:
  - `ContactForm.tsx`
- [ ] **`Header.tsx`:** Break down into smaller, single-responsibility components:
  - `DesktopNav.tsx` (for top-level links)
  - `MobileMenu.tsx` (for the hamburger menu and its flyout/overlay)
- [ ] **`Footer.tsx`:** Ensure complex parts are separate components:
  - `FooterNav.tsx` (for the link columns)
  - `FooterNewsletter.tsx` (if the newsletter form is embedded and complex)
- [ ] **`/services` page:** The comparison table should be its own component:
  - `PricingTable.tsx`

### 2.3. Hooks & Logic Abstraction
- [ ] Create a `src/hooks/` directory.
- [ ] **`useContactForm.ts`:** Extract the form state management, validation, and submission logic from the `ContactForm` component into a custom hook.
- [ ] **`useAnalytics.ts`:** Create a hook to standardize how analytics events are sent (e.g., `trackEvent('demo_interaction')`). This decouples components from the specific analytics provider.

### 2.4. Type Definitions
- [ ] Create a `src/types/` directory.
- [ ] **`sanity.ts`:** Define all interfaces for data fetched from Sanity (e.g., `Post`, `FaqItem`).
- [ ] **`forms.ts`:** Define types for form inputs and states.
- [ ] **`index.ts`:** For general, site-wide types.
- [ ] Remove all shared `type` and `interface` definitions from component files and import them from the central types directory.

---

## Phase 3: Code Quality and Documentation

> **Goal:** Ensure the code is self-documenting and easy for new developers to understand.

### 3.1. Code Commenting & Naming
- [ ] Add JSDoc comments to every component, explaining its purpose and props.
- [ ] Add inline comments for any complex algorithms or non-obvious business logic.
- [ ] Audit all file, variable, and function names to ensure they are descriptive and consistent.

### 3.2. Finalizing Documentation
- [ ] **Create `CONTRIBUTING.md`:**
  - [ ] Document the new project structure and explain the purpose of each directory.
  - [ ] Provide clear instructions on how to add a new page, section, or UI component.
  - [ ] Outline the process for adding new content via the Sanity CMS.
- [ ] **Update `README.md`:**
  - [ ] Simplify the main README to focus on project setup and deployment.
  - [ ] Add a "Project Structure" section that links to the more detailed `CONTRIBUTING.md`.

### 3.3. Linting and Formatting
- [ ] Set up pre-commit hooks with Husky to automatically run ESLint and Prettier (as noted in `FEATURE_TODO.md`).
- [ ] Run `npm run lint -- --fix` and `npm run format` across the entire project to enforce a consistent style.

---