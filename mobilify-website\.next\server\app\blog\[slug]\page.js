(()=>{var e={};e.id=953,e.ids=[512,953],e.modules={163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return i}});let i=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1322:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:i,blurHeight:s,blurDataURL:l,objectFit:n}=e,o=i?40*i:t,a=s?40*s:r,d=o&&a?"viewBox='0 0 "+o+" "+a+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===n?"xMidYMid":"cover"===n?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+l+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,t,r)=>{let{createProxy:i}=r(39844);e.exports=i("C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\node_modules\\next\\dist\\client\\app-dir\\link.js")},9131:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return a}}),r(21122);let i=r(1322),s=r(27894),l=["-moz-initial","fill","none","scale-down",void 0];function n(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var r,a;let d,c,u,{src:m,sizes:p,unoptimized:f=!1,priority:h=!1,loading:x,className:b,quality:g,width:y,height:v,fill:j=!1,style:_,overrideSrc:w,onLoad:R,onLoadingComplete:N,placeholder:O="empty",blurDataURL:E,fetchPriority:k,decoding:P="async",layout:M,objectFit:C,objectPosition:D,lazyBoundary:A,lazyRoot:S,...T}=e,{imgConf:I,showAltText:q,blurComplete:z,defaultLoader:L}=t,U=I||s.imageConfigDefault;if("allSizes"in U)d=U;else{let e=[...U.deviceSizes,...U.imageSizes].sort((e,t)=>e-t),t=U.deviceSizes.sort((e,t)=>e-t),i=null==(r=U.qualities)?void 0:r.sort((e,t)=>e-t);d={...U,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===L)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let F=T.loader||L;delete T.loader,delete T.srcSet;let B="__next_img_default"in F;if(B){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=F;F=t=>{let{config:r,...i}=t;return e(i)}}if(M){"fill"===M&&(j=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[M];e&&(_={..._,...e});let t={responsive:"100vw",fill:"100vw"}[M];t&&!p&&(p=t)}let $="",G=o(y),W=o(v);if((a=m)&&"object"==typeof a&&(n(a)||void 0!==a.src)){let e=n(m)?m.default:m;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,u=e.blurHeight,E=E||e.blurDataURL,$=e.src,!j)if(G||W){if(G&&!W){let t=G/e.width;W=Math.round(e.height*t)}else if(!G&&W){let t=W/e.height;G=Math.round(e.width*t)}}else G=e.width,W=e.height}let X=!h&&("lazy"===x||void 0===x);(!(m="string"==typeof m?m:$)||m.startsWith("data:")||m.startsWith("blob:"))&&(f=!0,X=!1),d.unoptimized&&(f=!0),B&&!d.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(f=!0);let H=o(g),Z=Object.assign(j?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:C,objectPosition:D}:{},q?{}:{color:"transparent"},_),K=z||"empty"===O?null:"blur"===O?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:G,heightInt:W,blurWidth:c,blurHeight:u,blurDataURL:E||"",objectFit:Z.objectFit})+'")':'url("'+O+'")',V=l.includes(Z.objectFit)?"fill"===Z.objectFit?"100% 100%":"cover":Z.objectFit,J=K?{backgroundSize:V,backgroundPosition:Z.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:K}:{},Y=function(e){let{config:t,src:r,unoptimized:i,width:s,quality:l,sizes:n,loader:o}=e;if(i)return{src:r,srcSet:void 0,sizes:void 0};let{widths:a,kind:d}=function(e,t,r){let{deviceSizes:i,allSizes:s}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(r);)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:s.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:s,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>s.find(t=>t>=e)||s[s.length-1]))],kind:"x"}}(t,s,n),c=a.length-1;return{sizes:n||"w"!==d?n:"100vw",srcSet:a.map((e,i)=>o({config:t,src:r,quality:l,width:e})+" "+("w"===d?e:i+1)+d).join(", "),src:o({config:t,src:r,quality:l,width:a[c]})}}({config:d,src:m,unoptimized:f,width:G,quality:H,sizes:p,loader:F});return{props:{...T,loading:X?"lazy":x,fetchPriority:k,width:G,height:W,decoding:P,className:b,style:{...Z,...J},sizes:Y.sizes,srcSet:Y.srcSet,src:w||Y.src},meta:{unoptimized:f,priority:h,placeholder:O,fill:j}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},18512:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var i=r(37413);r(61120);let s=({className:e=""})=>(0,i.jsx)("div",{className:`inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ${e}`,children:(0,i.jsx)("span",{className:"text-white font-bold text-xl",children:"M"})});var l=r(68367),n=r(87193),o=r(37463);let a=()=>(0,i.jsx)("footer",{className:"bg-dark-charcoal dark:bg-gray-950 text-white py-12 transition-colors duration-300",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8",children:[(0,i.jsxs)("div",{className:"lg:col-span-1",children:[(0,i.jsxs)("div",{className:"flex items-center mb-4",children:[(0,i.jsx)(s,{}),(0,i.jsx)("span",{className:"ml-2 text-xl font-bold text-white",children:"Mobilify"})]}),(0,i.jsx)("p",{className:"text-gray-400 dark:text-gray-300 text-sm leading-relaxed",children:"Transforming ideas into mobile reality. We help entrepreneurs and businesses create beautiful, high-performance mobile apps without the traditional complexity and cost."})]}),(0,i.jsxs)("div",{className:"lg:col-span-1",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold mb-4 text-white",children:"Quick Links"}),(0,i.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/#demo",className:"text-gray-400 dark:text-gray-300 hover:text-electric-blue transition-colors duration-200",children:"See Demo"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/services",className:"text-gray-400 dark:text-gray-300 hover:text-electric-blue transition-colors duration-200",children:"Services & Pricing"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/about",className:"text-gray-400 dark:text-gray-300 hover:text-electric-blue transition-colors duration-200",children:"About Us"})}),(0,i.jsx)("li",{children:(0,i.jsx)("a",{href:"/#contact",className:"text-gray-400 dark:text-gray-300 hover:text-electric-blue transition-colors duration-200",children:"Contact"})})]})]}),(0,i.jsx)("div",{className:"lg:col-span-1",children:(0,i.jsx)(l.default,{variant:"footer"})})]}),(0,i.jsx)("div",{className:"border-t border-gray-800 dark:border-gray-700 pt-8",children:(0,i.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[(0,i.jsx)("p",{className:"text-gray-400 dark:text-gray-300 text-sm",children:"\xa9 2024 Mobilify. All rights reserved."}),(0,i.jsxs)("div",{className:"flex items-center space-x-6 mt-4 md:mt-0",children:[(0,i.jsx)(o.default,{children:(0,i.jsx)(n.default,{size:"sm"})}),(0,i.jsx)("a",{href:"#",className:"text-gray-400 dark:text-gray-300 hover:text-electric-blue text-sm transition-colors duration-200",children:"Privacy Policy"}),(0,i.jsx)("a",{href:"#",className:"text-gray-400 dark:text-gray-300 hover:text-electric-blue text-sm transition-colors duration-200",children:"Terms of Service"})]})]})})]})})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21122:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},21820:e=>{"use strict";e.exports=require("os")},24597:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\Header.tsx","default")},26373:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var i=r(61120);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),a=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:l="",children:n,iconNode:c,...u},m)=>(0,i.createElement)("svg",{ref:m,...d,width:t,height:t,stroke:e,strokeWidth:s?24*Number(r)/Number(t):r,className:o("lucide",l),...!n&&!a(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(n)?n:[n]])),u=(e,t)=>{let r=(0,i.forwardRef)(({className:r,...l},a)=>(0,i.createElement)(c,{ref:a,iconNode:t,className:o(`lucide-${s(n(e))}`,`lucide-${e}`,r),...l}));return r.displayName=n(e),r}},27894:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return i}});let r=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32091:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:i,width:s,quality:l}=e,n=l||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(i)+"&w="+s+"&q="+n+(i.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),r.__next_img_default=!0;let i=r},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36440:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(26373).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},40918:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(26373).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},44012:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var i=r(65239),s=r(48088),l=r(88170),n=r.n(l),o=r(30893),a={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>o[e]);r.d(t,a);let d={children:["",{children:["blog",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,75861)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,14974)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\app\\blog\\[slug]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/blog/[slug]/page",pathname:"/blog/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},48976:(e,t,r)=>{"use strict";function i(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return i}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49603:(e,t,r)=>{let{createProxy:i}=r(39844);e.exports=i("C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\node_modules\\next\\dist\\client\\image-component.js")},52656:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.t.bind(r,49603,23)),Promise.resolve().then(r.bind(r,24597)),Promise.resolve().then(r.bind(r,68367)),Promise.resolve().then(r.bind(r,37463)),Promise.resolve().then(r.bind(r,87193))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62384:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.t.bind(r,46533,23)),Promise.resolve().then(r.bind(r,90261)),Promise.resolve().then(r.bind(r,91477)),Promise.resolve().then(r.bind(r,47009)),Promise.resolve().then(r.bind(r,13143))},62765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return s}});let i=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function s(){let e=Object.defineProperty(Error(i),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=i,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68367:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\NewsletterSignup.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\NewsletterSignup.tsx","default")},70099:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return a},getImageProps:function(){return o}});let i=r(72639),s=r(9131),l=r(49603),n=i._(r(32091));function o(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let a=l.Image},70899:(e,t,r)=>{"use strict";function i(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return i}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,n.isNextRouterError)(t)||(0,l.isBailoutToCSRError)(t)||(0,a.isDynamicServerError)(t)||(0,o.isDynamicPostpone)(t)||(0,s.isPostpone)(t)||(0,i.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let i=r(68388),s=r(52637),l=r(51846),n=r(31162),o=r(84971),a=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74075:e=>{"use strict";e.exports=require("zlib")},75861:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b,generateMetadata:()=>x});var i=r(37413);r(61120);var s=r(4536),l=r.n(s),n=r(70099),o=r.n(n),a=r(97576);let d=(0,r(26373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var c=r(88971),u=r(40918),m=r(36440),p=r(24597),f=r(18512);let h=({content:e,className:t=""})=>{let r=(e,t)=>{if("image"===e._type)return(0,i.jsxs)("div",{className:"my-8",children:[(0,i.jsx)("div",{className:"relative w-full h-64 md:h-96 rounded-lg overflow-hidden bg-gray-200 dark:bg-gray-700 flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:e.alt||"Blog image"})}),e.caption&&(0,i.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 text-center mt-2 italic",children:e.caption})]},e._key||t);if(!e.children)return null;let r=e.children.map((t,r)=>{let s=t.text;if(!t.marks||0===t.marks.length)return s;let n=(0,i.jsx)("span",{children:s},r);return t.marks.forEach(t=>{if("strong"===t)n=(0,i.jsx)("strong",{className:"font-semibold",children:n},`${r}-strong`);else if("em"===t)n=(0,i.jsx)("em",{className:"italic",children:n},`${r}-em`);else if("code"===t)n=(0,i.jsx)("code",{className:"bg-gray-100 px-1 py-0.5 rounded text-sm font-mono",children:n},`${r}-code`);else{let s=e.markDefs?.find(e=>e._key===t);s&&"link"===s._type&&s.href&&(n=s.href.startsWith("http")?(0,i.jsx)("a",{href:s.href,target:"_blank",rel:"noopener noreferrer",className:"text-electric-blue hover:underline",children:n},`${r}-link`):(0,i.jsx)(l(),{href:s.href,className:"text-electric-blue hover:underline",children:n},`${r}-link`))}}),n});switch(e.style){case"h1":return(0,i.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-dark-charcoal mb-6 mt-8",children:r},e._key||t);case"h2":return(0,i.jsx)("h2",{className:"text-2xl md:text-3xl font-bold text-dark-charcoal mb-4 mt-8",children:r},e._key||t);case"h3":return(0,i.jsx)("h3",{className:"text-xl md:text-2xl font-semibold text-dark-charcoal mb-3 mt-6",children:r},e._key||t);case"h4":return(0,i.jsx)("h4",{className:"text-lg md:text-xl font-semibold text-dark-charcoal mb-2 mt-4",children:r},e._key||t);case"blockquote":return(0,i.jsx)("blockquote",{className:"border-l-4 border-electric-blue pl-4 py-2 my-6 italic text-gray-700 bg-gray-50 rounded-r",children:r},e._key||t);default:if("bullet"===e.listItem||"number"===e.listItem)return(0,i.jsx)("li",{className:"mb-2",children:r},e._key||t);return(0,i.jsx)("p",{className:"mb-4 leading-relaxed text-gray-700",children:r},e._key||t)}},s=[],n=[],o=null;return e.forEach(e=>{if("block"===e._type&&e.listItem){let t=e.listItem;t===o?n.push(e):(n.length>0&&s.push([...n]),n=[e],o=t)}else n.length>0&&(s.push([...n]),n=[],o=null),s.push(e)}),n.length>0&&s.push([...n]),(0,i.jsx)("div",{className:`prose prose-lg max-w-none ${t}`,children:s.map((e,t)=>{if(Array.isArray(e)){let s=e[0].listItem;return(0,i.jsx)("bullet"===s?"ul":"ol",{className:"bullet"===s?"list-disc pl-6 mb-4":"list-decimal pl-6 mb-4",children:e.map((e,t)=>r(e,t))},t)}return r(e,t)})})};async function x({params:e}){let{slug:t}=await e;return{title:`${t.replace(/-/g," ")} | Mobilify Blog`,description:"Read our latest insights on mobile app development.",openGraph:{title:`${t.replace(/-/g," ")} | Mobilify Blog`,description:"Read our latest insights on mobile app development.",type:"article"}}}async function b({params:e}){let{slug:t}=await e,r={_id:"1",_type:"post",title:"Sample Blog Post",slug:{current:t},author:"Mobilify Team",mainImage:void 0,categories:[{_id:"mobile-dev",_type:"category",title:"Mobile Development",slug:{current:"mobile-development"},description:"Tips and tutorials for mobile app development"}],publishedAt:"2024-01-15T10:00:00Z",excerpt:"This is a sample blog post to demonstrate the blog functionality.",body:[{_type:"block",_key:"1",children:[{_type:"span",_key:"1",text:"This is a sample blog post. In a real implementation, this content would come from Sanity CMS.",marks:[]}]}],_createdAt:"2024-01-15T10:00:00Z",_updatedAt:"2024-01-15T10:00:00Z"};r||(0,a.notFound)();let s=[],n=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),x={"@context":"https://schema.org","@type":"Article",headline:r.title,description:r.excerpt,image:{"@type":"ImageObject",url:"/placeholder-image.svg",width:1200,height:630},datePublished:r.publishedAt,dateModified:r._updatedAt,author:{"@type":"Person",name:r.author},publisher:{"@type":"Organization",name:"Mobilify",logo:{"@type":"ImageObject",url:"/logo.svg",width:200,height:200}},mainEntityOfPage:{"@type":"WebPage","@id":`https://mobilify.com/blog/${r.slug.current}`}};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(x)}}),(0,i.jsxs)("div",{className:"min-h-screen w-full overflow-x-hidden",children:[(0,i.jsx)(p.default,{}),(0,i.jsxs)("main",{className:"pt-16",children:[(0,i.jsx)("section",{className:"py-6 bg-gray-50",children:(0,i.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)(l(),{href:"/blog",className:"inline-flex items-center text-electric-blue hover:underline font-medium",children:[(0,i.jsx)(d,{className:"mr-2 w-4 h-4"}),"Back to Blog"]})})}),(0,i.jsx)("article",{className:"py-12",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,i.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:r.categories.map(e=>(0,i.jsx)(l(),{href:`/blog?category=${e.slug.current}`,className:"text-sm font-medium text-electric-blue bg-blue-50 px-3 py-1 rounded-full hover:bg-blue-100 transition-colors duration-200",children:e.title},e._id))}),(0,i.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-dark-charcoal mb-6 leading-tight",children:r.title}),r.excerpt&&(0,i.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:r.excerpt}),(0,i.jsxs)("div",{className:"flex items-center gap-6 text-gray-500 mb-8 pb-8 border-b",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(c.A,{className:"w-5 h-5"}),(0,i.jsx)("span",{className:"font-medium",children:r.author})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(u.A,{className:"w-5 h-5"}),(0,i.jsx)("span",{children:n(r.publishedAt)})]})]}),(0,i.jsx)("div",{className:"relative w-full h-64 md:h-96 rounded-xl overflow-hidden mb-12 bg-gray-200 dark:bg-gray-700 flex items-center justify-center",children:r.mainImage?(0,i.jsx)(o(),{src:r.mainImage,alt:r.title,width:800,height:400,className:"w-full h-full object-cover",priority:!0}):(0,i.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"Sample Blog Post Image"})}),(0,i.jsx)("div",{className:"prose prose-lg max-w-none",children:(0,i.jsx)(h,{content:r.body})})]})}),s.length>0&&(0,i.jsx)("section",{className:"py-16 bg-gray-50",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,i.jsx)("h2",{className:"text-3xl font-bold text-dark-charcoal mb-8 text-center",children:"Related Articles"}),(0,i.jsx)("div",{className:"grid md:grid-cols-3 gap-8",children:s.map(e=>(0,i.jsxs)("article",{className:"bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden",children:[(0,i.jsx)("div",{className:"relative h-48 w-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center",children:e.mainImage?(0,i.jsx)(o(),{src:e.mainImage,alt:e.title,width:400,height:192,className:"w-full h-full object-cover"}):(0,i.jsx)("span",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"Related Post Image"})}),(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-bold text-dark-charcoal mb-2 line-clamp-2",children:(0,i.jsx)(l(),{href:`/blog/${e.slug.current}`,className:"hover:text-electric-blue transition-colors duration-200",children:e.title})}),e.excerpt&&(0,i.jsx)("p",{className:"text-gray-600 mb-4 line-clamp-3 text-sm",children:e.excerpt}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{className:"text-sm text-gray-500",children:n(e.publishedAt)}),(0,i.jsxs)(l(),{href:`/blog/${e.slug.current}`,className:"inline-flex items-center text-electric-blue font-medium hover:underline text-sm",children:["Read More",(0,i.jsx)(m.A,{className:"ml-1 w-3 h-3"})]})]})]})]},e._id))})]})})]}),(0,i.jsx)(f.default,{})]})]})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return n},getRedirectStatusCodeFromError:function(){return u},getRedirectTypeFromError:function(){return c},getURLFromRedirectError:function(){return d},permanentRedirect:function(){return a},redirect:function(){return o}});let i=r(52836),s=r(49026),l=r(19121).actionAsyncStorage;function n(e,t,r){void 0===r&&(r=i.RedirectStatusCode.TemporaryRedirect);let l=Object.defineProperty(Error(s.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return l.digest=s.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",l}function o(e,t){var r;throw null!=t||(t=(null==l||null==(r=l.getStore())?void 0:r.isAction)?s.RedirectType.push:s.RedirectType.replace),n(e,t,i.RedirectStatusCode.TemporaryRedirect)}function a(e,t){throw void 0===t&&(t=s.RedirectType.replace),n(e,t,i.RedirectStatusCode.PermanentRedirect)}function d(e){return(0,s.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function c(e){if(!(0,s.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function u(e){if(!(0,s.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87193:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\My projects\\\\Mobilify\\\\website\\\\gemini\\\\mobilify-website\\\\src\\\\components\\\\SimpleDarkModeToggle.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\My projects\\Mobilify\\website\\gemini\\mobilify-website\\src\\components\\SimpleDarkModeToggle.tsx","default")},88971:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(26373).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return c},RedirectType:function(){return s.RedirectType},forbidden:function(){return n.forbidden},notFound:function(){return l.notFound},permanentRedirect:function(){return i.permanentRedirect},redirect:function(){return i.redirect},unauthorized:function(){return o.unauthorized},unstable_rethrow:function(){return a.unstable_rethrow}});let i=r(86897),s=r(49026),l=r(62765),n=r(48976),o=r(70899),a=r(163);class d extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class c extends URLSearchParams{append(){throw new d}delete(){throw new d}set(){throw new d}sort(){throw new d}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,759,375,814,533,652,209],()=>r(44012));module.exports=i})();