"use strict";exports.id=449,exports.ids=[449],exports.modules={1745:(e,t,r)=>{r.d(t,{$U:()=>a,Bg:()=>l,PK:()=>c,UZ:()=>n,f_:()=>o,jx:()=>s,zR:()=>i});let s={name:"Mobilify",tagline:"Turn Your Website or Idea Into a Custom Mobile App",description:"Mobilify converts your existing website or new idea into a high-quality, native mobile app for iOS and Android. Get a beautiful, custom-designed app without the complexity.",url:process.env.NEXT_PUBLIC_SITE_URL||"https://mobilify.app",author:"Mobilify Team"},a={main:[{label:"Services",href:"#services-overview",id:"services-overview"},{label:"How It Works",href:"#process",id:"process"},{label:"About Us",href:"#about",id:"about"}],footer:{company:[{label:"About",href:"/about"},{label:"Services",href:"/services"},{label:"Blog",href:"/blog"},{label:"FAQ",href:"/faq"}],legal:[{label:"Privacy Policy",href:"/privacy"},{label:"Terms of Service",href:"/terms"},{label:"Cookie Policy",href:"/cookies"}],support:[{label:"Contact Us",href:"#contact"},{label:"Help Center",href:"/help"},{label:"Documentation",href:"/docs"}]}},i={hero:{headline:"Your Idea. Your App. Realized.",subtext:"Mobilify transforms your concepts and existing websites into stunning, high-performance mobile apps. We are the bridge from vision to launch.",buttonText:"See How It Works"},contact:{headline:"Ready to Build Your Mobile Future?",subtext:"Let's discuss your project. We're happy to provide a free, no-obligation consultation and quote.",buttonText:"Send Message",successMessage:"Thank you! Your message has been sent successfully. We'll get back to you within 24 hours.",errorMessage:"Sorry, there was an error sending your message. Please try again or contact us directly."},services:{headline:"Our Services",subtext:"Choose the perfect solution for your mobile app needs"},process:{headline:"How It Works",subtext:"Our proven process to turn your idea into a successful mobile app"},about:{headline:"About Mobilify",subtext:"We are passionate about helping businesses and entrepreneurs bring their ideas to life through mobile technology."}},l={starter:{name:"Starter App",description:"Perfect for converting existing websites into mobile apps.",price:"Starting at $5,000",features:["Website Conversion","iOS & Android","Basic Features"],popular:!1},custom:{name:"Custom App",description:"Turn your new ideas into reality with custom development.",price:"Starting at $15,000",features:["Idea to App","Custom UI/UX","Advanced Features"],popular:!0},enterprise:{name:"Enterprise Solution",description:"Comprehensive solutions for large-scale applications.",price:"Custom Pricing",features:["Full Development","Scalable Architecture","Ongoing Support"],popular:!1}},o={analytics:{googleAnalytics:""},forms:{web3forms:""},chat:{crisp:process.env.NEXT_PUBLIC_CRISP_WEBSITE_ID,tawkTo:process.env.NEXT_PUBLIC_TAWK_TO_PROPERTY_ID},newsletter:{mailchimp:process.env.NEXT_PUBLIC_MAILCHIMP_API_KEY,convertkit:process.env.CONVERTKIT_API_KEY}};s.name,s.tagline,s.name,s.description,s.url,s.name,s.name,s.tagline;let n={duration:{fast:.2,normal:.4,slow:.6,verySlow:1},easing:{easeInOut:[.4,0,.2,1],easeOut:[0,0,.2,1],easeIn:[.4,0,1,1]}},c={isDevelopment:!1,isProduction:!0,enableDebugLogs:!1,enableAnalytics:!0}},7378:(e,t,r)=>{r.d(t,{st:()=>l,qF:()=>d,Mw:()=>p});var s=r(43210),a=r(1745);let i={HERO_CTA_CLICK:"hero_cta_click",CONTACT_CTA_CLICK:"contact_cta_click",SERVICES_CTA_CLICK:"services_cta_click",DEMO_INTERACTION:"demo_interaction",DEMO_TAB_SWITCH:"demo_tab_switch",DEMO_ANIMATION_COMPLETE:"demo_animation_complete",DEMO_PREVIEW_CLICK:"demo_preview_click",FORM_SUBMIT:"form_submit",FORM_SUCCESS:"form_success",FORM_ERROR:"form_error",FORM_FIELD_FOCUS:"form_field_focus",BLOG_POST_CLICK:"blog_post_click",FAQ_ITEM_EXPAND:"faq_item_expand",EXTERNAL_LINK_CLICK:"external_link_click",CHAT_TRIGGER_CLICK:"chat_trigger_click",PHONE_CLICK:"phone_click",EMAIL_CLICK:"email_click",PAGE_VIEW:"page_view",SCROLL_DEPTH:"scroll_depth",TIME_ON_PAGE:"time_on_page"};function l(){let e=(0,s.useCallback)((e,t)=>{(a.PK.enableAnalytics||a.PK.isDevelopment)&&a.PK.isDevelopment&&a.PK.enableDebugLogs},[]),t=(0,s.useCallback)((t,r)=>{e(i.HERO_CTA_CLICK,{category:"navigation",label:t,custom_parameters:{source:r}})},[e]),r=(0,s.useCallback)((t,r,s)=>{e({submit:i.FORM_SUBMIT,success:i.FORM_SUCCESS,error:i.FORM_ERROR,field_focus:i.FORM_FIELD_FOCUS}[t],{category:"form_interaction",label:r,custom_parameters:{field_name:s}})},[e]),l=(0,s.useCallback)((t,r)=>{e({tab_switch:i.DEMO_TAB_SWITCH,preview_click:i.DEMO_PREVIEW_CLICK,animation_complete:i.DEMO_ANIMATION_COMPLETE}[t],{category:"demo_interaction",label:r})},[e]),o=(0,s.useCallback)((t,r,s)=>{e({blog_post:i.BLOG_POST_CLICK,faq_item:i.FAQ_ITEM_EXPAND,external_link:i.EXTERNAL_LINK_CLICK}[t],{category:"content_interaction",label:r,custom_parameters:{action:s}})},[e]),n=(0,s.useCallback)((t,r)=>{e({chat:i.CHAT_TRIGGER_CLICK,phone:i.PHONE_CLICK,email:i.EMAIL_CLICK}[t],{category:"support_interaction",label:t,custom_parameters:{source:r}})},[e]),c=(0,s.useCallback)((t,r)=>{e(i.PAGE_VIEW,{category:"page_interaction",label:t,custom_parameters:{page_title:r,page_path:t}})},[e]);return{trackEvent:e,trackNavigation:t,trackFormInteraction:r,trackDemoInteraction:l,trackContentInteraction:o,trackSupportInteraction:n,trackPageView:c,EVENTS:i}}let o={data:{name:"",email:"",company:"",projectType:"",message:""},errors:{},isSubmitting:!1,isSubmitted:!1,submitSuccess:!1},n=(e,t)=>{switch(e){case"name":if(!t.trim())return"Name is required";if(t.trim().length<2)return"Name must be at least 2 characters";return;case"email":if(!t.trim())return"Email is required";if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t))return"Please enter a valid email address";return;case"company":if(t.trim()&&t.trim().length<2)return"Company name must be at least 2 characters";return;case"projectType":if(!t.trim())return"Please select a project type";return;case"message":if(!t.trim())return"Message is required";if(t.trim().length<10)return"Message must be at least 10 characters";if(t.trim().length>1e3)return"Message must be less than 1000 characters";return;default:return}},c=e=>{let t={};return Object.keys(e).forEach(r=>{let s=n(r,e[r]);s&&(t[r]=s)}),t};function d(){let[e,t]=(0,s.useState)(o),{trackFormInteraction:r}=l(),i=(0,s.useCallback)((e,r)=>{t(t=>({...t,data:{...t.data,[e]:r},errors:{...t.errors,[e]:void 0,general:void 0}}))},[]),d=(0,s.useCallback)(e=>{r("field_focus","contact_form",e)},[r]),u=(0,s.useCallback)(r=>{let s=n(r,e.data[r]);return t(e=>({...e,errors:{...e.errors,[r]:s}})),!s},[e.data]),m=(0,s.useCallback)(()=>{t(o)},[]),b=(0,s.useCallback)(async()=>{let s=c(e.data);if(Object.keys(s).length>0)return t(e=>({...e,errors:s})),!1;if(!a.f_.forms.web3forms)return t(e=>({...e,errors:{general:"Form service is not configured. Please contact us directly."}})),r("error","contact_form"),!1;t(e=>({...e,isSubmitting:!0,errors:{}})),r("submit","contact_form");try{let s=new FormData;s.append("access_key",a.f_.forms.web3forms),s.append("name",e.data.name),s.append("email",e.data.email),s.append("company",e.data.company),s.append("project_type",e.data.projectType),s.append("message",e.data.message),s.append("from_name","Mobilify Contact Form"),s.append("subject",`New Contact Form Submission from ${e.data.name}`);let i=await fetch("https://api.web3forms.com/submit",{method:"POST",body:s}),l=await i.json();if(l.success)return t(e=>({...e,isSubmitting:!1,isSubmitted:!0,submitSuccess:!0})),r("success","contact_form"),!0;throw Error(l.message||"Form submission failed")}catch(e){return t(e=>({...e,isSubmitting:!1,isSubmitted:!0,submitSuccess:!1,errors:{general:"There was an error sending your message. Please try again or contact us directly."}})),r("error","contact_form"),!1}},[e.data,r]);return{formData:e.data,errors:e.errors,isSubmitting:e.isSubmitting,isSubmitted:e.isSubmitted,submitSuccess:e.submitSuccess,updateField:i,handleFieldFocus:d,validateSingleField:u,submitForm:b,resetForm:m,isValid:0===Object.keys(e.errors).length,hasErrors:Object.keys(e.errors).length>0}}var u=r(22038),m=r(46463),b=r.n(m);let h=!!(process.env.NEXT_PUBLIC_SANITY_PROJECT_ID&&process.env.NEXT_PUBLIC_SANITY_DATASET),g=h?(0,u.UU)({projectId:process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,dataset:process.env.NEXT_PUBLIC_SANITY_DATASET||"production",apiVersion:"2024-01-01",useCdn:!0,token:process.env.SANITY_API_TOKEN}):null,x=(h&&g&&b()(g),(e,t=!1,r=null)=>{let s=new Date().getFullYear();return{heroHeadline:e?.heroHeadline||a.zR.hero.headline,heroSubtext:e?.heroSubtext||a.zR.hero.subtext,heroButtonText:e?.heroButtonText||a.zR.hero.buttonText,contactHeadline:e?.contactHeadline||a.zR.contact.headline,contactSubtext:e?.contactSubtext||a.zR.contact.subtext,contactButtonText:e?.contactButtonText||a.zR.contact.buttonText,formSuccessMessage:e?.formSuccessMessage||a.zR.contact.successMessage,formErrorMessage:e?.formErrorMessage||a.zR.contact.errorMessage,servicesHeadline:e?.servicesHeadline||a.zR.services.headline,servicesSubtext:e?.servicesSubtext||a.zR.services.subtext,processHeadline:e?.processHeadline||a.zR.process.headline,processSubtext:e?.processSubtext||a.zR.process.subtext,aboutHeadline:e?.aboutHeadline||a.zR.about.headline,aboutSubtext:e?.aboutSubtext||a.zR.about.subtext,footerTagline:e?.footerTagline||"Building the future of mobile apps",footerCopyright:e?.footerCopyright||`\xa9 ${s} Mobilify. All rights reserved.`,isLoading:t,isFromCMS:!!e,error:r}});function p(e){let t=function(){let[e,t]=(0,s.useState)(null),[r,a]=(0,s.useState)(!0),[i,l]=(0,s.useState)(null);return x(e,r,i)}();switch(e){case"hero":return{headline:t.heroHeadline,subtext:t.heroSubtext,buttonText:t.heroButtonText,isLoading:t.isLoading,isFromCMS:t.isFromCMS};case"contact":return{headline:t.contactHeadline,subtext:t.contactSubtext,buttonText:t.contactButtonText,successMessage:t.formSuccessMessage,errorMessage:t.formErrorMessage,isLoading:t.isLoading,isFromCMS:t.isFromCMS};case"services":return{headline:t.servicesHeadline,subtext:t.servicesSubtext,isLoading:t.isLoading,isFromCMS:t.isFromCMS};case"process":return{headline:t.processHeadline,subtext:t.processSubtext,isLoading:t.isLoading,isFromCMS:t.isFromCMS};case"about":return{headline:t.aboutHeadline,subtext:t.aboutSubtext,isLoading:t.isLoading,isFromCMS:t.isFromCMS};default:return{isLoading:t.isLoading,isFromCMS:t.isFromCMS}}}},13143:(e,t,r)=>{r.d(t,{A:()=>o});var s=r(60687);r(43210);var a=r(21134),i=r(363),l=r(1188);let o=({className:e="",size:t="md"})=>{let{theme:r,toggleTheme:o}=(0,l.DP)();return(0,s.jsx)("button",{onClick:o,className:`${(()=>{switch(t){case"sm":return"w-8 h-8 text-sm";case"lg":return"w-12 h-12 text-lg";default:return"w-10 h-10 text-base"}})()} flex items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200 ${e}`,"aria-label":`Switch to ${"dark"===r?"light":"dark"} mode`,children:"dark"===r?(0,s.jsx)(a.A,{className:"w-5 h-5"}):(0,s.jsx)(i.A,{className:"w-5 h-5"})})}},56857:(e,t,r)=>{r.r(t),r.d(t,{default:()=>y});var s=r(60687),a=r(43210),i=r(89733),l=r(13143),o=r(47009),n=r(85814),c=r.n(n),d=r(7378);let u=[{title:"Quick Links",links:[{label:"Interactive Demo",href:"/#demo"},{label:"Our Services",href:"/#services"},{label:"How It Works",href:"/#process"},{label:"About Us",href:"/about"},{label:"Contact",href:"/#contact"}]},{title:"Services",links:[{label:"Website to App",href:"/services#website-conversion"},{label:"Custom Mobile Apps",href:"/services#custom-apps"},{label:"App Maintenance",href:"/services#maintenance"},{label:"Consultation",href:"/services#consultation"}]},{title:"Resources",links:[{label:"Blog",href:"/blog"},{label:"FAQ",href:"/faq"},{label:"Case Studies",href:"/blog?category=case-studies"},{label:"Pricing",href:"/services#pricing"}]},{title:"Legal",links:[{label:"Privacy Policy",href:"/privacy"},{label:"Terms of Service",href:"/terms"},{label:"Cookie Policy",href:"/cookies"}]}],m=({className:e=""})=>{let{trackNavigation:t}=(0,d.st)(),r=(e,r)=>{t(e,"footer_nav")};return(0,s.jsx)("div",{className:`grid grid-cols-2 md:grid-cols-4 gap-8 ${e}`,children:u.map(e=>(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4 text-white",children:e.title}),(0,s.jsx)("ul",{className:"space-y-2 text-sm",children:e.links.map(e=>(0,s.jsx)("li",{children:e.external?(0,s.jsx)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",onClick:()=>r(e.label,e.href),className:"text-gray-400 dark:text-gray-300 hover:text-electric-blue transition-colors duration-200",children:e.label}):(0,s.jsx)(c(),{href:e.href,onClick:()=>r(e.label,e.href),className:"text-gray-400 dark:text-gray-300 hover:text-electric-blue transition-colors duration-200",children:e.label})},e.href))})]},e.title))})};var b=r(26001),h=r(41550),g=r(93613),x=r(5336),p=r(1745);let f=({className:e=""})=>{let[t,r]=(0,a.useState)({email:"",isSubmitting:!1,isSubmitted:!1,error:null}),{trackFormSubmission:i}=(0,d.st)(),l=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),o=async e=>{if(e.preventDefault(),!l(t.email))return void r(e=>({...e,error:"Please enter a valid email address"}));r(e=>({...e,isSubmitting:!0,error:null}));try{await new Promise(e=>setTimeout(e,1e3)),r(e=>({...e,isSubmitting:!1,isSubmitted:!0,email:""})),i("newsletter",!0,{source:"footer",email_domain:t.email.split("@")[1]}),setTimeout(()=>{r(e=>({...e,isSubmitted:!1}))},3e3)}catch(e){r(e=>({...e,isSubmitting:!1,error:"Something went wrong. Please try again."})),i("newsletter",!1,{source:"footer",error:"submission_failed"})}};return(0,s.jsxs)("div",{className:`${e}`,children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4 text-white",children:"Stay Updated"}),(0,s.jsx)("p",{className:"text-gray-400 dark:text-gray-300 text-sm mb-4",children:"Get the latest updates on mobile app development trends and Mobilify news."}),(0,s.jsxs)("form",{onSubmit:o,className:"space-y-3",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(h.A,{className:"h-4 w-4 text-gray-400"})}),(0,s.jsx)("input",{type:"email",value:t.email,onChange:e=>{r(t=>({...t,email:e.target.value,error:null}))},placeholder:"Enter your email",disabled:t.isSubmitting||t.isSubmitted,className:" w-full pl-10 pr-4 py-2 text-sm bg-gray-800 dark:bg-gray-700  border border-gray-600 dark:border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-electric-blue focus:border-electric-blue disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ","aria-label":"Email address for newsletter"})]}),t.error&&(0,s.jsxs)(b.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"flex items-center gap-2 text-red-400 text-xs",children:[(0,s.jsx)(g.A,{className:"h-3 w-3"}),t.error]}),(0,s.jsx)(b.P.button,{type:"submit",disabled:t.isSubmitting||t.isSubmitted||!t.email.trim(),className:" w-full py-2 px-4 text-sm font-medium rounded-lg bg-electric-blue hover:bg-electric-blue/90 text-white transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:ring-2 focus:ring-electric-blue focus:ring-offset-2 focus:ring-offset-gray-800 ",whileHover:{scale:1.02},whileTap:{scale:.98},children:t.isSubmitting?(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"}),"Subscribing..."]}):t.isSubmitted?(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4"}),"Subscribed!"]}):"Subscribe"})]}),t.isSubmitted&&(0,s.jsx)(b.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:p.UZ.duration},className:"text-green-400 text-xs mt-2",children:"Thank you for subscribing! Check your email for confirmation."})]})},y=()=>(0,s.jsx)("footer",{className:"bg-dark-charcoal dark:bg-gray-950 text-white py-12 transition-colors duration-300",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8 mb-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-1",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)(i.A,{}),(0,s.jsx)("span",{className:"ml-2 text-xl font-bold text-white",children:p.jx.name})]}),(0,s.jsx)("p",{className:"text-gray-400 dark:text-gray-300 text-sm leading-relaxed",children:p.jx.description}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsx)(f,{})})]}),(0,s.jsx)("div",{className:"lg:col-span-3",children:(0,s.jsx)(m,{})})]}),(0,s.jsx)("div",{className:"border-t border-gray-800 dark:border-gray-700 pt-8",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[(0,s.jsxs)("p",{className:"text-gray-400 dark:text-gray-300 text-sm",children:["\xa9 ",new Date().getFullYear()," ",p.jx.name,". All rights reserved."]}),(0,s.jsx)("div",{className:"flex items-center space-x-6 mt-4 md:mt-0",children:(0,s.jsx)(o.default,{children:(0,s.jsx)(l.A,{})})})]})})]})})},89733:(e,t,r)=>{r.d(t,{A:()=>a});var s=r(60687);r(43210);let a=({className:e=""})=>(0,s.jsx)("div",{className:`inline-flex items-center justify-center w-10 h-10 bg-gray-900 rounded-lg ${e}`,children:(0,s.jsx)("span",{className:"text-white font-bold text-xl",children:"M"})})},99537:(e,t,r)=>{r.d(t,{default:()=>f});var s=r(60687),a=r(43210),i=r(11860),l=r(12941),o=r(89733),n=r(47009),c=r(33872);let d=()=>(0,s.jsxs)("button",{onClick:()=>{window.location.href="mailto:<EMAIL>?subject=Chat%20Request"},className:"inline-flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-dark-charcoal dark:hover:text-white transition-colors duration-200","aria-label":"Open chat",children:[(0,s.jsx)(c.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"Chat"})]});var u=r(13143),m=r(1745),b=r(7378);let h=({isMobile:e=!1,onItemClick:t,className:r=""})=>{let{trackNavigation:a}=(0,b.st)(),i=(r,s)=>{let i=document.getElementById(r);i&&(i.scrollIntoView({behavior:"smooth"}),a(s,e?"mobile_nav":"desktop_nav")),t?.()},l=m.$U.main;return(0,s.jsx)("nav",{className:`${e?"flex flex-col space-y-4":"hidden md:flex md:items-center md:space-x-8"} ${r}`,children:l.map(t=>(0,s.jsx)("button",{onClick:()=>i(t.href.replace("#",""),t.label),className:`
            text-gray-700 dark:text-gray-300 hover:text-electric-blue dark:hover:text-electric-blue 
            transition-colors duration-200 font-medium
            ${e?"text-lg py-2 text-left w-full hover:bg-gray-50 dark:hover:bg-gray-800 px-4 rounded-lg":"text-sm"}
          `,"aria-label":`Navigate to ${t.label} section`,children:t.label},t.href))})};var g=r(88920),x=r(26001);let p=({isOpen:e,onClose:t})=>(0,s.jsx)(g.N,{children:e&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(x.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:m.UZ.duration},className:"fixed inset-0 bg-black/50 z-40 md:hidden",onClick:t}),(0,s.jsx)(x.P.div,{initial:{x:"100%"},animate:{x:0},exit:{x:"100%"},transition:{duration:m.UZ.duration,ease:m.UZ.easing},className:"fixed top-0 right-0 h-full w-80 max-w-[90vw] bg-white dark:bg-gray-900 shadow-xl z-50 md:hidden",children:(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Menu"}),(0,s.jsx)("button",{onClick:t,className:"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors","aria-label":"Close menu",children:(0,s.jsx)(i.A,{className:"h-5 w-5 text-gray-600 dark:text-gray-400"})})]}),(0,s.jsxs)("div",{className:"flex-1 p-4",children:[(0,s.jsx)(h,{isMobile:!0,onItemClick:t,className:"mb-8"}),(0,s.jsxs)("div",{className:"space-y-4 pt-8 border-t border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Dark Mode"}),(0,s.jsx)(u.A,{})]}),(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsx)(d,{})})]})]})]})})]})}),f=()=>{let[e,t]=(0,a.useState)(!1),{trackNavigation:r}=(0,b.st)();return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("header",{className:"fixed top-0 left-0 right-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800 z-50 transition-colors duration-300",children:(0,s.jsx)("div",{className:"w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-between h-16 w-full",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(o.A,{})}),(0,s.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[(0,s.jsx)(h,{}),(0,s.jsx)(n.default,{children:(0,s.jsx)(d,{})}),(0,s.jsx)(n.default,{children:(0,s.jsx)(u.A,{})})]}),(0,s.jsx)("button",{onClick:()=>{t(!e),r("mobile_menu_toggle","header")},className:"md:hidden p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors","aria-label":"Toggle mobile menu",children:e?(0,s.jsx)(i.A,{size:24}):(0,s.jsx)(l.A,{size:24})})]})})}),(0,s.jsx)(p,{isOpen:e,onClose:()=>t(!1)})]})}}};