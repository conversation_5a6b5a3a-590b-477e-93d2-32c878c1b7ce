"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[157],{845:(t,e,i)=>{i.d(e,{t:()=>n});let n=(0,i(2115).createContext)(null)},869:(t,e,i)=>{i.d(e,{L:()=>n});let n=(0,i(2115).createContext)({})},1366:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},1508:(t,e,i)=>{i.d(e,{Q:()=>n});let n=(0,i(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},2082:(t,e,i)=>{i.d(e,{xQ:()=>s});var n=i(2115),r=i(845);function s(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0],e=(0,n.useContext)(r.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:s,register:o}=e,a=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return o(a)},[t]);let l=(0,n.useCallback)(()=>t&&s&&s(a),[a,s,t]);return!i&&s?[!1,l]:[!0]}},2098:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2596:(t,e,i)=>{i.d(e,{$:()=>n});function n(){for(var t,e,i=0,n="",r=arguments.length;i<r;i++)(t=arguments[i])&&(e=function t(e){var i,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(i=0;i<s;i++)e[i]&&(n=t(e[i]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}(t))&&(n&&(n+=" "),n+=e);return n}},2885:(t,e,i)=>{i.d(e,{M:()=>r});var n=i(2115);function r(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},3509:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},4416:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},6408:(t,e,i)=>{let n;function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return null==t||t.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,n){if("function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}return e}function a(t,e,i){let n=t.getProps();return o(n,e,void 0!==i?i:n.custom,t)}function l(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>sM});let u=t=>t,h={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],c={value:null,addProjectionMetrics:null};function p(t,e){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=d.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,s=!1)=>{let a=s&&r?i:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{if(a=t,r){s=!0;return}r=!0,[i,n]=[n,i],i.forEach(u),e&&c.value&&c.value.frameloop[e].push(l),l=0,i.clear(),r=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:p,update:m,preRender:f,render:g,postRender:v}=o,y=()=>{let s=h.useManualTiming?r.timestamp:performance.now();i=!1,h.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),p.process(r),m.process(r),f.process(r),g.process(r),v.process(r),r.isProcessing=!1,i&&e&&(n=!1,t(y))},b=()=>{i=!0,n=!0,r.isProcessing||t(y)};return{schedule:d.reduce((t,e)=>{let n=o[e];return t[e]=(t,e=!1,r=!1)=>(i||b(),n.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<d.length;e++)o[d[e]].cancel(t)},state:r,steps:o}}let{schedule:m,cancel:f,state:g,steps:v}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),y=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(y),x=new Set(["width","height","top","left","right","bottom",...y]);function w(t,e){-1===t.indexOf(e)&&t.push(e)}function k(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class T{constructor(){this.subscriptions=[]}add(t){return w(this.subscriptions,t),()=>k(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function P(){n=void 0}let S={now:()=>(void 0===n&&S.set(g.isProcessing||h.useManualTiming?g.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(P)}},A=t=>!isNaN(parseFloat(t)),M={current:void 0};class E{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=S.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=S.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=A(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new T);let i=this.events[t].add(e);return"change"===t?()=>{i(),m.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return M.current&&M.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=S.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function V(t,e){return new E(t,e)}let C=t=>Array.isArray(t),D=t=>!!(t&&t.getVelocity);function R(t,e){let i=t.getValue("willChange");if(D(i)&&i.add)return i.add(e);if(!i&&h.WillChange){let i=new h.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let j=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),L="data-"+j("framerAppearId"),F=(t,e)=>i=>e(t(i)),B=(...t)=>t.reduce(F),O=(t,e,i)=>i>e?e:i<t?t:i,I=t=>1e3*t,z=t=>t/1e3,U={layout:0,mainThread:0,waapi:0},N=()=>{},W=()=>{},$=t=>e=>"string"==typeof e&&e.startsWith(t),Y=$("--"),X=$("var(--"),H=t=>!!X(t)&&G.test(t.split("/*")[0].trim()),G=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,K={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},q={...K,transform:t=>O(0,1,t)},_={...K,default:1},Z=t=>Math.round(1e5*t)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>i=>!!("string"==typeof i&&J.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),te=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,s,o,a]=n.match(Q);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ti=t=>O(0,255,t),tn={...K,transform:t=>Math.round(ti(t))},tr={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tn.transform(t)+", "+tn.transform(e)+", "+tn.transform(i)+", "+Z(q.transform(n))+")"},ts={test:tt("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:tr.transform},to=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ta=to("deg"),tl=to("%"),tu=to("px"),th=to("vh"),td=to("vw"),tc={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},tp={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+tl.transform(Z(e))+", "+tl.transform(Z(i))+", "+Z(q.transform(n))+")"},tm={test:t=>tr.test(t)||ts.test(t)||tp.test(t),parse:t=>tr.test(t)?tr.parse(t):tp.test(t)?tp.parse(t):ts.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tr.transform(t):tp.transform(t),getAnimatableNone:t=>{let e=tm.parse(t);return e.alpha=0,tm.transform(e)}},tf=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tg="number",tv="color",ty=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tb(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,o=e.replace(ty,t=>(tm.test(t)?(n.color.push(s),r.push(tv),i.push(tm.parse(t))):t.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(t)):(n.number.push(s),r.push(tg),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:n,types:r}}function tx(t){return tb(t).values}function tw(t){let{split:e,types:i}=tb(t),n=e.length;return t=>{let r="";for(let s=0;s<n;s++)if(r+=e[s],void 0!==t[s]){let e=i[s];e===tg?r+=Z(t[s]):e===tv?r+=tm.transform(t[s]):r+=t[s]}return r}}let tk=t=>"number"==typeof t?0:tm.test(t)?tm.getAnimatableNone(t):t,tT={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Q)?.length||0)+(t.match(tf)?.length||0)>0},parse:tx,createTransformer:tw,getAnimatableNone:function(t){let e=tx(t);return tw(t)(e.map(tk))}};function tP(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tS(t,e){return i=>i>0?e:t}let tA=(t,e,i)=>t+(e-t)*i,tM=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},tE=[ts,tr,tp],tV=t=>tE.find(e=>e.test(t));function tC(t){let e=tV(t);if(N(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tp&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=tP(a,n,t+1/3),s=tP(a,n,t),o=tP(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let tD=(t,e)=>{let i=tC(t),n=tC(e);if(!i||!n)return tS(t,e);let r={...i};return t=>(r.red=tM(i.red,n.red,t),r.green=tM(i.green,n.green,t),r.blue=tM(i.blue,n.blue,t),r.alpha=tA(i.alpha,n.alpha,t),tr.transform(r))},tR=new Set(["none","hidden"]);function tj(t,e){return i=>tA(t,e,i)}function tL(t){return"number"==typeof t?tj:"string"==typeof t?H(t)?tS:tm.test(t)?tD:tO:Array.isArray(t)?tF:"object"==typeof t?tm.test(t)?tD:tB:tS}function tF(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>tL(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function tB(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=tL(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let tO=(t,e)=>{let i=tT.createTransformer(e),n=tb(t),r=tb(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?tR.has(t)&&!r.values.length||tR.has(e)&&!n.values.length?function(t,e){return tR.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):B(tF(function(t,e){let i=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],o=t.indexes[s][n[s]],a=t.values[o]??0;i[r]=a,n[s]++}return i}(n,r),r.values),i):(N(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tS(t,e))};function tI(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tA(t,e,i):tL(t)(t,e)}let tz=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>m.update(e,t),stop:()=>f(e),now:()=>g.isProcessing?g.timestamp:S.now()}},tU=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=Math.round(1e4*t(e/(r-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function tN(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tW(t,e,i){var n,r;let s=Math.max(e-5,0);return n=i-t(s),(r=e-s)?1e3/r*n:0}let t$={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tY(t,e){return t*Math.sqrt(1-e*e)}let tX=["duration","bounce"],tH=["stiffness","damping","mass"];function tG(t,e){return e.some(e=>void 0!==t[e])}function tK(t=t$.visualDuration,e=t$.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:t$.velocity,stiffness:t$.stiffness,damping:t$.damping,mass:t$.mass,isResolvedFromDuration:!1,...t};if(!tG(t,tH)&&tG(t,tX))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*O(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:t$.mass,stiffness:n,damping:r}}else{let i=function({duration:t=t$.duration,bounce:e=t$.bounce,velocity:i=t$.velocity,mass:n=t$.mass}){let r,s;N(t<=I(t$.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=O(t$.minDamping,t$.maxDamping,o),t=O(t$.minDuration,t$.maxDuration,z(t)),o<1?(r=e=>{let n=e*o,r=n*t;return .001-(n-i)/tY(e,o)*Math.exp(-r)},s=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=tY(Math.pow(e,2),o);return(n*i+i-s)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=I(t),isNaN(a))return{stiffness:t$.stiffness,damping:t$.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:t$.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-z(n.velocity||0)}),f=p||0,g=h/(2*Math.sqrt(u*d)),v=a-o,y=z(Math.sqrt(u/d)),b=5>Math.abs(v);if(r||(r=b?t$.restSpeed.granular:t$.restSpeed.default),s||(s=b?t$.restDelta.granular:t$.restDelta.default),g<1){let t=tY(y,g);i=e=>a-Math.exp(-g*y*e)*((f+g*y*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-y*t)*(v+(f+y*v)*t);else{let t=y*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*y*e),n=Math.min(t*e,300);return a-i*((f+g*y*v)*Math.sinh(n)+t*v*Math.cosh(n))/t}}let x={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let n=0===t?f:0;g<1&&(n=0===t?I(f):tW(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(n)<=r&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(tN(x),2e4),e=tU(e=>x.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return x}function tq({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,v=i*e,y=p+v,b=void 0===o?y:o(y);b!==y&&(v=b-p);let x=t=>-v*Math.exp(-t/n),w=t=>b+x(t),k=t=>{let e=x(t),i=w(t);m.done=Math.abs(e)<=u,m.value=m.done?b:i},T=t=>{f(m.value)&&(d=t,c=tK({keyframes:[m.value,g(m.value)],velocity:tW(w,t,m.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,k(t),T(t)),void 0!==d&&t>=d)?c.next(t-d):(e||k(t),m)}}}tK.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min(tN(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:z(r)}}(t,100,tK);return t.ease=e.ease,t.duration=I(e.duration),t.type="keyframes",t};let t_=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tZ(t,e,i,n){if(t===e&&i===n)return u;let r=e=>(function(t,e,i,n,r){let s,o,a=0;do(s=t_(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:t_(r(t),e,n)}let tQ=tZ(.42,0,1,1),tJ=tZ(0,0,.58,1),t0=tZ(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t2=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t5=t=>e=>1-t(1-e),t3=tZ(.33,1.53,.69,.99),t4=t5(t3),t9=t2(t4),t6=t=>(t*=2)<1?.5*t4(t):.5*(2-Math.pow(2,-10*(t-1))),t8=t=>1-Math.sin(Math.acos(t)),t7=t5(t8),et=t2(t8),ee=t=>Array.isArray(t)&&"number"==typeof t[0],ei={linear:u,easeIn:tQ,easeInOut:t0,easeOut:tJ,circIn:t8,circInOut:et,circOut:t7,backIn:t4,backInOut:t9,backOut:t3,anticipate:t6},en=t=>"string"==typeof t,er=t=>{if(ee(t)){W(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,r]=t;return tZ(e,i,n,r)}return en(t)?(W(void 0!==ei[t],`Invalid easing type '${t}'`),ei[t]):t},es=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function eo({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var r;let s=t1(n)?n.map(er):er(n),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if(W(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let n=[],r=i||h.mix||tI,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=B(Array.isArray(e)?e[i]||u:e,s)),n.push(s)}return n}(e,n,r),l=a.length,d=i=>{if(o&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=es(t[n],t[n+1],i);return a[n](r)};return i?e=>d(O(t[0],t[s-1],e)):d}((r=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=es(0,e,n);t.push(tA(i,1,r))}}(e,t.length-1),e}(e),r.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let ea=t=>null!==t;function el(t,{repeat:e,repeatType:i="loop"},n,r=1){let s=t.filter(ea),o=r<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==n?n:s[o]}let eu={decay:tq,inertia:tq,tween:eo,keyframes:eo,spring:tK};function eh(t){"string"==typeof t.type&&(t.type=eu[t.type])}class ed{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ec=t=>t/100;class ep extends ed{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==S.now()&&this.tick(S.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},U.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;eh(t);let{type:e=eo,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=t,{keyframes:o}=t,a=e||eo;a!==eo&&"number"!=typeof o[0]&&(this.mixKeyframes=B(ec,tI(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=tN(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let y=this.currentTime,b=i;if(h){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/o)):"mirror"===d&&(b=s)),y=O(0,1,i)*o}let x=v?{done:!1,value:u[0]}:b.next(y);r&&(x.value=r(x.value));let{done:w}=x;v||null===a||(w=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return k&&p!==tq&&(x.value=el(u,this.options,f,this.speed)),m&&m(x.value),k&&this.finish(),x}then(t,e){return this.finished.then(t,e)}get duration(){return z(this.calculatedDuration)}get time(){return z(this.currentTime)}set time(t){t=I(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(S.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=z(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tz,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(S.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,U.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let em=t=>180*t/Math.PI,ef=t=>ev(em(Math.atan2(t[1],t[0]))),eg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ef,rotateZ:ef,skewX:t=>em(Math.atan(t[1])),skewY:t=>em(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ev=t=>((t%=360)<0&&(t+=360),t),ey=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),eb=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ex={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ey,scaleY:eb,scale:t=>(ey(t)+eb(t))/2,rotateX:t=>ev(em(Math.atan2(t[6],t[5]))),rotateY:t=>ev(em(Math.atan2(-t[2],t[0]))),rotateZ:ef,rotate:ef,skewX:t=>em(Math.atan(t[4])),skewY:t=>em(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ew(t){return+!!t.includes("scale")}function ek(t,e){let i,n;if(!t||"none"===t)return ew(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=ex,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=eg,n=e}if(!n)return ew(e);let s=i[e],o=n[1].split(",").map(eP);return"function"==typeof s?s(o):o[s]}let eT=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return ek(i,e)};function eP(t){return parseFloat(t.trim())}let eS=t=>t===K||t===tu,eA=new Set(["x","y","z"]),eM=y.filter(t=>!eA.has(t)),eE={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>ek(e,"x"),y:(t,{transform:e})=>ek(e,"y")};eE.translateX=eE.x,eE.translateY=eE.y;let eV=new Set,eC=!1,eD=!1,eR=!1;function ej(){if(eD){let t=Array.from(eV).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eM.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eD=!1,eC=!1,eV.forEach(t=>t.complete(eR)),eV.clear()}function eL(){eV.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eD=!0)})}class eF{constructor(t,e,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(eV.add(this),eC||(eC=!0,m.read(eL),m.resolveKeyframes(ej))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let r=n?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eV.delete(this)}cancel(){"scheduled"===this.state&&(eV.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eB=t=>t.startsWith("--");function eO(t){let e;return()=>(void 0===e&&(e=t()),e)}let eI=eO(()=>void 0!==window.ScrollTimeline),ez={},eU=function(t,e){let i=eO(t);return()=>ez[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eN=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,eW={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eN([0,.65,.55,1]),circOut:eN([.55,0,1,.45]),backIn:eN([.31,.01,.66,-.59]),backOut:eN([.33,1.53,.69,.99])};function e$(t){return"function"==typeof t&&"applyToOptions"in t}class eY extends ed{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,W("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return e$(t)&&eU()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?eU()?tU(e,i):"ease-out":ee(e)?eN(e):Array.isArray(e)?e.map(e=>t(e,i)||eW.easeOut):eW[e]}(a,r);Array.isArray(d)&&(h.easing=d),c.value&&U.waapi++;let p={delay:n,duration:r,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(p.pseudoElement=u);let m=t.animate(h,p);return c.value&&m.finished.finally(()=>{U.waapi--}),m}(e,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=el(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eB(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return z(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return z(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=I(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eI())?(this.animation.timeline=t,u):e(this)}}let eX={anticipate:t6,backInOut:t9,circInOut:et};class eH extends eY{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eX&&(t.ease=eX[t.ease])}(t),eh(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new ep({...s,autoplay:!1}),a=I(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eG=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tT.test(t)||"0"===t)&&!t.startsWith("url("));var eK,eq,e_=i(7351);let eZ=new Set(["opacity","clipPath","filter","transform"]),eQ=eO(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eJ extends ed{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=S.now();let d={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,name:a,motionValue:l,element:u,...h},c=u?.KeyframeResolver||eF;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:d}=i;this.resolvedAt=S.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eG(r,e),a=eG(s,e);return N(o===a,`You are trying to animate ${e} from "${r}" to "${s}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||e$(i))&&n)}(t,r,s,o)&&((h.instantAnimations||!a)&&d?.(el(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let c={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},p=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:o}=t;if(!(0,e_.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eQ()&&i&&eZ.has(i)&&("transform"!==i||!l)&&!a&&!n&&"mirror"!==r&&0!==s&&"inertia"!==o}(c)?new eH({...c,element:c.motionValue.owner.current}):new ep(c);p.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eR=!0,eL(),ej(),eR=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e0=t=>null!==t,e1={type:"spring",stiffness:500,damping:25,restSpeed:10},e2=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e5={type:"keyframes",duration:.8},e3={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e4=(t,e)=>{let{keyframes:i}=e;return i.length>2?e5:b.has(t)?t.startsWith("scale")?e2(i[1]):e1:e3},e9=function(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=arguments.length>4?arguments[4]:void 0,s=arguments.length>5?arguments[5]:void 0;return o=>{let a=l(n,t)||{},u=a.delay||n.delay||0,{elapsed:d=0}=n;d-=I(u);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-d,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function(t){let{when:e,delay:i,delayChildren:n,staggerChildren:r,staggerDirection:s,repeat:o,repeatType:a,repeatDelay:l,from:u,elapsed:h,...d}=t;return!!Object.keys(d).length}(a)&&Object.assign(c,e4(t,c)),c.duration&&(c.duration=I(c.duration)),c.repeatDelay&&(c.repeatDelay=I(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let p=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(p=!0)),(h.instantAnimations||h.skipAnimations)&&(p=!0,c.duration=0,c.delay=0),c.allowFlatten=!a.type&&!a.ease,p&&!s&&void 0!==e.get()){let t=function(t,e,i){let{repeat:n,repeatType:r="loop"}=e,s=t.filter(e0),o=n&&"loop"!==r&&n%2==1?0:s.length-1;return s[o]}(c.keyframes,a);if(void 0!==t)return void m.update(()=>{c.onUpdate(t),c.onComplete()})}return a.isSync?new ep(c):new eJ(c)}};function e6(t,e){let{delay:i=0,transitionOverride:n,type:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{transition:s=t.getDefaultTransition(),transitionEnd:o,...u}=e;n&&(s=n);let h=[],d=r&&t.animationState&&t.animationState.getState()[r];for(let e in u){var c;let n=t.getValue(e,null!=(c=t.latestValues[e])?c:null),r=u[e];if(void 0===r||d&&function(t,e){let{protectedKeys:i,needsAnimating:n}=t,r=i.hasOwnProperty(e)&&!0!==n[e];return n[e]=!1,r}(d,e))continue;let o={delay:i,...l(s||{},e)},a=n.get();if(void 0!==a&&!n.isAnimating&&!Array.isArray(r)&&r===a&&!o.velocity)continue;let p=!1;if(window.MotionHandoffAnimation){let i=t.props[L];if(i){let t=window.MotionHandoffAnimation(i,e,m);null!==t&&(o.startTime=t,p=!0)}}R(t,e),n.start(e9(e,n,r,t.shouldReduceMotion&&x.has(e)?{type:!1}:o,t,p));let f=n.animation;f&&h.push(f)}return o&&Promise.all(h).then(()=>{m.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=a(t,e)||{};for(let e in r={...r,...i}){var s;let i=C(s=r[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,V(i))}}(t,o)})}),h}function e8(t,e){var i;let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=a(t,e,"exit"===n.type?null==(i=t.presenceContext)?void 0:i.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(s=n.transitionOverride);let o=r?()=>Promise.all(e6(t,r,n)):()=>Promise.resolve(),l=t.variantChildren&&t.variantChildren.size?function(){let i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,{delayChildren:r=0,staggerChildren:o,staggerDirection:a}=s;return function(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,o=arguments.length>6?arguments[6]:void 0,a=[],l=t.variantChildren.size,u=(l-1)*r,h="function"==typeof n,d=h?t=>n(t,l):1===s?function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return t*r}:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return u-t*r};return Array.from(t.variantChildren).sort(e7).forEach((t,r)=>{t.notify("AnimationStart",e),a.push(e8(t,e,{...o,delay:i+(h?0:n)+d(r)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,i,r,o,a,n)}:()=>Promise.resolve(),{when:u}=s;if(!u)return Promise.all([o(),l(n.delay)]);{let[t,e]="beforeChildren"===u?[o,l]:[l,o];return t().then(()=>e())}}function e7(t,e){return t.sortNodePosition(e)}function it(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function ie(t){return"string"==typeof t||Array.isArray(t)}let ii=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ir=["initial",...ii],is=ir.length,io=[...ii].reverse(),ia=ii.length;function il(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iu(){return{animate:il(!0),whileInView:il(),whileHover:il(),whileTap:il(),whileDrag:il(),whileFocus:il(),exit:il()}}class ih{update(){}constructor(t){this.isMounted=!1,this.node=t}}class id extends ih{updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null==(t=this.unmountControls)||t.call(this)}constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(e=>{let{animation:i,options:n}=e;return function(t,e){let i,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(t.notify("AnimationStart",e),Array.isArray(e))i=Promise.all(e.map(e=>e8(t,e,n)));else if("string"==typeof e)i=e8(t,e,n);else{let r="function"==typeof e?a(t,e,n.custom):e;i=Promise.all(e6(t,r,n))}return i.then(()=>{t.notify("AnimationComplete",e)})}(t,i,n)})),i=iu(),n=!0,s=e=>(i,n)=>{var r;let s=a(t,n,"exit"===e?null==(r=t.presenceContext)?void 0:r.custom:void 0);if(s){let{transition:t,transitionEnd:e,...n}=s;i={...i,...n,...e}}return i};function o(o){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<is;t++){let n=ir[t],r=e.props[n];(ie(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},h=[],d=new Set,c={},p=1/0;for(let e=0;e<ia;e++){var m,f;let a=io[e],g=i[a],v=void 0!==l[a]?l[a]:u[a],y=ie(v),b=a===o?g.isActive:null;!1===b&&(p=e);let x=v===u[a]&&v!==l[a]&&y;if(x&&n&&t.manuallyAnimateOnMount&&(x=!1),g.protectedKeys={...c},!g.isActive&&null===b||!v&&!g.prevProp||r(v)||"boolean"==typeof v)continue;let w=(m=g.prevProp,"string"==typeof(f=v)?f!==m:!!Array.isArray(f)&&!it(f,m)),k=w||a===o&&g.isActive&&!x&&y||e>p&&y,T=!1,P=Array.isArray(v)?v:[v],S=P.reduce(s(a),{});!1===b&&(S={});let{prevResolvedValues:A={}}=g,M={...A,...S},E=e=>{k=!0,d.has(e)&&(T=!0,d.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in M){let e=S[t],i=A[t];if(c.hasOwnProperty(t))continue;let n=!1;(C(e)&&C(i)?it(e,i):e===i)?void 0!==e&&d.has(t)?E(t):g.protectedKeys[t]=!0:null!=e?E(t):d.add(t)}g.prevProp=v,g.prevResolvedValues=S,g.isActive&&(c={...c,...S}),n&&t.blockInitialAnimation&&(k=!1);let V=!(x&&w)||T;k&&V&&h.push(...P.map(t=>({animation:t,options:{type:a}})))}if(d.size){let e={};if("boolean"!=typeof l.initial){let i=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=null!=n?n:null}),h.push({animation:e})}let g=!!h.length;return n&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(g=!1),n=!1,g?e(h):Promise.resolve()}return{animateChanges:o,setActive:function(e,n){var r;if(i[e].isActive===n)return Promise.resolve();null==(r=t.variantChildren)||r.forEach(t=>{var i;return null==(i=t.animationState)?void 0:i.setActive(e,n)}),i[e].isActive=n;let s=o(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iu(),n=!0}}}(t))}}let ic=0;class ip extends ih{update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}constructor(){super(...arguments),this.id=ic++}}let im={x:!1,y:!1};function ig(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let iv=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iy(t){return{point:{x:t.pageX,y:t.pageY}}}let ib=t=>e=>iv(e)&&t(e,iy(e));function ix(t,e,i,n){return ig(t,e,ib(i),n)}function iw(t){let{top:e,left:i,right:n,bottom:r}=t;return{x:{min:i,max:n},y:{min:e,max:r}}}function ik(t){return t.max-t.min}function iT(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;t.origin=n,t.originPoint=tA(e.min,e.max,t.origin),t.scale=ik(i)/ik(e),t.translate=tA(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iP(t,e,i,n){iT(t.x,e.x,i.x,n?n.originX:void 0),iT(t.y,e.y,i.y,n?n.originY:void 0)}function iS(t,e,i){t.min=i.min+e.min,t.max=t.min+ik(e)}function iA(t,e,i){t.min=e.min-i.min,t.max=t.min+ik(e)}function iM(t,e,i){iA(t.x,e.x,i.x),iA(t.y,e.y,i.y)}let iE=()=>({translate:0,scale:1,origin:0,originPoint:0}),iV=()=>({x:iE(),y:iE()}),iC=()=>({min:0,max:0}),iD=()=>({x:iC(),y:iC()});function iR(t){return[t("x"),t("y")]}function ij(t){return void 0===t||1===t}function iL(t){let{scale:e,scaleX:i,scaleY:n}=t;return!ij(e)||!ij(i)||!ij(n)}function iF(t){return iL(t)||iB(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iB(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iO(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function iI(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3?arguments[3]:void 0,r=arguments.length>4?arguments[4]:void 0;t.min=iO(t.min,e,i,n,r),t.max=iO(t.max,e,i,n,r)}function iz(t,e){let{x:i,y:n}=e;iI(t.x,i.translate,i.scale,i.originPoint),iI(t.y,n.translate,n.scale,n.originPoint)}function iU(t,e){t.min=t.min+e,t.max=t.max+e}function iN(t,e,i,n){let r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:.5,s=tA(t.min,t.max,r);iI(t,e,i,s,n)}function iW(t,e){iN(t.x,e.x,e.scaleX,e.scale,e.originX),iN(t.y,e.y,e.scaleY,e.scale,e.originY)}function i$(t,e){return iw(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let iY=t=>{let{current:e}=t;return e?e.ownerDocument.defaultView:null};function iX(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iH=(t,e)=>Math.abs(t-e);class iG{updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}constructor(t,e,{transformPagePoint:i,contextWindow:n=window,dragSnapToOrigin:r=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=i_(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iH(t.x,e.x)**2+iH(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=g;this.history.push({...n,timestamp:r});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iK(e,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=i_("pointercancel"===t.type?this.lastMoveEventInfo:iK(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!iv(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=s,this.contextWindow=n||window;let o=iK(iy(t),this.transformPagePoint),{point:a}=o,{timestamp:l}=g;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=e;u&&u(t,i_(o,this.history)),this.removeListeners=B(ix(this.contextWindow,"pointermove",this.handlePointerMove),ix(this.contextWindow,"pointerup",this.handlePointerUp),ix(this.contextWindow,"pointercancel",this.handlePointerUp))}}function iK(t,e){return e?{point:e(t.point)}:t}function iq(t,e){return{x:t.x-e.x,y:t.y-e.y}}function i_(t,e){let{point:i}=t;return{point:i,delta:iq(i,iZ(e)),offset:iq(i,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=iZ(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>I(.1)));)i--;if(!n)return{x:0,y:0};let s=z(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iZ(t){return t[t.length-1]}function iQ(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iJ(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function i0(t,e,i){return{min:i1(t,e),max:i1(t,i)}}function i1(t,e){return"number"==typeof t?t:t[e]||0}let i2=new WeakMap;class i5{start(t){let{snapToCursor:e=!1,distanceThreshold:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new iG(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iy(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(im[t])return null;else return im[t]=!0,()=>{im[t]=!1};return im.x||im.y?null:(im.x=im.y=!0,()=>{im.x=im.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iR(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=ik(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&m.postRender(()=>r(t,e)),R(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>iR(t=>{var e;return"paused"===this.getAnimationState(t)&&(null==(e=this.getAxisMotionValue(t).animation)?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,distanceThreshold:i,contextWindow:iY(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,n=e||this.latestPanInfo,r=this.isDragging;if(this.cancel(),!r||!n||!i)return;let{velocity:s}=n;this.startAnimation(s);let{onDragEnd:o}=this.getProps();o&&m.postRender(()=>o(i,n))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!i3(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,e,i){let{min:n,max:r}=e;return void 0!==n&&t<n?t=i?tA(n,t,i.min):Math.max(t,n):void 0!==r&&t>r&&(t=i?tA(r,t,i.max):Math.min(t,r)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(t=this.visualElement.projection)?void 0:t.layout,r=this.constraints;e&&iX(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(t,e){let{top:i,left:n,bottom:r,right:s}=e;return{x:iQ(t.x,n,s),y:iQ(t.y,i,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.35;return!1===t?t=0:!0===t&&(t=.35),{x:i0(t,"left","right"),y:i0(t,"top","bottom")}}(i),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&iR(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iX(e))return!1;let n=e.current;W(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=i$(t,i),{scroll:r}=e;return r&&(iU(n.x,r.offset.x),iU(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o=(t=r.layout.layoutBox,{x:iJ(t.x,s.x),y:iJ(t.y,s.y)});if(i){let t=i(function(t){let{x:e,y:i}=t;return{top:i.min,right:e.max,bottom:i.max,left:e.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iw(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iR(o=>{if(!i3(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return R(this.visualElement,t),i.start(e9(t,i,0,e,this.visualElement,!1))}stopAnimation(){iR(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iR(t=>{var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.pause()})}getAnimationState(t){var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.state}getAxisMotionValue(t){let e="_drag".concat(t.toUpperCase()),i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iR(e=>{let{drag:i}=this.getProps();if(!i3(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-tA(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iX(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iR(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=ik(t),r=ik(e);return r>n?i=es(e.min,e.max-n,t.min):n>r&&(i=es(t.min,t.max-r,e.min)),O(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iR(e=>{if(!i3(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(tA(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;i2.set(this.visualElement,this);let t=ix(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iX(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),m.read(e);let r=ig(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",t=>{let{delta:e,hasLayoutChanged:i}=t;this.isDragging&&i&&(iR(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iD(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}}function i3(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i4 extends ih{mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}constructor(t){super(t),this.removeGroupControls=u,this.removeListeners=u,this.controls=new i5(t)}}let i9=t=>(e,i)=>{t&&m.postRender(()=>t(e,i))};class i6 extends ih{onPointerDown(t){this.session=new iG(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iY(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:i9(t),onStart:i9(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&m.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=ix(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}constructor(){super(...arguments),this.removePointerDownListener=u}}var i8=i(5155);let{schedule:i7}=p(queueMicrotask,!1);var nt=i(2115),ne=i(2082),ni=i(869);let nn=(0,nt.createContext)({}),nr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ns(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let no={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tu.test(t))return t;else t=parseFloat(t);let i=ns(t,e.target.x),n=ns(t,e.target.y);return"".concat(i,"% ").concat(n,"%")}},na={},nl=!1;class nu extends nt.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;for(let t in nd)na[t]=nd[t],Y(t)&&(na[t].isCSSVariable=!0);r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),nl&&r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),nr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,nl=!0,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?s.promote():s.relegate()||m.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),i7.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nh(t){let[e,i]=(0,ne.xQ)(),n=(0,nt.useContext)(ni.L);return(0,i8.jsx)(nu,{...t,layoutGroup:n,switchLayoutGroup:(0,nt.useContext)(nn),isPresent:e,safeToRemove:i})}let nd={borderRadius:{...no,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:no,borderTopRightRadius:no,borderBottomLeftRadius:no,borderBottomRightRadius:no,boxShadow:{correct:(t,e)=>{let{treeScale:i,projectionDelta:n}=e,r=tT.parse(t);if(r.length>5)return t;let s=tT.createTransformer(t),o=+("number"!=typeof r[0]),a=n.x.scale*i.x,l=n.y.scale*i.y;r[0+o]/=a,r[1+o]/=l;let u=tA(a,l,.5);return"number"==typeof r[2+o]&&(r[2+o]/=u),"number"==typeof r[3+o]&&(r[3+o]/=u),s(r)}}};var nc=i(6983);function np(t){return(0,nc.G)(t)&&"ownerSVGElement"in t}let nm=(t,e)=>t.depth-e.depth;class nf{add(t){w(this.children,t),this.isDirty=!0}remove(t){k(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nm),this.isDirty=!1,this.children.forEach(t)}constructor(){this.children=[],this.isDirty=!1}}function ng(t){return D(t)?t.get():t}let nv=["TopLeft","TopRight","BottomLeft","BottomRight"],ny=nv.length,nb=t=>"string"==typeof t?parseFloat(t):t,nx=t=>"number"==typeof t||tu.test(t);function nw(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nk=nP(0,.5,t7),nT=nP(.5,.95,u);function nP(t,e,i){return n=>n<t?0:n>e?1:i(es(t,e,n))}function nS(t,e){t.min=e.min,t.max=e.max}function nA(t,e){nS(t.x,e.x),nS(t.y,e.y)}function nM(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nE(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function nV(t,e,i,n,r){let[s,o,a]=i;!function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,r=arguments.length>4?arguments[4]:void 0,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:t,o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:t;if(tl.test(e)&&(e=parseFloat(e),e=tA(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tA(s.min,s.max,n);t===s&&(a-=e),t.min=nE(t.min,e,i,a,r),t.max=nE(t.max,e,i,a,r)}(t,e[s],e[o],e[a],e.scale,n,r)}let nC=["x","scaleX","originX"],nD=["y","scaleY","originY"];function nR(t,e,i,n){nV(t.x,e,nC,i?i.x:void 0,n?n.x:void 0),nV(t.y,e,nD,i?i.y:void 0,n?n.y:void 0)}function nj(t){return 0===t.translate&&1===t.scale}function nL(t){return nj(t.x)&&nj(t.y)}function nF(t,e){return t.min===e.min&&t.max===e.max}function nB(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nO(t,e){return nB(t.x,e.x)&&nB(t.y,e.y)}function nI(t){return ik(t.x)/ik(t.y)}function nz(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nU{add(t){w(this.members,t),t.scheduleRender()}remove(t){if(k(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}constructor(){this.members=[]}}let nN={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nW=["","X","Y","Z"],n$=0;function nY(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function nX(t){let{attachResizeListener:e,defaultParent:i,measureScroll:n,checkIsScrollRoot:r,resetTransform:s}=t;return class{addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new T),this.eventHandlers.get(t).add(e)}notifyListeners(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];let r=this.eventHandlers.get(t);r&&r.notify(...i)}hasListeners(t){return this.eventHandlers.has(t)}mount(t){if(this.instance)return;this.isSVG=np(t)&&!(np(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),e){let i,n=0,r=()=>this.root.updateBlockedByResize=!1;m.read(()=>{n=window.innerWidth}),e(t,()=>{let t=window.innerWidth;t!==n&&(n=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=S.now(),n=r=>{let{timestamp:s}=r,o=s-i;o>=250&&(f(n),t(o-e))};return m.setup(n,!0),()=>f(n)}(r,250),nr.hasAnimatedSinceResize&&(nr.hasAnimatedSinceResize=!1,this.nodes.forEach(n0)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",t=>{let{delta:e,hasLayoutChanged:i,hasRelativeLayoutChanged:n,layout:s}=t;if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||r.getDefaultTransition()||n8,{onLayoutAnimationStart:a,onLayoutAnimationComplete:u}=r.getProps(),h=!this.targetLayout||!nO(this.targetLayout,s),d=!i&&n;if(this.options.layoutRoot||this.resumeFrom||d||i&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...l(o,"layout"),onPlay:a,onComplete:u};(r.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,d)}else i||n0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n5),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[L];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",m,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nZ);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(nQ);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(nJ),this.nodes.forEach(nH),this.nodes.forEach(nG)):this.nodes.forEach(nQ),this.clearAllSnapshots();let t=S.now();g.delta=O(0,1e3/60,t-g.timestamp),g.timestamp=t,g.isProcessing=!0,v.update.process(g),v.preRender.process(g),v.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,i7.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(n_),this.sharedNodes.forEach(n3)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||ik(this.snapshot.measuredBox.x)||ik(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iD(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nL(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,r=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||iF(this.latestValues)||r)&&(s(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(){var t;let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],i=this.measurePageBox(),n=this.removeElementScroll(i);return e&&(n=this.removeTransform(n)),re((t=n).x),re(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return iD();let i=e.measureViewportBox();if(!((null==(t=this.scroll)?void 0:t.wasRoot)||this.path.some(rn))){let{scroll:t}=this.root;t&&(iU(i.x,t.offset.x),iU(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=iD();if(nA(i,t),null==(e=this.scroll)?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let n=this.path[e],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&nA(i,t),iU(i.x,r.offset.x),iU(i.y,r.offset.y))}return i}applyTransform(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=iD();nA(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iW(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),iF(n.latestValues)&&iW(i,n.latestValues)}return iF(this.latestValues)&&iW(i,this.latestValues),i}removeTransform(t){let e=iD();nA(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iF(i.latestValues))continue;iL(i.latestValues)&&i.updateSnapshot();let n=iD();nA(n,i.measurePageBox()),nR(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return iF(this.latestValues)&&nR(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(){var t,e,i,n;let r=arguments.length>0&&void 0!==arguments[0]&&arguments[0],s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==s;if(!(r||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iD(),this.relativeTargetOrigin=iD(),iM(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=iD(),this.targetWithTransforms=iD()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),e=this.target,i=this.relativeTarget,n=this.relativeParent.target,iS(e.x,i.x,n.x),iS(e.y,i.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nA(this.target,this.layout.layoutBox),iz(this.target,this.targetDelta)):nA(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iD(),this.relativeTargetOrigin=iD(),iM(this.relativeTargetOrigin,this.target,t.target),nA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}c.value&&nN.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iL(this.parent.latestValues)||iB(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty))&&(n=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===g.timestamp&&(n=!1),n)return;let{layout:r,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||s))return;nA(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i){let n,r,s=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){r=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iW(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,iz(t,r)),s&&iF(n.latestValues)&&iW(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=iD());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nM(this.prevProjectionDelta.x,this.projectionDelta.x),nM(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iP(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&nz(this.projectionDelta.x,this.prevProjectionDelta.x)&&nz(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),c.value&&nN.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){var t;let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(null==(t=this.options.visualElement)||t.scheduleRender(),e){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iV(),this.projectionDelta=iV(),this.projectionDeltaWithTransform=iV()}setAnimationOrigin(t){let e,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=iV();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!i;let a=iD(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(n6));this.animationProgress=0,this.mixTargetDelta=i=>{let n=i/1e3;if(n4(o.x,t.x,n),n4(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,g;iM(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=n,n9(p.x,m.x,f.x,g),n9(p.y,m.y,f.y,g),e&&(u=this.relativeTarget,c=e,nF(u.x,c.x)&&nF(u.y,c.y))&&(this.isProjectionDirty=!1),e||(e=iD()),nA(e,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){var o,a,l,u;r?(t.opacity=tA(0,null!=(o=i.opacity)?o:1,nk(n)),t.opacityExit=tA(null!=(a=e.opacity)?a:1,0,nT(n))):s&&(t.opacity=tA(null!=(l=e.opacity)?l:1,null!=(u=i.opacity)?u:1,n));for(let r=0;r<ny;r++){let s="border".concat(nv[r],"Radius"),o=nw(e,s),a=nw(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nx(o)===nx(a)?(t[s]=Math.max(tA(nb(o),nb(a),n),0),(tl.test(a)||tl.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=tA(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){var e,i,n;this.notifyListeners("animationStart"),null==(e=this.currentAnimation)||e.stop(),null==(n=this.resumingFrom)||null==(i=n.currentAnimation)||i.stop(),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{nr.hasAnimatedSinceResize=!0,U.layout++,this.motionValue||(this.motionValue=V(0)),this.currentAnimation=function(t,e,i){let n=D(t)?t:V(t);return n.start(e9("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{U.layout--},onComplete:()=>{U.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&ri(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||iD();let e=ik(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=ik(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}nA(e,i),iW(e,r),iP(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nU),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null==(t=this.getStack())?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null==(t=this.getStack())?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote(){let{needsReset:t,transition:e,preserveFollowOpacity:i}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&nY("z",t,n,this.animationValues);for(let e=0;e<nW.length;e++)nY("rotate".concat(nW[e]),t,n,this.animationValues),nY("skew".concat(nW[e]),t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=ng(null==e?void 0:e.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=ng(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!iF(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let r=n.animationValues||n.latestValues;this.applyTransformsToTarget();let s=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=(null==i?void 0:i.z)||0;if((r||s||o)&&(n="translate3d(".concat(r,"px, ").concat(s,"px, ").concat(o,"px) ")),(1!==e.x||1!==e.y)&&(n+="scale(".concat(1/e.x,", ").concat(1/e.y,") ")),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n="perspective(".concat(t,"px) ").concat(n)),e&&(n+="rotate(".concat(e,"deg) ")),r&&(n+="rotateX(".concat(r,"deg) ")),s&&(n+="rotateY(".concat(s,"deg) ")),o&&(n+="skewX(".concat(o,"deg) ")),a&&(n+="skewY(".concat(a,"deg) "))}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+="scale(".concat(a,", ").concat(l,")")),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r);i&&(s=i(r,s)),t.transform=s;let{x:o,y:a}=this.projectionDelta;if(t.transformOrigin="".concat(100*o.origin,"% ").concat(100*a.origin,"% 0"),n.animationValues){var l,u;t.opacity=n===this?null!=(u=null!=(l=r.opacity)?l:this.latestValues.opacity)?u:1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit}else t.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0;for(let e in na){if(void 0===r[e])continue;let{correct:i,applyTo:o,isCSSVariable:a}=na[e],l="none"===s?r[e]:i(r[e],n);if(o){let e=o.length;for(let i=0;i<e;i++)t[o[i]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=n===this?ng(null==e?void 0:e.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null==(e=t.currentAnimation)?void 0:e.stop()}),this.root.nodes.forEach(nZ),this.root.sharedNodes.clear()}constructor(t={},e=null==i?void 0:i()){this.id=n$++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,c.value&&(nN.nodes=nN.calculatedTargetDeltas=nN.calculatedProjections=0),this.nodes.forEach(nK),this.nodes.forEach(n1),this.nodes.forEach(n2),this.nodes.forEach(nq),c.addProjectionMetrics&&c.addProjectionMetrics(nN)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=e?e.root||e:this,this.path=e?[...e.path,e]:[],this.parent=e,this.depth=e?e.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nf)}}}function nH(t){t.updateLayout()}function nG(t){var e;let i=(null==(e=t.resumeFrom)?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:n}=t.layout,{animationType:r}=t.options,s=i.source!==t.layout.source;"size"===r?iR(t=>{let n=s?i.measuredBox[t]:i.layoutBox[t],r=ik(n);n.min=e[t].min,n.max=n.min+r}):ri(r,i.layoutBox,e)&&iR(n=>{let r=s?i.measuredBox[n]:i.layoutBox[n],o=ik(e[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=iV();iP(o,e,i.layoutBox);let a=iV();s?iP(a,t.applyTransform(n,!0),i.measuredBox):iP(a,e,i.layoutBox);let l=!nL(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=iD();iM(o,i.layoutBox,r.layoutBox);let a=iD();iM(a,e,s.layoutBox),nO(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nK(t){c.value&&nN.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nq(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function n_(t){t.clearSnapshot()}function nZ(t){t.clearMeasurements()}function nQ(t){t.isLayoutDirty=!1}function nJ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function n0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function n1(t){t.resolveTargetDelta()}function n2(t){t.calcProjection()}function n5(t){t.resetSkewAndRotation()}function n3(t){t.removeLeadSnapshot()}function n4(t,e,i){t.translate=tA(e.translate,0,i),t.scale=tA(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function n9(t,e,i,n){t.min=tA(e.min,i.min,n),t.max=tA(e.max,i.max,n)}function n6(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let n8={duration:.45,ease:[.4,0,.1,1]},n7=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rt=n7("applewebkit/")&&!n7("chrome/")?Math.round:u;function re(t){t.min=rt(t.min),t.max=rt(t.max)}function ri(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nI(e)-nI(i)))}function rn(t){var e;return t!==t.root&&(null==(e=t.scroll)?void 0:e.wasRoot)}let rr=nX({attachResizeListener:(t,e)=>ig(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rs={current:void 0},ro=nX({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rs.current){let t=new rr({});t.mount(window),t.setOptions({layoutScroll:!0}),rs.current=t}return rs.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ra(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function rl(t){return!("touch"===t.pointerType||im.x||im.y)}function ru(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&m.postRender(()=>r(e,iy(e)))}class rh extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=ra(t,i),o=t=>{if(!rl(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{rl(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,(t,e)=>(ru(this.node,e,"Start"),t=>ru(this.node,t,"End"))))}unmount(){}}class rd extends ih{onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=B(ig(this.node.current,"focus",()=>this.onFocus()),ig(this.node.current,"blur",()=>this.onBlur()))}unmount(){}constructor(){super(...arguments),this.isActive=!1}}let rc=(t,e)=>!!e&&(t===e||rc(t,e.parentElement)),rp=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rm=new WeakSet;function rf(t){return e=>{"Enter"===e.key&&t(e)}}function rg(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let rv=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=rf(()=>{if(rm.has(i))return;rg(i,"down");let t=rf(()=>{rg(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>rg(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function ry(t){return iv(t)&&!(im.x||im.y)}function rb(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&m.postRender(()=>r(e,iy(e)))}class rx extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=ra(t,i),o=t=>{let n=t.currentTarget;if(!ry(t))return;rm.add(n);let s=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),rm.has(n)&&rm.delete(n),ry(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,n===window||n===document||i.useGlobalTarget||rc(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),(0,e_.s)(t))&&(t.addEventListener("focus",t=>rv(t,r)),rp.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(rb(this.node,e,"Start"),(t,e)=>{let{success:i}=e;return rb(this.node,t,i?"End":"Cancel")}),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rw=new WeakMap,rk=new WeakMap,rT=t=>{let e=rw.get(t.target);e&&e(t)},rP=t=>{t.forEach(rT)},rS={some:0,all:1};class rA extends ih{startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rS[n]};return function(t,e,i){let n=function(t){let{root:e,...i}=t,n=e||document;rk.has(n)||rk.set(n,{});let r=rk.get(n),s=JSON.stringify(i);return r[s]||(r[s]=new IntersectionObserver(rP,{root:e,...i})),r[s]}(e);return rw.set(t,i),n.observe(t),()=>{rw.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function(t){let{viewport:e={}}=t,{viewport:i={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=>e[t]!==i[t]}(t,e))&&this.startObserver()}unmount(){}constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}}let rM=(0,nt.createContext)({strict:!1});var rE=i(1508);let rV=(0,nt.createContext)({});function rC(t){return r(t.animate)||ir.some(e=>ie(t[e]))}function rD(t){return!!(rC(t)||t.variants)}function rR(t){return Array.isArray(t)?t.join(" "):t}var rj=i(8972);let rL={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rF={};for(let t in rL)rF[t]={isEnabled:e=>rL[t].some(t=>!!e[t])};let rB=Symbol.for("motionComponentSymbol");var rO=i(845),rI=i(7494);function rz(t,e){let{layout:i,layoutId:n}=e;return b.has(t)||t.startsWith("origin")||(i||void 0!==n)&&(!!na[t]||"opacity"===t)}let rU=(t,e)=>e&&"number"==typeof t?e.transform(t):t,rN={...K,transform:Math.round},rW={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:ta,rotateX:ta,rotateY:ta,rotateZ:ta,scale:_,scaleX:_,scaleY:_,scaleZ:_,skew:ta,skewX:ta,skewY:ta,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:q,originX:tc,originY:tc,originZ:tu,zIndex:rN,fillOpacity:q,strokeOpacity:q,numOctaves:rN},r$={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rY=y.length;function rX(t,e,i){let{style:n,vars:r,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(b.has(t)){o=!0;continue}if(Y(t)){r[t]=i;continue}{let e=rU(i,rW[t]);t.startsWith("origin")?(a=!0,s[t]=e):n[t]=e}}if(!e.transform&&(o||i?n.transform=function(t,e,i){let n="",r=!0;for(let s=0;s<rY;s++){let o=y[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=rU(a,rW[o]);if(!l){r=!1;let e=r$[o]||o;n+="".concat(e,"(").concat(t,") ")}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;n.transformOrigin="".concat(t," ").concat(e," ").concat(i)}}let rH=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rG(t,e,i){for(let n in e)D(e[n])||rz(n,i)||(t[n]=e[n])}let rK={offset:"stroke-dashoffset",array:"stroke-dasharray"},rq={offset:"strokeDashoffset",array:"strokeDasharray"};function r_(t,e,i,n,r){var s,o;let{attrX:a,attrY:l,attrScale:u,pathLength:h,pathSpacing:d=1,pathOffset:c=0,...p}=e;if(rX(t,p,n),i){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:m,style:f}=t;m.transform&&(f.transform=m.transform,delete m.transform),(f.transform||m.transformOrigin)&&(f.transformOrigin=null!=(s=m.transformOrigin)?s:"50% 50%",delete m.transformOrigin),f.transform&&(f.transformBox=null!=(o=null==r?void 0:r.transformBox)?o:"fill-box",delete m.transformBox),void 0!==a&&(m.x=a),void 0!==l&&(m.y=l),void 0!==u&&(m.scale=u),void 0!==h&&function(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,r=!(arguments.length>4)||void 0===arguments[4]||arguments[4];t.pathLength=1;let s=r?rK:rq;t[s.offset]=tu.transform(-n);let o=tu.transform(e),a=tu.transform(i);t[s.array]="".concat(o," ").concat(a)}(m,h,d,c,!1)}let rZ=()=>({...rH(),attrs:{}}),rQ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),rJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r0(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||rJ.has(t)}let r1=t=>!r0(t);try{!function(t){"function"==typeof t&&(r1=e=>e.startsWith("on")?!r0(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}let r2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r5(t){if("string"!=typeof t||t.includes("-"));else if(r2.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var r3=i(2885);let r4=t=>(e,i)=>{let n=(0,nt.useContext)(rV),s=(0,nt.useContext)(rO.t),a=()=>(function(t,e,i,n){let{scrapeMotionValuesFromProps:s,createRenderState:a}=t;return{latestValues:function(t,e,i,n){let s={},a=n(t,{});for(let t in a)s[t]=ng(a[t]);let{initial:l,animate:u}=t,h=rC(t),d=rD(t);e&&d&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!r(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=o(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(e,i,n,s),renderState:a()}})(t,e,n,s);return i?a():(0,r3.M)(a)};function r9(t,e,i){let{style:n}=t,r={};for(let o in n){var s;(D(n[o])||e.style&&D(e.style[o])||rz(o,t)||(null==i||null==(s=i.getValue(o))?void 0:s.liveStyle)!==void 0)&&(r[o]=n[o])}return r}let r6={useVisualState:r4({scrapeMotionValuesFromProps:r9,createRenderState:rH})};function r8(t,e,i){let n=r9(t,e,i);for(let i in t)(D(t[i])||D(e[i]))&&(n[-1!==y.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let r7={useVisualState:r4({scrapeMotionValuesFromProps:r8,createRenderState:rZ})},st=t=>e=>e.test(t),se=[K,tu,tl,ta,td,th,{test:t=>"auto"===t,parse:t=>t}],si=t=>se.find(st(t)),sn=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),sr=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ss=t=>/^0[^.\s]+$/u.test(t),so=new Set(["brightness","contrast","saturate","opacity"]);function sa(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(Q)||[];if(!n)return t;let r=i.replace(n,""),s=+!!so.has(e);return n!==i&&(s*=100),e+"("+s+r+")"}let sl=/\b([a-z-]*)\(.*?\)/gu,su={...tT,getAnimatableNone:t=>{let e=t.match(sl);return e?e.map(sa).join(" "):t}},sh={...rW,color:tm,backgroundColor:tm,outlineColor:tm,fill:tm,stroke:tm,borderColor:tm,borderTopColor:tm,borderRightColor:tm,borderBottomColor:tm,borderLeftColor:tm,filter:su,WebkitFilter:su},sd=t=>sh[t];function sc(t,e){let i=sd(t);return i!==su&&(i=tT),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let sp=new Set(["auto","none","0"]);class sm extends eF{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&H(n=n.trim())){let r=function t(e,i,n=1){W(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,s]=function(t){let e=sr.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${i??n}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return sn(t)?parseFloat(t):t}return H(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!x.has(i)||2!==t.length)return;let[n,r]=t,s=si(n),o=si(r);if(s!==o)if(eS(s)&&eS(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eE[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||ss(n)))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!sp.has(e)&&tb(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=sc(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eE[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=eE[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let sf=[...se,tm,tT],sg=t=>sf.find(st(t)),sv={current:null},sy={current:!1},sb=new WeakMap,sx=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sw{scrapeMotionValuesFromProps(t,e,i){return{}}mount(t){this.current=t,sb.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sy.current||function(){if(sy.current=!0,rj.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sv.current=t.matches;t.addEventListener("change",e),e()}else sv.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sv.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=b.has(t);n&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&m.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rF){let e=rF[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iD()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sx.length;e++){let i=sx[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(D(r))t.addValue(n,r);else if(D(s))t.addValue(n,V(r,{owner:t}));else if(s!==r)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,V(void 0!==e?e:r,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=V(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let n=void 0===this.latestValues[t]&&this.current?null!=(i=this.getBaseTargetFromProps(this.props,t))?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&(sn(n)||ss(n))?n=parseFloat(n):!sg(n)&&tT.test(e)&&(n=sc(t,e)),this.setBaseTarget(t,D(n)?n.get():n)),D(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){var n;let r=o(this.props,i,null==(n=this.presenceContext)?void 0:n.custom);r&&(e=r[t])}if(i&&void 0!==e)return e;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||D(r)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new T),this.events[t].add(e)}notify(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];this.events[t]&&this.events[t].notify(...i)}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eF,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=S.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,m.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=rC(e),this.isVariantNode=rD(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&D(e)&&e.set(a[t],!1)}}}class sk extends sw{sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,e){let{vars:i,style:n}=e;delete i[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;D(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent="".concat(t))}))}constructor(){super(...arguments),this.KeyframeResolver=sm}}function sT(t,e,i,n){let r,{style:s,vars:o}=e,a=t.style;for(r in s)a[r]=s[r];for(r in null==n||n.applyProjectionStyles(a,i),o)a.setProperty(r,o[r])}class sP extends sk{readValueFromInstance(t,e){var i;if(b.has(e))return(null==(i=this.projection)?void 0:i.isProjecting)?ew(e):eT(t,e);{let i=window.getComputedStyle(t),n=(Y(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,e){let{transformPagePoint:i}=e;return i$(t,i)}build(t,e,i){rX(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return r9(t,e,i)}constructor(){super(...arguments),this.type="html",this.renderInstance=sT}}let sS=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sA extends sk{getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(b.has(e)){let t=sd(e);return t&&t.default||0}return e=sS.has(e)?e:j(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return r8(t,e,i)}build(t,e,i){r_(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in sT(t,e,void 0,n),e.attrs)t.setAttribute(sS.has(i)?i:j(i),e.attrs[i])}mount(t){this.isSVGTag=rQ(t.tagName),super.mount(t)}constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iD}}let sM=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy(function(){for(var e=arguments.length,i=Array(e),n=0;n<e;n++)i[n]=arguments[n];return t(...i)},{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((eK={animation:{Feature:id},exit:{Feature:ip},inView:{Feature:rA},tap:{Feature:rx},focus:{Feature:rd},hover:{Feature:rh},pan:{Feature:i6},drag:{Feature:i4,ProjectionNode:ro,MeasureLayout:nh},layout:{ProjectionNode:ro,MeasureLayout:nh}},eq=(t,e)=>r5(t)?new sA(e):new sP(e,{allowProjection:t!==nt.Fragment}),function(t){let{forwardMotionProps:e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{forwardMotionProps:!1};return function(t){var e,i;let{preloadedFeatures:n,createVisualElement:r,useRender:s,useVisualState:o,Component:a}=t;function l(t,e){var i,n,l;let u,h={...(0,nt.useContext)(rE.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,nt.useContext)(ni.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:d}=h,c=function(t){let{initial:e,animate:i}=function(t,e){if(rC(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ie(e)?e:void 0,animate:ie(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,nt.useContext)(rV));return(0,nt.useMemo)(()=>({initial:e,animate:i}),[rR(e),rR(i)])}(t),p=o(t,d);if(!d&&rj.B){n=0,l=0,(0,nt.useContext)(rM).strict;let t=function(t){let{drag:e,layout:i}=rF;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(h);u=t.MeasureLayout,c.visualElement=function(t,e,i,n,r){var s,o,a,l;let{visualElement:u}=(0,nt.useContext)(rV),h=(0,nt.useContext)(rM),d=(0,nt.useContext)(rO.t),c=(0,nt.useContext)(rE.Q).reducedMotion,p=(0,nt.useRef)(null);n=n||h.renderer,!p.current&&n&&(p.current=n(t,{visualState:e,parent:u,props:i,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:c}));let m=p.current,f=(0,nt.useContext)(nn);m&&!m.projection&&r&&("html"===m.type||"svg"===m.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&iX(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(p.current,i,r,f);let g=(0,nt.useRef)(!1);(0,nt.useInsertionEffect)(()=>{m&&g.current&&m.update(i,d)});let v=i[L],y=(0,nt.useRef)(!!v&&!(null==(s=(o=window).MotionHandoffIsComplete)?void 0:s.call(o,v))&&(null==(a=(l=window).MotionHasOptimisedAnimation)?void 0:a.call(l,v)));return(0,rI.E)(()=>{m&&(g.current=!0,window.MotionIsMounted=!0,m.updateFeatures(),i7.render(m.render),y.current&&m.animationState&&m.animationState.animateChanges())}),(0,nt.useEffect)(()=>{m&&(!y.current&&m.animationState&&m.animationState.animateChanges(),y.current&&(queueMicrotask(()=>{var t,e;null==(t=(e=window).MotionHandoffMarkAsComplete)||t.call(e,v)}),y.current=!1))}),m}(a,p,h,r,t.ProjectionNode)}return(0,i8.jsxs)(rV.Provider,{value:c,children:[u&&c.visualElement?(0,i8.jsx)(u,{visualElement:c.visualElement,...h}):null,s(a,t,(i=c.visualElement,(0,nt.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):iX(e)&&(e.current=t))},[i])),p,d,c.visualElement)]})}n&&function(t){for(let e in t)rF[e]={...rF[e],...t[e]}}(n),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(i=null!=(e=a.displayName)?e:a.name)?i:"",")"));let u=(0,nt.forwardRef)(l);return u[rB]=a,u}({...r5(t)?r7:r6,preloadedFeatures:eK,useRender:function(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(e,i,n,r,s)=>{let{latestValues:o}=r,a=(r5(e)?function(t,e,i,n){let r=(0,nt.useMemo)(()=>{let i=rZ();return r_(i,e,rQ(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};rG(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return rG(n,i,t),Object.assign(n,function(t,e){let{transformTemplate:i}=t;return(0,nt.useMemo)(()=>{let t=rH();return rX(t,e,i),Object.assign({},t.vars,t.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":"pan-".concat("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,o,s,e),l=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(r1(r)||!0===i&&r0(r)||!e&&!r0(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(i,"string"==typeof e,t),u=e!==nt.Fragment?{...l,...a,ref:n}:{},{children:h}=i,d=(0,nt.useMemo)(()=>D(h)?h.get():h,[h]);return(0,nt.createElement)(e,{...u,children:d})}}(e),createVisualElement:eq,Component:t})}))},6983:(t,e,i)=>{i.d(e,{G:()=>n});function n(t){return"object"==typeof t&&null!==t}},7351:(t,e,i)=>{i.d(e,{s:()=>r});var n=i(6983);function r(t){return(0,n.G)(t)&&"offsetHeight"in t}},7494:(t,e,i)=>{i.d(e,{E:()=>r});var n=i(2115);let r=i(8972).B?n.useLayoutEffect:n.useEffect},8972:(t,e,i)=>{i.d(e,{B:()=>n});let n="undefined"!=typeof window},9688:(t,e,i)=>{i.d(e,{QP:()=>tu});let n=t=>{let e=a(t),{conflictingClassGroups:i,conflictingClassGroupModifiers:n}=t;return{getClassGroupId:t=>{let i=t.split("-");return""===i[0]&&1!==i.length&&i.shift(),r(i,e)||o(t)},getConflictingClassGroupIds:(t,e)=>{let r=i[t]||[];return e&&n[t]?[...r,...n[t]]:r}}},r=(t,e)=>{if(0===t.length)return e.classGroupId;let i=t[0],n=e.nextPart.get(i),s=n?r(t.slice(1),n):void 0;if(s)return s;if(0===e.validators.length)return;let o=t.join("-");return e.validators.find(({validator:t})=>t(o))?.classGroupId},s=/^\[(.+)\]$/,o=t=>{if(s.test(t)){let e=s.exec(t)[1],i=e?.substring(0,e.indexOf(":"));if(i)return"arbitrary.."+i}},a=t=>{let{theme:e,classGroups:i}=t,n={nextPart:new Map,validators:[]};for(let t in i)l(i[t],n,t,e);return n},l=(t,e,i,n)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:u(e,t)).classGroupId=i;return}if("function"==typeof t)return h(t)?void l(t(n),e,i,n):void e.validators.push({validator:t,classGroupId:i});Object.entries(t).forEach(([t,r])=>{l(r,u(e,t),i,n)})})},u=(t,e)=>{let i=t;return e.split("-").forEach(t=>{i.nextPart.has(t)||i.nextPart.set(t,{nextPart:new Map,validators:[]}),i=i.nextPart.get(t)}),i},h=t=>t.isThemeGetter,d=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,i=new Map,n=new Map,r=(r,s)=>{i.set(r,s),++e>t&&(e=0,n=i,i=new Map)};return{get(t){let e=i.get(t);return void 0!==e?e:void 0!==(e=n.get(t))?(r(t,e),e):void 0},set(t,e){i.has(t)?i.set(t,e):r(t,e)}}},c=t=>{let{prefix:e,experimentalParseClassName:i}=t,n=t=>{let e,i=[],n=0,r=0,s=0;for(let o=0;o<t.length;o++){let a=t[o];if(0===n&&0===r){if(":"===a){i.push(t.slice(s,o)),s=o+1;continue}if("/"===a){e=o;continue}}"["===a?n++:"]"===a?n--:"("===a?r++:")"===a&&r--}let o=0===i.length?t:t.substring(s),a=p(o);return{modifiers:i,hasImportantModifier:a!==o,baseClassName:a,maybePostfixModifierPosition:e&&e>s?e-s:void 0}};if(e){let t=e+":",i=n;n=e=>e.startsWith(t)?i(e.substring(t.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:e,maybePostfixModifierPosition:void 0}}if(i){let t=n;n=e=>i({className:e,parseClassName:t})}return n},p=t=>t.endsWith("!")?t.substring(0,t.length-1):t.startsWith("!")?t.substring(1):t,m=t=>{let e=Object.fromEntries(t.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;let i=[],n=[];return t.forEach(t=>{"["===t[0]||e[t]?(i.push(...n.sort(),t),n=[]):n.push(t)}),i.push(...n.sort()),i}},f=t=>({cache:d(t.cacheSize),parseClassName:c(t),sortModifiers:m(t),...n(t)}),g=/\s+/,v=(t,e)=>{let{parseClassName:i,getClassGroupId:n,getConflictingClassGroupIds:r,sortModifiers:s}=e,o=[],a=t.trim().split(g),l="";for(let t=a.length-1;t>=0;t-=1){let e=a[t],{isExternal:u,modifiers:h,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:p}=i(e);if(u){l=e+(l.length>0?" "+l:l);continue}let m=!!p,f=n(m?c.substring(0,p):c);if(!f){if(!m||!(f=n(c))){l=e+(l.length>0?" "+l:l);continue}m=!1}let g=s(h).join(":"),v=d?g+"!":g,y=v+f;if(o.includes(y))continue;o.push(y);let b=r(f,m);for(let t=0;t<b.length;++t){let e=b[t];o.push(v+e)}l=e+(l.length>0?" "+l:l)}return l};function y(){let t,e,i=0,n="";for(;i<arguments.length;)(t=arguments[i++])&&(e=b(t))&&(n&&(n+=" "),n+=e);return n}let b=t=>{let e;if("string"==typeof t)return t;let i="";for(let n=0;n<t.length;n++)t[n]&&(e=b(t[n]))&&(i&&(i+=" "),i+=e);return i},x=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},w=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,T=/^\d+\/\d+$/,P=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,S=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,A=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,E=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,V=t=>T.test(t),C=t=>!!t&&!Number.isNaN(Number(t)),D=t=>!!t&&Number.isInteger(Number(t)),R=t=>t.endsWith("%")&&C(t.slice(0,-1)),j=t=>P.test(t),L=()=>!0,F=t=>S.test(t)&&!A.test(t),B=()=>!1,O=t=>M.test(t),I=t=>E.test(t),z=t=>!N(t)&&!G(t),U=t=>tt(t,tr,B),N=t=>w.test(t),W=t=>tt(t,ts,F),$=t=>tt(t,to,C),Y=t=>tt(t,ti,B),X=t=>tt(t,tn,I),H=t=>tt(t,tl,O),G=t=>k.test(t),K=t=>te(t,ts),q=t=>te(t,ta),_=t=>te(t,ti),Z=t=>te(t,tr),Q=t=>te(t,tn),J=t=>te(t,tl,!0),tt=(t,e,i)=>{let n=w.exec(t);return!!n&&(n[1]?e(n[1]):i(n[2]))},te=(t,e,i=!1)=>{let n=k.exec(t);return!!n&&(n[1]?e(n[1]):i)},ti=t=>"position"===t||"percentage"===t,tn=t=>"image"===t||"url"===t,tr=t=>"length"===t||"size"===t||"bg-size"===t,ts=t=>"length"===t,to=t=>"number"===t,ta=t=>"family-name"===t,tl=t=>"shadow"===t;Symbol.toStringTag;let tu=function(t,...e){let i,n,r,s=function(a){return n=(i=f(e.reduce((t,e)=>e(t),t()))).cache.get,r=i.cache.set,s=o,o(a)};function o(t){let e=n(t);if(e)return e;let s=v(t,i);return r(t,s),s}return function(){return s(y.apply(null,arguments))}}(()=>{let t=x("color"),e=x("font"),i=x("text"),n=x("font-weight"),r=x("tracking"),s=x("leading"),o=x("breakpoint"),a=x("container"),l=x("spacing"),u=x("radius"),h=x("shadow"),d=x("inset-shadow"),c=x("text-shadow"),p=x("drop-shadow"),m=x("blur"),f=x("perspective"),g=x("aspect"),v=x("ease"),y=x("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],w=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...w(),G,N],T=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto","contain","none"],S=()=>[G,N,l],A=()=>[V,"full","auto",...S()],M=()=>[D,"none","subgrid",G,N],E=()=>["auto",{span:["full",D,G,N]},D,G,N],F=()=>[D,"auto",G,N],B=()=>["auto","min","max","fr",G,N],O=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],I=()=>["start","end","center","stretch","center-safe","end-safe"],tt=()=>["auto",...S()],te=()=>[V,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...S()],ti=()=>[t,G,N],tn=()=>[...w(),_,Y,{position:[G,N]}],tr=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ts=()=>["auto","cover","contain",Z,U,{size:[G,N]}],to=()=>[R,K,W],ta=()=>["","none","full",u,G,N],tl=()=>["",C,K,W],tu=()=>["solid","dashed","dotted","double"],th=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],td=()=>[C,R,_,Y],tc=()=>["","none",m,G,N],tp=()=>["none",C,G,N],tm=()=>["none",C,G,N],tf=()=>[C,G,N],tg=()=>[V,"full",...S()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[j],breakpoint:[j],color:[L],container:[j],"drop-shadow":[j],ease:["in","out","in-out"],font:[z],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[j],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[j],shadow:[j],spacing:["px",C],text:[j],"text-shadow":[j],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",V,N,G,g]}],container:["container"],columns:[{columns:[C,N,G,a]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:T()}],"overflow-x":[{"overflow-x":T()}],"overflow-y":[{"overflow-y":T()}],overscroll:[{overscroll:P()}],"overscroll-x":[{"overscroll-x":P()}],"overscroll-y":[{"overscroll-y":P()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:A()}],"inset-x":[{"inset-x":A()}],"inset-y":[{"inset-y":A()}],start:[{start:A()}],end:[{end:A()}],top:[{top:A()}],right:[{right:A()}],bottom:[{bottom:A()}],left:[{left:A()}],visibility:["visible","invisible","collapse"],z:[{z:[D,"auto",G,N]}],basis:[{basis:[V,"full","auto",a,...S()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[C,V,"auto","initial","none",N]}],grow:[{grow:["",C,G,N]}],shrink:[{shrink:["",C,G,N]}],order:[{order:[D,"first","last","none",G,N]}],"grid-cols":[{"grid-cols":M()}],"col-start-end":[{col:E()}],"col-start":[{"col-start":F()}],"col-end":[{"col-end":F()}],"grid-rows":[{"grid-rows":M()}],"row-start-end":[{row:E()}],"row-start":[{"row-start":F()}],"row-end":[{"row-end":F()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":B()}],"auto-rows":[{"auto-rows":B()}],gap:[{gap:S()}],"gap-x":[{"gap-x":S()}],"gap-y":[{"gap-y":S()}],"justify-content":[{justify:[...O(),"normal"]}],"justify-items":[{"justify-items":[...I(),"normal"]}],"justify-self":[{"justify-self":["auto",...I()]}],"align-content":[{content:["normal",...O()]}],"align-items":[{items:[...I(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...I(),{baseline:["","last"]}]}],"place-content":[{"place-content":O()}],"place-items":[{"place-items":[...I(),"baseline"]}],"place-self":[{"place-self":["auto",...I()]}],p:[{p:S()}],px:[{px:S()}],py:[{py:S()}],ps:[{ps:S()}],pe:[{pe:S()}],pt:[{pt:S()}],pr:[{pr:S()}],pb:[{pb:S()}],pl:[{pl:S()}],m:[{m:tt()}],mx:[{mx:tt()}],my:[{my:tt()}],ms:[{ms:tt()}],me:[{me:tt()}],mt:[{mt:tt()}],mr:[{mr:tt()}],mb:[{mb:tt()}],ml:[{ml:tt()}],"space-x":[{"space-x":S()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":S()}],"space-y-reverse":["space-y-reverse"],size:[{size:te()}],w:[{w:[a,"screen",...te()]}],"min-w":[{"min-w":[a,"screen","none",...te()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[o]},...te()]}],h:[{h:["screen","lh",...te()]}],"min-h":[{"min-h":["screen","lh","none",...te()]}],"max-h":[{"max-h":["screen","lh",...te()]}],"font-size":[{text:["base",i,K,W]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,G,$]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",R,N]}],"font-family":[{font:[q,N,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[r,G,N]}],"line-clamp":[{"line-clamp":[C,"none",G,$]}],leading:[{leading:[s,...S()]}],"list-image":[{"list-image":["none",G,N]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",G,N]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ti()}],"text-color":[{text:ti()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...tu(),"wavy"]}],"text-decoration-thickness":[{decoration:[C,"from-font","auto",G,W]}],"text-decoration-color":[{decoration:ti()}],"underline-offset":[{"underline-offset":[C,"auto",G,N]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:S()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",G,N]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",G,N]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:tn()}],"bg-repeat":[{bg:tr()}],"bg-size":[{bg:ts()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},D,G,N],radial:["",G,N],conic:[D,G,N]},Q,X]}],"bg-color":[{bg:ti()}],"gradient-from-pos":[{from:to()}],"gradient-via-pos":[{via:to()}],"gradient-to-pos":[{to:to()}],"gradient-from":[{from:ti()}],"gradient-via":[{via:ti()}],"gradient-to":[{to:ti()}],rounded:[{rounded:ta()}],"rounded-s":[{"rounded-s":ta()}],"rounded-e":[{"rounded-e":ta()}],"rounded-t":[{"rounded-t":ta()}],"rounded-r":[{"rounded-r":ta()}],"rounded-b":[{"rounded-b":ta()}],"rounded-l":[{"rounded-l":ta()}],"rounded-ss":[{"rounded-ss":ta()}],"rounded-se":[{"rounded-se":ta()}],"rounded-ee":[{"rounded-ee":ta()}],"rounded-es":[{"rounded-es":ta()}],"rounded-tl":[{"rounded-tl":ta()}],"rounded-tr":[{"rounded-tr":ta()}],"rounded-br":[{"rounded-br":ta()}],"rounded-bl":[{"rounded-bl":ta()}],"border-w":[{border:tl()}],"border-w-x":[{"border-x":tl()}],"border-w-y":[{"border-y":tl()}],"border-w-s":[{"border-s":tl()}],"border-w-e":[{"border-e":tl()}],"border-w-t":[{"border-t":tl()}],"border-w-r":[{"border-r":tl()}],"border-w-b":[{"border-b":tl()}],"border-w-l":[{"border-l":tl()}],"divide-x":[{"divide-x":tl()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":tl()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...tu(),"hidden","none"]}],"divide-style":[{divide:[...tu(),"hidden","none"]}],"border-color":[{border:ti()}],"border-color-x":[{"border-x":ti()}],"border-color-y":[{"border-y":ti()}],"border-color-s":[{"border-s":ti()}],"border-color-e":[{"border-e":ti()}],"border-color-t":[{"border-t":ti()}],"border-color-r":[{"border-r":ti()}],"border-color-b":[{"border-b":ti()}],"border-color-l":[{"border-l":ti()}],"divide-color":[{divide:ti()}],"outline-style":[{outline:[...tu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[C,G,N]}],"outline-w":[{outline:["",C,K,W]}],"outline-color":[{outline:ti()}],shadow:[{shadow:["","none",h,J,H]}],"shadow-color":[{shadow:ti()}],"inset-shadow":[{"inset-shadow":["none",d,J,H]}],"inset-shadow-color":[{"inset-shadow":ti()}],"ring-w":[{ring:tl()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ti()}],"ring-offset-w":[{"ring-offset":[C,W]}],"ring-offset-color":[{"ring-offset":ti()}],"inset-ring-w":[{"inset-ring":tl()}],"inset-ring-color":[{"inset-ring":ti()}],"text-shadow":[{"text-shadow":["none",c,J,H]}],"text-shadow-color":[{"text-shadow":ti()}],opacity:[{opacity:[C,G,N]}],"mix-blend":[{"mix-blend":[...th(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":th()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[C]}],"mask-image-linear-from-pos":[{"mask-linear-from":td()}],"mask-image-linear-to-pos":[{"mask-linear-to":td()}],"mask-image-linear-from-color":[{"mask-linear-from":ti()}],"mask-image-linear-to-color":[{"mask-linear-to":ti()}],"mask-image-t-from-pos":[{"mask-t-from":td()}],"mask-image-t-to-pos":[{"mask-t-to":td()}],"mask-image-t-from-color":[{"mask-t-from":ti()}],"mask-image-t-to-color":[{"mask-t-to":ti()}],"mask-image-r-from-pos":[{"mask-r-from":td()}],"mask-image-r-to-pos":[{"mask-r-to":td()}],"mask-image-r-from-color":[{"mask-r-from":ti()}],"mask-image-r-to-color":[{"mask-r-to":ti()}],"mask-image-b-from-pos":[{"mask-b-from":td()}],"mask-image-b-to-pos":[{"mask-b-to":td()}],"mask-image-b-from-color":[{"mask-b-from":ti()}],"mask-image-b-to-color":[{"mask-b-to":ti()}],"mask-image-l-from-pos":[{"mask-l-from":td()}],"mask-image-l-to-pos":[{"mask-l-to":td()}],"mask-image-l-from-color":[{"mask-l-from":ti()}],"mask-image-l-to-color":[{"mask-l-to":ti()}],"mask-image-x-from-pos":[{"mask-x-from":td()}],"mask-image-x-to-pos":[{"mask-x-to":td()}],"mask-image-x-from-color":[{"mask-x-from":ti()}],"mask-image-x-to-color":[{"mask-x-to":ti()}],"mask-image-y-from-pos":[{"mask-y-from":td()}],"mask-image-y-to-pos":[{"mask-y-to":td()}],"mask-image-y-from-color":[{"mask-y-from":ti()}],"mask-image-y-to-color":[{"mask-y-to":ti()}],"mask-image-radial":[{"mask-radial":[G,N]}],"mask-image-radial-from-pos":[{"mask-radial-from":td()}],"mask-image-radial-to-pos":[{"mask-radial-to":td()}],"mask-image-radial-from-color":[{"mask-radial-from":ti()}],"mask-image-radial-to-color":[{"mask-radial-to":ti()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":w()}],"mask-image-conic-pos":[{"mask-conic":[C]}],"mask-image-conic-from-pos":[{"mask-conic-from":td()}],"mask-image-conic-to-pos":[{"mask-conic-to":td()}],"mask-image-conic-from-color":[{"mask-conic-from":ti()}],"mask-image-conic-to-color":[{"mask-conic-to":ti()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:tn()}],"mask-repeat":[{mask:tr()}],"mask-size":[{mask:ts()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",G,N]}],filter:[{filter:["","none",G,N]}],blur:[{blur:tc()}],brightness:[{brightness:[C,G,N]}],contrast:[{contrast:[C,G,N]}],"drop-shadow":[{"drop-shadow":["","none",p,J,H]}],"drop-shadow-color":[{"drop-shadow":ti()}],grayscale:[{grayscale:["",C,G,N]}],"hue-rotate":[{"hue-rotate":[C,G,N]}],invert:[{invert:["",C,G,N]}],saturate:[{saturate:[C,G,N]}],sepia:[{sepia:["",C,G,N]}],"backdrop-filter":[{"backdrop-filter":["","none",G,N]}],"backdrop-blur":[{"backdrop-blur":tc()}],"backdrop-brightness":[{"backdrop-brightness":[C,G,N]}],"backdrop-contrast":[{"backdrop-contrast":[C,G,N]}],"backdrop-grayscale":[{"backdrop-grayscale":["",C,G,N]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[C,G,N]}],"backdrop-invert":[{"backdrop-invert":["",C,G,N]}],"backdrop-opacity":[{"backdrop-opacity":[C,G,N]}],"backdrop-saturate":[{"backdrop-saturate":[C,G,N]}],"backdrop-sepia":[{"backdrop-sepia":["",C,G,N]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":S()}],"border-spacing-x":[{"border-spacing-x":S()}],"border-spacing-y":[{"border-spacing-y":S()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",G,N]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[C,"initial",G,N]}],ease:[{ease:["linear","initial",v,G,N]}],delay:[{delay:[C,G,N]}],animate:[{animate:["none",y,G,N]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,G,N]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:tp()}],"rotate-x":[{"rotate-x":tp()}],"rotate-y":[{"rotate-y":tp()}],"rotate-z":[{"rotate-z":tp()}],scale:[{scale:tm()}],"scale-x":[{"scale-x":tm()}],"scale-y":[{"scale-y":tm()}],"scale-z":[{"scale-z":tm()}],"scale-3d":["scale-3d"],skew:[{skew:tf()}],"skew-x":[{"skew-x":tf()}],"skew-y":[{"skew-y":tf()}],transform:[{transform:[G,N,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:tg()}],"translate-x":[{"translate-x":tg()}],"translate-y":[{"translate-y":tg()}],"translate-z":[{"translate-z":tg()}],"translate-none":["translate-none"],accent:[{accent:ti()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ti()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",G,N]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":S()}],"scroll-mx":[{"scroll-mx":S()}],"scroll-my":[{"scroll-my":S()}],"scroll-ms":[{"scroll-ms":S()}],"scroll-me":[{"scroll-me":S()}],"scroll-mt":[{"scroll-mt":S()}],"scroll-mr":[{"scroll-mr":S()}],"scroll-mb":[{"scroll-mb":S()}],"scroll-ml":[{"scroll-ml":S()}],"scroll-p":[{"scroll-p":S()}],"scroll-px":[{"scroll-px":S()}],"scroll-py":[{"scroll-py":S()}],"scroll-ps":[{"scroll-ps":S()}],"scroll-pe":[{"scroll-pe":S()}],"scroll-pt":[{"scroll-pt":S()}],"scroll-pr":[{"scroll-pr":S()}],"scroll-pb":[{"scroll-pb":S()}],"scroll-pl":[{"scroll-pl":S()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",G,N]}],fill:[{fill:["none",...ti()]}],"stroke-w":[{stroke:[C,K,W,$]}],stroke:[{stroke:["none",...ti()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9946:(t,e,i)=>{i.d(e,{A:()=>d});var n=i(2115);let r=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,i)=>i?i.toUpperCase():e.toLowerCase()),o=t=>{let e=s(t);return e.charAt(0).toUpperCase()+e.slice(1)},a=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim()},l=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,n.forwardRef)((t,e)=>{let{color:i="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:h="",children:d,iconNode:c,...p}=t;return(0,n.createElement)("svg",{ref:e,...u,width:r,height:r,stroke:i,strokeWidth:o?24*Number(s)/Number(r):s,className:a("lucide",h),...!d&&!l(p)&&{"aria-hidden":"true"},...p},[...c.map(t=>{let[e,i]=t;return(0,n.createElement)(e,i)}),...Array.isArray(d)?d:[d]])}),d=(t,e)=>{let i=(0,n.forwardRef)((i,s)=>{let{className:l,...u}=i;return(0,n.createElement)(h,{ref:s,iconNode:e,className:a("lucide-".concat(r(o(t))),"lucide-".concat(t),l),...u})});return i.displayName=o(t),i}}}]);